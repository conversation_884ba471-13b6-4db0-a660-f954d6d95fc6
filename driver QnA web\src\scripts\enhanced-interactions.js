/**
 * Enhanced interactions for FAQ content display
 * Provides smooth animations, interactive elements, and accessibility features
 */

class EnhancedInteractions {
    constructor() {
        this.isInitialized = false;
        this.config = {
            animationDuration: 600,
            easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
            enableAnimations: true,
            enableTooltips: true,
            enableKeyboardNav: true
        };
        
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        this.setupAnimations();
        this.setupInteractions();
        this.setupKeyboardNavigation();
        this.setupAccessibility();
        
        this.isInitialized = true;
        console.log('Enhanced interactions initialized');
    }

    /**
     * Setup entrance animations for content boxes
     */
    setupAnimations() {
        if (!this.config.enableAnimations) return;

        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElement(entry.target);
                }
            });
        }, observerOptions);

        // Observe all enhanced content boxes
        const boxes = document.querySelectorAll('.info-box, .procedure-box, .warning-box, .tip-box, .success-box, .error-box');
        boxes.forEach(box => observer.observe(box));
    }

    /**
     * Animate element entrance
     */
    animateElement(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        requestAnimationFrame(() => {
            element.style.transition = `opacity ${this.config.animationDuration}ms ${this.config.easing}, transform ${this.config.animationDuration}ms ${this.config.easing}`;
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        });
    }

    /**
     * Setup interactive elements
     */
    setupInteractions() {
        // Hover effects for content boxes
        const boxes = document.querySelectorAll('.info-box, .procedure-box, .warning-box, .tip-box, .success-box, .error-box');
        
        boxes.forEach(box => {
            box.addEventListener('mouseenter', () => this.onBoxHover(box));
            box.addEventListener('mouseleave', () => this.onBoxLeave(box));
            box.addEventListener('click', () => this.onBoxClick(box));
        });

        // Click-to-copy for phone numbers and emails
        this.setupClickToCopy();
        
        // Expandable content
        this.setupExpandableContent();
        
        // Smooth scroll for internal links
        this.setupSmoothScroll();
    }

    /**
     * Handle box hover effects
     */
    onBoxHover(box) {
        if (!this.config.enableAnimations) return;
        
        box.style.transform = 'translateY(-2px)';
        box.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.15)';
    }

    /**
     * Handle box leave effects
     */
    onBoxLeave(box) {
        if (!this.config.enableAnimations) return;
        
        box.style.transform = 'translateY(0)';
        box.style.boxShadow = '';
    }

    /**
     * Handle box click effects
     */
    onBoxClick(box) {
        // Add click ripple effect
        this.createRippleEffect(box);
        
        // Optional: Copy content to clipboard
        if (box.classList.contains('copy-on-click')) {
            this.copyToClipboard(box);
        }
    }

    /**
     * Create ripple effect on click
     */
    createRippleEffect(element) {
        const ripple = document.createElement('span');
        ripple.classList.add('ripple-effect');
        
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
            z-index: 1000;
        `;
        
        element.style.position = 'relative';
        element.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }

    /**
     * Setup click-to-copy functionality
     */
    setupClickToCopy() {
        // Phone numbers
        document.querySelectorAll('[data-phone]').forEach(element => {
            element.addEventListener('click', () => {
                this.copyToClipboard(element.dataset.phone);
                this.showToast(`电话 ${element.dataset.phone} 已复制到剪贴板`);
            });
        });

        // Email addresses
        document.querySelectorAll('[data-email]').forEach(element => {
            element.addEventListener('click', () => {
                this.copyToClipboard(element.dataset.email);
                this.showToast(`邮箱 ${element.dataset.email} 已复制到剪贴板`);
            });
        });
    }

    /**
     * Setup expandable content
     */
    setupExpandableContent() {
        document.querySelectorAll('.expandable-content').forEach(element => {
            const toggle = element.querySelector('.expand-toggle');
            const content = element.querySelector('.expand-content');
            
            if (toggle && content) {
                toggle.addEventListener('click', () => this.toggleExpandable(element, content));
            }
        });
    }

    /**
     * Toggle expandable content
     */
    toggleExpandable(element, content) {
        const isExpanded = element.classList.contains('expanded');
        
        if (isExpanded) {
            content.style.maxHeight = '0';
            element.classList.remove('expanded');
        } else {
            content.style.maxHeight = content.scrollHeight + 'px';
            element.classList.add('expanded');
        }
    }

    /**
     * Setup smooth scroll
     */
    setupSmoothScroll() {
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Setup keyboard navigation
     */
    setupKeyboardNavigation() {
        if (!this.config.enableKeyboardNav) return;

        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'Tab':
                    this.handleTabNavigation(e);
                    break;
                case 'Enter':
                case ' ':
                    this.handleKeyboardActivation(e);
                    break;
                case 'Escape':
                    this.handleEscapeKey(e);
                    break;
            }
        });
    }

    /**
     * Handle tab navigation
     */
    handleTabNavigation(event) {
        const focusableElements = document.querySelectorAll(
            '.info-box, .procedure-box, .warning-box, .tip-box, .success-box, .error-box, [data-phone], [data-email]'
        );
        
        const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);
        
        if (currentIndex !== -1) {
            const nextIndex = event.shiftKey ? 
                (currentIndex - 1 + focusableElements.length) % focusableElements.length :
                (currentIndex + 1) % focusableElements.length;
            
            focusableElements[nextIndex].focus();
            event.preventDefault();
        }
    }

    /**
     * Handle keyboard activation
     */
    handleKeyboardActivation(event) {
        const target = event.target;
        
        if (target.matches('.info-box, .procedure-box, .warning-box, .tip-box, .success-box, .error-box')) {
            this.onBoxClick(target);
            event.preventDefault();
        }
    }

    /**
     * Handle escape key
     */
    handleEscapeKey(event) {
        // Close any open modals or dropdowns
        const openModals = document.querySelectorAll('.modal.open, .dropdown.open');
        openModals.forEach(modal => modal.classList.remove('open'));
    }

    /**
     * Setup accessibility features
     */
    setupAccessibility() {
        // Add ARIA labels
        document.querySelectorAll('.info-box, .procedure-box, .warning-box, .tip-box, .success-box, .error-box').forEach((box, index) => {
            box.setAttribute('tabindex', '0');
            box.setAttribute('role', 'article');
            box.setAttribute('aria-label', `信息内容 ${index + 1}`);
        });

        // Add focus styles
        document.addEventListener('focusin', (e) => {
            if (e.target.matches('.info-box, .procedure-box, .warning-box, .tip-box, .success-box, .error-box')) {
                e.target.style.outline = '2px solid var(--primary-500)';
                e.target.style.outlineOffset = '2px';
            }
        });

        document.addEventListener('focusout', (e) => {
            if (e.target.matches('.info-box, .procedure-box, .warning-box, .tip-box, .success-box, .error-box')) {
                e.target.style.outline = '';
                e.target.style.outlineOffset = '';
            }
        });

        // Screen reader announcements
        this.setupScreenReaderSupport();
    }

    /**
     * Setup screen reader support
     */
    setupScreenReaderSupport() {
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(announcer);
        
        this.screenReaderAnnouncer = announcer;
    }

    /**
     * Copy text to clipboard
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            console.error('Failed to copy to clipboard:', err);
            return false;
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'enhanced-toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-500);
            color: white;
            padding: 12px 24px;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-size: var(--font-sm);
            max-width: 300px;
        `;

        document.body.appendChild(toast);
        
        // Animate in
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0)';
        });

        // Auto remove
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    /**
     * Destroy all enhancements
     */
    destroy() {
        // Remove event listeners
        document.removeEventListener('keydown', this.handleKeyboardActivation);
        document.removeEventListener('focusin', this.handleFocusIn);
        document.removeEventListener('focusout', this.handleFocusOut);
        
        // Remove any added elements
        const toasts = document.querySelectorAll('.enhanced-toast');
        toasts.forEach(toast => toast.remove());
        
        this.isInitialized = false;
    }
}

// Add CSS for interactions
const interactionStyles = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .enhanced-toast {
        animation: slideInRight 0.3s ease;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .expandable-content {
        transition: max-height 0.3s ease;
        overflow: hidden;
    }

    .expand-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .expanded .expand-content {
        max-height: 1000px;
    }

    .expand-toggle {
        cursor: pointer;
        color: var(--primary-500);
        text-decoration: underline;
    }

    .expand-toggle:hover {
        color: var(--primary-600);
    }
`;

// Inject styles
const styleSheet = document.createElement('style');
styleSheet.textContent = interactionStyles;
document.head.appendChild(styleSheet);

// Initialize on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new EnhancedInteractions();
    });
} else {
    new EnhancedInteractions();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedInteractions;
} else {
    window.EnhancedInteractions = EnhancedInteractions;
}