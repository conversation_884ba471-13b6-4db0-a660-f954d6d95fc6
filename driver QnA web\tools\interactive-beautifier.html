<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ内容交互式美化工具 - GoMyHire</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }

        .panel {
            padding: 30px;
            overflow-y: auto;
            max-height: 700px;
        }

        .panel-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .panel-header h2 {
            color: #333;
            font-size: 1.5rem;
        }

        .control-panel {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .control-group {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .textarea {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
        }

        .preview-area {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: white;
            min-height: 400px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .language-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .tab {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            background: white;
            transition: all 0.3s ease;
        }

        .tab.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .panel {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 FAQ内容交互式美化工具</h1>
            <p>实时预览FAQ内容美化效果，支持多语言批量处理</p>
        </div>

        <div class="control-panel">
            <div class="control-group">
                <button class="btn btn-primary" onclick="loadSampleData()">📂 加载示例数据</button>
                <button class="btn btn-secondary" onclick="beautifyContent()">✨ 美化内容</button>
                <button class="btn btn-success" onclick="exportResult()">💾 导出结果</button>
                <button class="btn btn-secondary" onclick="clearAll()">🗑️ 清空</button>
            </div>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-count">0</div>
                <div class="stat-label">总问题数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="enhanced-count">0</div>
                <div class="stat-label">已美化</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="skipped-count">0</div>
                <div class="stat-label">已跳过</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="languages-count">0</div>
                <div class="stat-label">支持语言</div>
            </div>
        </div>

        <div class="main-content">
            <div class="panel">
                <div class="panel-header">
                    <h2>📝 原始内容</h2>
                </div>
                <div class="language-tabs" id="source-tabs">
                    <div class="tab active" onclick="switchTab('source', 'zh')">中文</div>
                    <div class="tab" onclick="switchTab('source', 'en')">English</div>
                    <div class="tab" onclick="switchTab('source', 'ms')">Melayu</div>
                </div>
                <textarea class="textarea" id="source-content" placeholder="粘贴FAQ内容到此处，或点击上方按钮加载示例数据...></textarea>
            </div>

            <div class="panel">
                <div class="panel-header">
                    <h2>🎨 美化预览</h2>
                </div>
                <div class="language-tabs" id="preview-tabs">
                    <div class="tab active" onclick="switchTab('preview', 'zh')">中文</div>
                    <div class="tab" onclick="switchTab('preview', 'en')">English</div>
                    <div class="tab" onclick="switchTab('preview', 'ms')">Melayu</div>
                </div>
                <div class="preview-area" id="preview-content">
                    <div style="text-align: center; color: #999; padding: 40px;">
                        🎨 美化后的内容将在这里显示
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // 动态导入美化工具
        import FAQBeautifier from './beautify-faq-content.js';

        let beautifier = new FAQBeautifier();
        let currentData = null;
        let currentLanguage = 'zh';

        // 示例数据
        const sampleData = {
            zh: `订单状态更新时间要求

重要提醒：
- 15分钟"已到达"规则：必须在预定接送时间前至少15分钟点击GMH中的"已到达"按钮
- 违规后果：系统将自动取消订单
- 客服电话：+60162234711

最佳实践：
1. 提前20分钟到达
2. 保持手机畅通
3. 及时更新状态`,
            
            en: `Order Status Update Time Requirements

Important Reminders:
- 15-minute "Arrived" rule: Must click "Arrived" in GMH at least 15 minutes before scheduled pickup time
- Consequences: System will automatically cancel the order
- Customer service: +60162234711

Best Practices:
1. Arrive 20 minutes early
2. Keep phone accessible
3. Update status promptly`,

            ms: `Keperluan Masa Kemas Kini Status Pesanan

Peringatan Penting:
- Peraturan 15 minit "Tiba": Mesti klik "Tiba" dalam GMH sekurang-kurangnya 15 minit sebelum masa pickup yang dijadualkan
- Akibat: Sistem akan membatalkan pesanan secara automatik
- Khidmat pelanggan: +60162234711

Amalan Terbaik:
1. Tiba 20 minit awal
2. Pastikan telefon mudah dicapai
3. Kemas kini status dengan segera`
        };

        // 加载示例数据
        window.loadSampleData = function() {
            try {
                const data = sampleData[currentLanguage];
                document.getElementById('source-content').value = data;
                currentData = { [currentLanguage]: data };
                updateStats();
                showMessage('✅ 示例数据已加载', 'success');
            } catch (error) {
                showMessage('❌ 加载失败: ' + error.message, 'error');
            }
        };

        // 美化内容
        window.beautifyContent = function() {
            try {
                const sourceText = document.getElementById('source-content').value;
                if (!sourceText.trim()) {
                    showMessage('⚠️ 请先输入内容', 'error');
                    return;
                }

                showLoading('preview-content', '正在美化内容...');
                
                setTimeout(() => {
                    const content = { [currentLanguage]: sourceText };
                    const beautified = beautifier.beautifyContent(content, currentLanguage);
                    
                    document.getElementById('preview-content').innerHTML = beautified[currentLanguage];
                    updateStats();
                    showMessage('✨ 内容美化完成！', 'success');
                }, 500);

            } catch (error) {
                showMessage('❌ 美化失败: ' + error.message, 'error');
            }
        };

        // 切换标签
        window.switchTab = function(panel, lang) {
            currentLanguage = lang;
            
            // 更新标签状态
            document.querySelectorAll(`#${panel}-tabs .tab`).forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容
            if (panel === 'source' && currentData) {
                document.getElementById('source-content').value = 
                    currentData[lang] || currentData[currentLanguage] || '';
            }
        };

        // 导出结果
        window.exportResult = function() {
            try {
                const preview = document.getElementById('preview-content').innerHTML;
                if (!preview.trim() || preview.includes('美化后的内容将在这里显示')) {
                    showMessage('⚠️ 没有可导出的内容', 'error');
                    return;
                }

                // 创建下载链接
                const blob = new Blob([preview], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `faq-beautified-${currentLanguage}-${Date.now()}.html`;
                a.click();
                URL.revokeObjectURL(url);
                
                showMessage('💾 导出成功！', 'success');
            } catch (error) {
                showMessage('❌ 导出失败: ' + error.message, 'error');
            }
        };

        // 清空所有
        window.clearAll = function() {
            document.getElementById('source-content').value = '';
            document.getElementById('preview-content').innerHTML = 
                '<div style="text-align: center; color: #999; padding: 40px;">🎨 美化后的内容将在这里显示</div>';
            currentData = null;
            updateStats();
            showMessage('🗑️ 已清空', 'success');
        };

        // 更新统计
        function updateStats() {
            const stats = {
                total: currentData ? Object.keys(currentData).reduce((sum, lang) => 
                    sum + (currentData[lang] ? 1 : 0), 0) : 0,
                enhanced: currentData ? Object.keys(currentData).length : 0,
                skipped: 0,
                languages: currentData ? Object.keys(currentData).length : 0
            };

            document.getElementById('total-count').textContent = stats.total;
            document.getElementById('enhanced-count').textContent = stats.enhanced;
            document.getElementById('skipped-count').textContent = stats.skipped;
            document.getElementById('languages-count').textContent = stats.languages;
        }

        // 显示消息
        function showMessage(text, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = text;
            messageDiv.style.position = 'fixed';
            messageDiv.style.top = '20px';
            messageDiv.style.right = '20px';
            messageDiv.style.zIndex = '1000';
            messageDiv.style.maxWidth = '300px';
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 显示加载
        function showLoading(elementId, text) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="loading">${text}</div>`;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎨 FAQ交互式美化工具已加载');
            updateStats();
        });

        // 自动美化功能
        document.getElementById('source-content').addEventListener('input', 
            debounce(() => {
                if (document.getElementById('source-content').value.trim()) {
                    beautifyContent();
                }
            }, 1000)
        );

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>