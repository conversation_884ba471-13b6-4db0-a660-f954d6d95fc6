/*
 * 文件路径: src/styles/layout/header.css
 * 文件描述: 定义了应用程序主头部组件的样式和布局。它实现了一个固定的、两层的头部结构，包含Logo区域和搜索/功能区域，旨在通过玻璃拟态效果和渐变实现响应式和视觉吸引力。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于在以下文件中定义CSS变量（设计令牌）：
 *     - `src/styles/tokens/colors.css` (`--text-inverse`, `rgba(0, 0, 0, 0.2)`)
 *     - `src/styles/tokens/spacing.css` (`--space-sm`, `--space-lg`, `--header-height`)
 *     - `src/styles/tokens/radius.css` (`--radius`)
 *     - `src/styles/tokens/shadows.css` (`--shadow-lg`, `--shadow-xl`)
 *     - `src/styles/tokens/transitions.css` (`--transition-all`, `--transition-transform`)
 *     - `src/styles/tokens/typography.css` (`--font-xl`, `--font-lg`, `--font-weight-bold`)
 *     - `src/styles/themes/variables.css` (`--gradient-header`)
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 定义应用程序头部的视觉外观和结构布局。
 *   - 确保头部固定在视口顶部，提供一致的导航和对关键功能的访问。
 *   - 应用玻璃拟态和渐变等现代UI效果，增强美感。
 *   - 为不同屏幕尺寸提供响应式调整，确保在移动设备上的可用性。
 *   - 包含对减少动画和高对比度模式的可访问性考虑。
 * 关键部分/规则:
 *   - `.header`: 头部的主要容器。
 *     - `position: fixed; top: 0; left: 0; right: 0;`: 确保其固定在视口顶部。
 *     - `background: var(--gradient-header);`: 应用渐变背景。
 *     - `-webkit-backdrop-filter: blur(20px); backdrop-filter: blur(20px);`: 实现玻璃拟态模糊效果。
 *     - `color: var(--text-inverse);`: 设置文本颜色以与头部背景形成对比。
 *     - `z-index: 1000;`: 确保头部位于其他内容之上。
 *     - `box-shadow: var(--shadow-lg);`: 应用微妙的阴影。
 *     - `height: var(--header-height);`: 使用设计令牌保持一致的高度。
 *     - `display: flex; flex-direction: column;`: 设置为其两个内部层的flex容器。
 *   - `.header-top`: 样式化包含Logo的顶层。
 *   - `.header-bottom`: 样式化包含搜索和控制按钮的底层。
 *   - `.logo-container`, `.gmh-logo-img`, `.logo-text h1`: Logo及其文本的样式，包括悬停效果。
 *   - `.search-section`, `.controls-section`: 使用flex属性定义搜索输入和控制按钮的布局。
 *   - `@media (max-width: 320px)`, `@media (max-width: 480px)`: 针对小屏幕的响应式调整，修改头部高度、内边距和flex分布。
 *   - `.header--scrolled`: 一个类，用于在页面滚动时应用不同的样式（可能由JavaScript添加）。
 *   - `@media (prefers-reduced-motion: reduce)`: 为偏好减少动画的用户禁用过渡，增强可访问性。
 *   - `@media (prefers-contrast: high)`: 调整高对比度模式下的边框样式，增强可访问性。
 * 使用约定:
 *   - `index.html` 中的 `header` 元素使用 `.header` 类。
 *   - JavaScript（可能在 `app.js` 中）可能会根据滚动位置添加/移除 `header--scrolled` 类。
 */
/* 布局组件 - 头部样式 */

/* 主头部容器 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--gradient-header);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  color: var(--text-inverse);
  z-index: 1000;
  box-shadow: var(--shadow-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition-all);
  display: flex;
  flex-direction: column;
  height: var(--header-height);
}

/* 上层：Logo区域 */
.header-top {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-sm) var(--space-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex: 1;
}

/* 下层：搜索和功能区域 */
.header-bottom {
  display: flex;
  align-items: center;
  padding: var(--space-sm) var(--space-lg);
  gap: var(--space-sm);
  flex: 1;
}

/* Logo容器 */
.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
}

.gmh-logo-img {
  height: 28px;
  width: auto;
  border-radius: var(--radius);
  transition: var(--transition-transform);
}

.gmh-logo-img:hover {
  transform: scale(1.05);
}

/* Logo文字 */
.logo-text h1 {
  font-size: var(--font-xl);
  font-weight: var(--font-weight-bold);
  white-space: nowrap;
  color: var(--text-inverse);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin: 0;
}

/* 搜索区域布局 (70%) */
.search-section {
  flex: 0 0 70%;
  min-width: 0; /* 防止flex项目溢出 */
}

/* 功能按钮区域 (30%) */
.controls-section {
  flex: 0 0 30%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-sm);
  min-width: 0; /* 防止flex项目溢出 */
}

/* 超小屏幕优化 */
@media (max-width: 320px) {
  .header {
    height: 90px;
  }

  .header-top {
    padding: var(--space-xs) var(--space-md);
  }

  .header-bottom {
    padding: var(--space-xs) var(--space-md);
    gap: var(--space-xs);
  }

  .gmh-logo-img {
    height: 24px;
  }

  .logo-text h1 {
    font-size: var(--font-lg);
  }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  .search-section {
    flex: 0 0 65%;
  }

  .controls-section {
    flex: 0 0 35%;
  }
}

/* 头部滚动时的样式变化 */
.header--scrolled {
  background: var(--gradient-header);
  box-shadow: var(--shadow-xl);
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .header {
    transition: none;
  }
  
  .gmh-logo-img {
    transition: none;
  }
  
  .gmh-logo-img:hover {
    transform: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .header {
    border-bottom: 2px solid var(--text-inverse);
  }
  
  .header-top {
    border-bottom: 1px solid var(--text-inverse);
  }
}