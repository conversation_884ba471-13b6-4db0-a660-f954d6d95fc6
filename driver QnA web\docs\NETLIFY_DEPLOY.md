# GoMyHire FAQ - Netlify部署指南

## 🚀 快速部署

### 方法一：拖放部署（推荐）
1. 访问 [Netlify](https://netlify.com)
2. 将项目文件夹直接拖放到Netlify主页
3. 等待自动部署完成
4. 获得部署URL

### 方法二：Netlify CLI部署
```bash
# 安装Netlify CLI
npm install -g netlify-cli

# 部署到Netlify
netlify deploy --prod --dir .
```

## 📋 部署前检查

部署前请确保：
- ✅ 所有必需文件已包含
- ✅ API密钥已在meta标签中设置
- ✅ netlify.toml配置文件存在

## 🔧 环境变量配置

### 方式1：直接在HTML中配置（已配置）
已在 `index.html` 中预配置：
```html
<meta name="env:GEMINI_API_KEY" content="AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s">
<meta name="env:GEMINI_MODEL" content="gemini-2.5-flash">
```

### 方式2：使用Netlify环境变量（推荐）
1. 登录Netlify控制台
2. 进入项目设置 → Environment variables
3. 添加以下变量：
   - `GEMINI_API_KEY`: 你的Gemini API密钥
   - `GEMINI_MODEL`: gemini-2.5-flash

### 方式3：开发环境配置
在浏览器控制台中运行：
```javascript
// 设置开发环境API密钥
setNetlifyDevApiKey('AIza...your-key...');

// 清除开发环境变量
clearNetlifyDevEnvironment();
```

## 📁 必需文件清单

必需文件：
- `index.html` - 主页面
- `netlify.toml` - Netlify配置文件
- `gmh logo.png` - 网站图标
- `src/` 文件夹 - 所有JavaScript和CSS文件

```
project/
├── index.html
├── netlify.toml
├── deploy-check.js
├── gmh logo.png
├── src/
│   ├── core/
│   │   ├── config.js
│   │   ├── netlify-env-loader.js
│   │   ├── data.js
│   │   ├── i18n.js
│   │   └── app.js
│   ├── search/
│   │   └── gemini-assistant.js
│   ├── components/
│   │   └── floating-chat.js
│   └── styles/
│       └── index.css
└── NETLIFY_DEPLOY.md
```

## 🎯 部署后测试

部署后请测试以下功能：

1. **基础功能**
   - 页面正常加载
   - FAQ分类显示正确
   - 搜索功能可用

2. **AI功能**
   - Gemini搜索建议
   - 浮动聊天窗口
   - 智能回答

3. **移动端适配**
   - 响应式布局
   - 触摸手势
   - 虚拟键盘适配

## 🔍 故障排除

### 常见问题

**问题1：404错误**
- 确认 `netlify.toml` 文件存在
- 检查文件路径是否正确

**问题2：API密钥无效**
- 确认API密钥格式正确：以 `AIza` 开头，共39位字符
- 检查密钥是否已激活
- 查看浏览器控制台错误信息

**问题3：AI功能不工作**
- 检查浏览器控制台是否有CORS错误
- 确认API密钥有访问权限
- 检查网络连接

### 调试工具

在浏览器控制台中运行：
```javascript
// 运行部署检查
deployChecker.run();

// 查看系统状态
console.log(app.getSystemStatus());

// 测试API密钥
window.netlifyEnvironmentLoader.validateGeminiApiKey('your-key');
```

## 🌐 自定义域名

1. 在Netlify控制台中添加自定义域名
2. 配置DNS指向Netlify
3. 等待DNS传播完成

## 📊 性能优化

- 所有静态资源已配置缓存头
- CSS和JS文件已压缩
- 图片使用懒加载
- 响应式设计优化

## 🆘 技术支持

如果部署遇到问题：
1. 检查浏览器开发者工具控制台
2. 查看Netlify部署日志
3. 确认所有必需文件已上传
4. 联系技术支持

## 🎉 部署成功标志

当看到以下日志时，表示部署成功：
```
✅ 所有检查通过！可以部署到Netlify
🚀 部署步骤已就绪
```