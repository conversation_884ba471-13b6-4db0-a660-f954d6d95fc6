# 技术指南

**报告日期:** 2025年9月1日

## 1. API与AI配置

### 1.1. Gemini API密钥设置

*   **方式1: 环境变量 (推荐生产)**
    *   `set GEMINI_API_KEY=your_key` (Windows)
    *   `export GEMINI_API_KEY=your_key` (Linux/Mac)
*   **方式2: 修改 `config.js`**
    *   直接在 `config.js` 中设置 `apiKey`。
*   **方式3: 全局变量注入**
    *   在 `index.html` 的 `<head>` 中添加 `<script>window.GEMINI_API_KEY = 'your_key';</script>`。

### 1.2. 获取Gemini API密钥

1.  访问 [Google AI Studio](https://makersuite.google.com/app/apikey)。
2.  创建新的API密钥并复制。

### 1.3. AI功能说明

*   **默认启用**: Gemini AI助手默认启用。
*   **流式响应**: 提供即时反馈。
*   **智能搜索**: AI分析用户查询并提供建议。
*   **回退机制**: AI搜索失败时自动回退到传统搜索。

---
*本部分整合了 `docs/technical/GEMINI_SETUP.md` 和 `docs/API_KEY_SETUP.md` 的核心内容。*

## 2. 系统优化

### 2.1. 搜索优化与分类索引

*   **分类索引**:
    *   **主要分类**: `technical`, `financial`, `service`, `registration`, `communication`, `emergency`。
    *   **次要分类**: `login_issues`, `payment_failure`, 等。
    *   **关键词**: `GoMyHire`, `Ctrip`, `GPS Map Camera`, 等。
*   **两阶段搜索**:
    1.  **Gemini AI分析**: 分析查询并返回标签。
    2.  **本地匹配**: 根据标签在本地索引中匹配。

### 2.2. 性能优化

*   **搜索性能**: 预构建搜索索引，权重计算优化，结果缓存。
*   **图片加载**: 懒加载 (`loading="lazy"`)，响应式图片，占位符。
*   **内存管理**: 优化索引数据结构，避免重复数据。

---
*本部分整合了 `docs/guides/OPTIMIZATION_GUIDE.md` 的核心内容。*

## 3. 布局问题排查

### 3.1. 常见问题排查步骤

1.  **确认实际CSS类名**: 使用浏览器开发者工具检查渲染的DOM。
2.  **检查CSS优先级**: 注意 `!important` 和选择器特异性。
3.  **排查响应式断点冲突**: 检查所有相关的媒体查询。
4.  **验证CSS加载顺序**: 确认文件导入顺序和路径。

### 3.2. 关键注意事项

*   **动态生成的HTML**: JavaScript动态生成的HTML可能使用与静态文件不同的CSS类。
*   **CSS模块化**: 布局规则可能分散在不同文件中。
*   **响应式设计的级联影响**: 小屏幕的规则可能意外影响大屏幕。

---
*本部分整合了 `docs/LAYOUT_TROUBLESHOOTING_GUIDE.md` 的核心内容。*

## 4. 其他技术信息

*   **Claude AI助手**: `CLAUDE.md` 提供了与Claude Code协同工作的指南。
*   **RAG向量搜索**: `RAG-INTEGRATION-SUMMARY.md` 总结了RAG搜索的集成。

---
*本部分引用了 `docs/technical/CLAUDE.md` 和 `docs/technical/RAG-INTEGRATION-SUMMARY.md`。*
