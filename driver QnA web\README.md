# 实施计划说明

本仓库是一个静态前端（HTML/CSS/JS）FAQ 搜索与对话应用。以下为“对话先检索、再结合 AI”的落地计划与验证方法。

## 目标与范围
- 复用搜索栏同一逻辑（`app.basicSearch`）作为对话检索入口。
- 规范检索结果结构，确保渲染与 AI 上下文一致、可靠。
- AI 可用时再生成增强回复；失败/不可用时优雅回退基础列表。

## 修改点概览
- `src/components/floating-chat.js`
  - 统一 related 渲染数据结构，支持 `{question: {...}}` 与直接问题对象。
  - 基础回复 `generateBasicFAQResponse` 归一问题对象，保证标题/编号与跳转可用。
- `src/search/gemini-assistant.js`
  - 继续使用 `buildChatPrompt`/`generateFAQBasedResponse`，确保从候选 FAQ 注入上下文，并限制回答语言。
- `src/core/app.js`
  - 搜索入口为 `basicSearch(query)`（对话复用，不触发 UI 导航）。

## 工作流（简要）
1) 用户发问 → 显示“正在输入”；语言检测与模糊校验。
2) 本地检索：`const basic = app.basicSearch(q)` → 取前 3 条，归一为问题对象。
3) 组合 Prompt：`geminiAssistant.buildChatPrompt(q, related, lang)` 并调用 `generateFAQBasedResponse`。
4) 失败/超时（≤3 次）→ 回退 `generateBasicFAQResponse` 基础列表。
5) 渲染：`addMessage(content,'bot', related)`；相关问题按钮跳转 `app.showQuestionDetail(id)`。

## 验证清单
- 有/无 API Key；不同语言；无匹配；网络超时。
- 始终先检索；AI 回复包含 FAQ 依据；失败回退可用；按钮可跳转。

## 本地运行
- Python: `python -m http.server 8080` → 打开 `http://localhost:8080/`
- 测试页：`tests/test-search.html`（浏览器控制台查看日志）

## 部署与安全
- Netlify：见 `netlify.toml`，在环境变量中配置 `CONFIG.gemini.apiKey`。
- 仅允许 `https://generativelanguage.googleapis.com` 出站（CSP 在 `src/core/config.js`）。
