/**
 * 内容美化工具
 * 用于批量优化和美化FAQ问题内容的HTML结构
 */

class ContentBeautifier {
    constructor() {
        this.styleMappings = {
            'info': 'info-box',
            'procedure': 'procedure-box',
            'warning': 'warning-box',
            'tip': 'tip-box',
            'success': 'success-box',
            'error': 'error-box'
        };
        
        this.iconMappings = {
            'info': '📋',
            'procedure': '📋',
            'warning': '⚠️',
            'tip': '💡',
            'success': '✅',
            'error': '❌'
        };
    }

    /**
     * 美化单个问题的内容
     */
    beautifyQuestionContent(content) {
        if (!content) return content;

        // 替换旧的div类名
        let beautified = content
            .replace(/class="info-box"/g, `class="info-box enhanced-content"`)
            .replace(/class="procedure-box"/g, `class="procedure-box enhanced-content"`)
            .replace(/class="warning-box"/g, `class="warning-box enhanced-content"`)
            .replace(/class="tip-box"/g, `class="tip-box enhanced-content"`)
            .replace(/class="success-box"/g, `class="success-box enhanced-content"`)
            .replace(/class="error-box"/g, `class="error-box enhanced-content"`);

        // 增强标题样式
        beautified = beautified
            .replace(/<h3>(.*?)<\/h3>/g, (match, content) => {
                const icon = this.extractIcon(content) || '📋';
                return `<h3>${icon} ${content}</h3>`;
            })
            .replace(/<h4>(.*?)<\/h4>/g, (match, content) => {
                const icon = this.extractIcon(content) || '▶️';
                return `<h4>${icon} ${content}</h4>`;
            });

        // 增强列表样式
        beautified = beautified
            .replace(/<ol>/g, '<ol class="enhanced-list">')
            .replace(/<ul>/g, '<ul class="enhanced-list">');

        // 增强表格样式
        beautified = beautified
            .replace(/<table>/g, '<table class="enhanced-table">');

        return beautified;
    }

    /**
     * 提取标题中的图标
     */
    extractIcon(content) {
        const iconMatch = content.match(/^([\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}])/u);
        return iconMatch ? iconMatch[1] : null;
    }

    /**
     * 创建标准的信息盒子
     */
    createInfoBox(title, content, type = 'info') {
        const icon = this.iconMappings[type] || '📋';
        const className = this.styleMappings[type] || 'info-box';
        
        return `
            <div class="${className} enhanced-content">
                <h3>${icon} ${title}</h3>
                ${Array.isArray(content) ? this.formatListContent(content, type) : `<p>${content}</p>`}
            </div>
        `;
    }

    /**
     * 格式化列表内容
     */
    formatListContent(items, type = 'info') {
        if (items.some(item => item.includes(':'))) {
            // 有序列表（流程）
            return `
                <ol class="enhanced-list">
                    ${items.map(item => `<li>${item}</li>`).join('')}
                </ol>
            `;
        } else {
            // 无序列表（要点）
            return `
                <ul class="enhanced-list">
                    ${items.map(item => `<li>${item}</li>`).join('')}
                </ul>
            `;
        }
    }

    /**
     * 创建表格内容
     */
    createTable(headers, rows) {
        return `
            <table class="enhanced-table">
                <thead>
                    <tr>
                        ${headers.map(header => `<th>${header}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${rows.map(row => `
                        <tr>
                            ${row.map(cell => `<td>${cell}</td>`).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    /**
     * 批量处理FAQ数据
     */
    processFaqData(faqData) {
        const processed = { ...faqData };
        
        if (processed.questions && Array.isArray(processed.questions)) {
            processed.questions = processed.questions.map(question => {
                const processedQuestion = { ...question };
                
                // 处理多语言内容
                ['zh', 'en', 'ms'].forEach(lang => {
                    if (question.content && question.content[lang]) {
                        processedQuestion.content[lang] = this.beautifyQuestionContent(
                            question.content[lang]
                        );
                    }
                });
                
                return processedQuestion;
            });
        }
        
        return processed;
    }

    /**
     * 生成样式指南
     */
    generateStyleGuide() {
        return `
            <!-- 样式使用指南 -->
            <div class="info-box enhanced-content">
                <h3>📚 内容样式使用指南</h3>
                <p>以下是可用的样式类：</p>
                
                <div class="procedure-box">
                    <h4>📋 信息盒子类</h4>
                    <ul>
                        <li><code>info-box</code> - 一般信息展示</li>
                        <li><code>procedure-box</code> - 流程步骤展示</li>
                        <li><code>warning-box</code> - 警告提醒信息</li>
                        <li><code>tip-box</code> - 技巧建议信息</li>
                        <li><code>success-box</code> - 成功完成信息</li>
                        <li><code>error-box</code> - 错误问题信息</li>
                    </ul>
                </div>
                
                <div class="tip-box">
                    <h4>💡 使用技巧</h4>
                    <ul>
                        <li>每个盒子都有悬停动画效果</li>
                        <li>支持响应式设计，适配移动端</li>
                        <li>自动添加图标和装饰线条</li>
                        <li>支持深色模式切换</li>
                    </ul>
                </div>
            </div>
        `;
    }
}

// 导出工具实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContentBeautifier;
} else {
    window.ContentBeautifier = ContentBeautifier;
}

// 使用示例
console.log('ContentBeautifier loaded successfully!');
console.log('Usage: new ContentBeautifier().beautifyQuestionContent(htmlContent)');

// 创建全局工具
if (typeof window !== 'undefined') {
    window.beautifier = new ContentBeautifier();
}