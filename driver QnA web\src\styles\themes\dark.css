/*
 * 文件路径: src/styles/themes/dark.css
 * 文件描述: 定义了应用程序“暗色”主题的特定颜色值和样式。它通过覆盖或设置语义化颜色变量（在 `src/styles/tokens/colors.css` 和 `src/styles/themes/variables.css` 中定义）来创建一种深色视觉美感。此外，还包含了暗色主题下滚动条和文本选择的特定样式。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于 `src/styles/tokens/colors.css` 中定义的颜色变量（例如：`var(--neutral-50)`, `var(--neutral-900)`）以及 `src/styles/themes/variables.css` 中定义的变量（例如：`rgba(16, 185, 129, 0.1)`）。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 *   - 仅当祖先元素（通常是 `<html>` 或 `<body>`）设置了 `data-theme="dark"` 属性时，这些变量和样式才会生效。
 * 功能:
 *   - 为应用程序提供一个独特的暗色视觉主题。
 *   - 将抽象的语义颜色变量（如 `--text-primary`, `--background-color`）映射到适合深色背景的具体颜色值。
 *   - 定义暗色主题下状态指示器、输入字段和卡片的特定背景颜色。
 *   - 调整主色调的亮度，以确保在暗色模式下具有良好的对比度。
 *   - 样式化滚动条和文本选择，以提供统一的暗色主题体验。
 * 关键部分:
 *   - `[data-theme="dark"]`: 这个选择器确保这些变量和样式仅在 `data-theme="dark"` 属性被设置时应用。
 *   - **语义颜色覆盖**: 将 `--text-*`, `--background-*`, `--surface-*`, `--border-*`, `--shadow-color`, `--overlay-color` 设置为适合暗色主题的值，通常使用较深的中性色作为背景，较浅的中性色作为文本。
 *   - **玻璃拟态效果**: 重新定义玻璃拟态的 `rgba` 值，通常使用较深的基色和不同的透明度，以适应较暗的视觉效果。
 *   - **状态颜色背景**: 定义成功、警告、危险和信息状态的深色背景。
 *   - **组件特定样式**: 设置输入字段和卡片的背景和边框颜色，确保它们与暗色主题良好融合。
 *   - **主色调调整**: 调整 `--primary-color`, `--primary-dark`, `--primary-light` 以确保在暗色模式下具有良好的对比度和可见性。
 *   - **渐变调整**: 调整 `gradient-header` 以适应暗色模式。
 *   - **滚动条样式**: 使用 `::-webkit-scrollbar-*` 伪元素来专门为暗色主题样式化滚动条。
 *   - **文本选择样式**: 使用 `::selection` 伪元素来样式化暗色主题下的文本选择。
 * 使用约定:
 *   - 此文件通常作为主题切换机制的一部分（例如，`app.js` 切换 `<body>` 或 `<html>` 上的类或 `data-theme` 属性）进行条件加载或应用。
 */
/* 主题系统 - 暗色主题 */

/* 主题系统 - 暗色主题 */

.dark-theme {
  /* 语义颜色覆盖 - 暗色 */
  --text-primary: var(--neutral-50);
  --text-secondary: var(--neutral-300);
  --text-tertiary: var(--neutral-500);
  --text-inverse: var(--neutral-900);
  
  --background-color: var(--neutral-900);
  --background-secondary: var(--neutral-800);
  --surface-color: var(--neutral-800);
  --surface-elevated: var(--neutral-700);
  
  --border-color: var(--neutral-700);
  --border-light: var(--neutral-600);
  
  --shadow-color: rgba(0, 0, 0, 0.3);
  --overlay-color: rgba(0, 0, 0, 0.7);

  /* 玻璃拟态效果 - 暗色 */
  --glass-background: rgba(17, 24, 39, 0.1);
  --glass-background-intense: rgba(17, 24, 39, 0.15);
  --glass-background-subtle: rgba(17, 24, 39, 0.05);
  --glass-border: rgba(75, 85, 99, 0.2);
  --glass-border-intense: rgba(75, 85, 99, 0.3);
  --glass-border-subtle: rgba(75, 85, 99, 0.1);

  /* 状态颜色背景 - 暗色 */
  --success-background: rgba(16, 185, 129, 0.1);
  --warning-background: rgba(245, 158, 11, 0.1);
  --danger-background: rgba(239, 68, 68, 0.1);
  --info-background: rgba(59, 130, 246, 0.1);

  /* 输入框样式 - 暗色 */
  --input-background: var(--surface-color);
  --input-border: var(--border-color);
  --input-focus-border: var(--primary-400);
  --input-placeholder: var(--text-tertiary);

  /* 卡片样式 - 暗色 */
  --card-background: var(--surface-color);
  --card-border: var(--border-color);
  --card-hover-background: var(--surface-elevated);

  /* 调整主色调在暗色模式下的对比度 */
  --primary-color: var(--primary-400);
  --primary-dark: var(--primary-500);
  --primary-light: var(--primary-200);

  /* 渐变调整 - 暗色 */
  --gradient-header: linear-gradient(135deg, 
    rgba(168, 85, 247, 0.7), 
    rgba(184, 61, 186, 0.7)
  );
}

/* 暗色主题下的滚动条 */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 暗色主题下的文字选择 */
[data-theme="dark"] ::selection {
  background-color: var(--primary-800);
  color: var(--primary-100);
}