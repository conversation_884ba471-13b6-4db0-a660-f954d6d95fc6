/*
 * 文件路径: src/styles/tokens/typography.css
 * 文件描述: 定义了应用程序的排版系统，通过CSS自定义属性（变量）提供了一致的字体大小、行高、字重和字体家族。它还包含了用于标题、正文和说明文字的语义化排版样式。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 集中管理所有排版定义，确保应用程序的视觉一致性和可读性。
 *   - 通过提供所有文本相关样式单一的真实来源，简化字体管理和更新。
 *   - 促进排版的语义化使用，允许开发者应用有意义的样式（例如：`--text-heading-1`），而不是单独的字体属性。
 * 关键部分:
 *   - `--font-xs` 到 `--font-4xl`: 定义了一个可伸缩的字体大小系统。
 *   - `--leading-tight`, `--leading-normal`, `--leading-relaxed`: 定义了一致的行高值。
 *   - `--font-weight-normal` 到 `--font-weight-bold`: 定义了标准的字重。
 *   - `--font-family-sans`, `--font-family-mono`: 定义了无衬线和等宽字体的字体栈。
 *   - `--text-heading-1` 到 `--text-caption`: 结合了字体大小、行高和字体家族的语义化排版样式，用于常见的文本元素。
 * 使用约定:
 *   - 其他CSS文件应通过 `var()` 函数引用这些排版变量（例如：`font: var(--text-body);`）来应用一致的排版样式。
 */
/* 设计令牌 - 字体系统 */
:root {
  /* 字体大小系统 */
  --font-xs: 12px;
  --font-sm: 14px;
  --font-base: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
  --font-3xl: 30px;
  --font-4xl: 36px;

  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 字体家族 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;

  /* 语义化字体样式 */
  --text-heading-1: var(--font-3xl) / var(--leading-tight) var(--font-family-sans);
  --text-heading-2: var(--font-2xl) / var(--leading-tight) var(--font-family-sans);
  --text-heading-3: var(--font-xl) / var(--leading-normal) var(--font-family-sans);
  --text-body: var(--font-base) / var(--leading-normal) var(--font-family-sans);
  --text-small: var(--font-sm) / var(--leading-normal) var(--font-family-sans);
  --text-caption: var(--font-xs) / var(--leading-tight) var(--font-family-sans);
}