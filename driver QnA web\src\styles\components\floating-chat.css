/*
 * 文件路径: src/styles/components/floating-chat.css
 * 文件描述: GoMyHire FAQ 系统浮动聊天窗口的样式定义
 * 功能: 定义浮动聊天窗口的外观、动画和交互样式
 */

/* ===== 浮动聊天窗口 ===== */
.floating-chat-window {
  position: fixed;
  top: calc(var(--header-height) + 20px);
  right: 20px;
  width: 400px;
  height: 500px;
  background: var(--glass-bg);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: 1200; /* 提升层级，确保不被固定头部和下拉建议遮挡 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: var(--transition-smooth);
  font-family: var(--font-family);
}

/* 移动端优化：避免与固定头部重叠，放到底部右侧且自适应尺寸 */
@media (max-width: 768px) {
  .floating-chat-window {
    top: auto;
    right: 16px;
  bottom: calc(var(--bottom-nav-height) + 16px);
    width: min(92vw, 360px);
    height: min(70vh, 420px);
  }
}

/* 窗口标题栏 */
.chat-window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  background: var(--glass-bg-secondary);
  border-bottom: 1px solid var(--glass-border);
  cursor: move;
  -webkit-user-select: none;
  user-select: none;
}

.window-title {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.title-icon {
  font-size: var(--text-lg);
}

.title-text {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.window-controls {
  display: flex;
  gap: var(--space-xs);
}

.control-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--glass-bg);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-md);
  transition: var(--transition-fast);
}

.control-btn:hover {
  background: var(--glass-bg-hover);
  color: var(--text-primary);
}

.minimize-btn:hover {
  background: var(--warning-bg);
  color: var(--warning-text);
}

.close-btn:hover {
  background: var(--error-bg);
  color: var(--error-text);
}

/* 消息容器 */
.chat-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-md);
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.chat-messages-container::-webkit-scrollbar {
  width: 6px;
}

.chat-messages-container::-webkit-scrollbar-track {
  background: var(--glass-bg);
}

.chat-messages-container::-webkit-scrollbar-thumb {
  background: var(--glass-border);
  border-radius: var(--radius-sm);
}

.chat-messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* 消息样式 */
.message {
  display: flex;
  gap: var(--space-sm);
  max-width: 100%;
}

.message.user-message {
  justify-content: flex-end;
}

.message.bot-message {
  justify-content: flex-start;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.user-message .avatar {
  background: var(--primary-bg);
  order: 2;
}

.bot-message .avatar {
  background: var(--secondary-bg);
  order: 1;
}

.message-body {
  max-width: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.user-message .message-body {
  align-items: flex-end;
}

.bot-message .message-body {
  align-items: flex-start;
}

.message-content {
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.user-message .message-content {
  background: var(--primary-bg);
  color: var(--primary-text);
  border-color: var(--primary-border);
}

.bot-message .message-content {
  background: var(--glass-bg);
  color: var(--text-primary);
  border-color: var(--glass-border);
}

.message-meta {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-xs);
  color: var(--text-muted);
}

.user-message .message-meta {
  justify-content: flex-end;
}

.bot-message .message-meta {
  justify-content: flex-start;
}

.message-time {
  opacity: 0.7;
}

/* 输入区域 */
.chat-input-area {
  padding: var(--space-md);
  background: var(--glass-bg-secondary);
  border-top: 1px solid var(--glass-border);
}

.input-container {
  display: flex;
  gap: var(--space-sm);
  align-items: center;
}

.chat-input {
  flex: 1;
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  color: var(--text-primary);
  font-size: var(--text-base);
  outline: none;
  transition: var(--transition-fast);
}

.chat-input:focus {
  border-color: var(--primary-border);
  box-shadow: 0 0 0 2px var(--primary-shadow);
}

.chat-input::placeholder {
  color: var(--text-muted);
}

.send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-lg);
  background: var(--primary-bg);
  color: var(--primary-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
  flex-shrink: 0;
}

.send-btn:hover {
  background: var(--primary-hover);
  transform: scale(1.05);
}

.send-btn:active {
  transform: scale(0.95);
}

.send-icon {
  font-size: var(--text-lg);
}

/* 调整大小手柄 */
.resize-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  background: transparent;
  cursor: nw-resize;
  opacity: 0;
  transition: var(--transition-fast);
}

.resize-handle-se {
  bottom: 0;
  right: 0;
  cursor: nw-resize;
}

.resize-handle-s {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  cursor: ns-resize;
  width: 100%;
  height: 10px;
}

.resize-handle-e {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  cursor: ew-resize;
  width: 10px;
  height: 100%;
}

.floating-chat-window:hover .resize-handle {
  opacity: 0.5;
}

.resize-handle:hover {
  opacity: 1 !important;
  background: var(--primary-bg);
}

/* 最小化状态 */
.floating-chat-window.minimized {
  height: 60px;
}

.floating-chat-window.minimized .chat-messages-container,
.floating-chat-window.minimized .chat-input-area {
  display: none;
}

.floating-chat-window.minimized .chat-window-header {
  border-bottom: none;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.typing-dots {
  display: flex;
  gap: var(--space-xs);
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--text-muted);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-chat-window {
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    height: 400px;
    max-width: none;
  }

  .window-controls {
    gap: var(--space-xs);
  }

  .control-btn {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .floating-chat-window {
    top: 5px;
    right: 5px;
    left: 5px;
    height: 350px;
  }

  .chat-window-header {
    padding: var(--space-sm);
  }

  .chat-messages-container,
  .chat-input-area {
    padding: var(--space-sm);
  }
}

/* 动画效果 */
.floating-chat-window.show {
  animation: slideIn 0.3s ease-out;
}

.floating-chat-window.hide {
  animation: slideOut 0.3s ease-in;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
}

/* 深色主题适配 */
[data-theme="dark"] .floating-chat-window {
  --glass-bg: rgba(30, 30, 30, 0.95);
  --glass-bg-secondary: rgba(45, 45, 45, 0.9);
  --glass-border: rgba(255, 255, 255, 0.1);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .floating-chat-window {
    --glass-bg: rgba(255, 255, 255, 0.98);
    --glass-border: rgba(0, 0, 0, 0.3);
  }

  [data-theme="dark"] .floating-chat-window {
    --glass-bg: rgba(0, 0, 0, 0.98);
    --glass-border: rgba(255, 255, 255, 0.3);
  }
}

/* FAQ链接样式 */
.chat-faq-link {
  color: var(--primary-color, #007bff);
  text-decoration: none;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: var(--radius-sm, 4px);
  background: rgba(0, 123, 255, 0.1);
  border: 1px solid rgba(0, 123, 255, 0.2);
  transition: all 0.2s ease;
  display: inline-block;
  cursor: pointer;
  font-size: 0.9em;
}

.chat-faq-link:hover {
  background: rgba(0, 123, 255, 0.2);
  border-color: rgba(0, 123, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.chat-faq-link:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 123, 255, 0.2);
}

/* 深色主题下的FAQ链接 */
[data-theme="dark"] .chat-faq-link {
  color: #4dabf7;
  background: rgba(77, 171, 247, 0.15);
  border-color: rgba(77, 171, 247, 0.3);
}

[data-theme="dark"] .chat-faq-link:hover {
  background: rgba(77, 171, 247, 0.25);
  border-color: rgba(77, 171, 247, 0.5);
  box-shadow: 0 2px 4px rgba(77, 171, 247, 0.3);
}
