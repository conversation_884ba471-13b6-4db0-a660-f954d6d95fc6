/*
 * 文件路径: src/core/i18n.js
 * 文件描述: 应用程序的多语言（Internationalization, i18n）配置文件和管理器。它定义了中文、英文和马来文的翻译字符串，并提供了语言切换和文本渲染的功能        // 页脚
        contact: "联系我们",
        customerService: "客服热线：400-XXX-XXXX",
        email: "邮箱：<EMAIL>",
        quickLinks: "快速链接",
        driverApp: "司机APP下载",
        driverPortal: "司机门户",
        trainingCenter: "培训中心",
        allRightsReserved: "版权所有",
        
        // 联系方式
        gomyhireCustomerService: "GoMyHire客服",
        whatsappContact: "WhatsApp +60162234711",
        appCustomerService: "App客服", 
        appCustomerServicePath: "通过GoMyHire APP内客服入口",
        platformMessages: "平台内消息",
        platformMessagesDesc: "使用APP内文字讯息功能（推荐）",度追踪记录:
 *
 * 1.  **翻译数据 (`i18n` 对象)**:
 *     - `i18n` 是一个常量对象，在脚本加载时立即定义。
 *     - 它包含 `zh` (中文), `en` (英文), `ms` (马来文) 三个顶级属性，每个属性对应一种语言的翻译键值对。
 *     - 键值对覆盖了网站标题、导航、欢迎页面、分类名称、操作按钮、搜索相关、错误信息、提示信息以及时间文本等所有UI文本。
 *     - 它是 `I18nManager` 实例的数据源。
 *
 * 2.  **多语言管理器 (`I18nManager` 类)**:
 *     - **构造函数 (`constructor`)**:
 *         - 在实例化时，它会尝试从 `localStorage` 中获取名为 `language` 的值作为 `this.currentLang` 的初始值。
 *         - 如果 `localStorage` 中没有找到，则默认设置为 `'zh'` (中文)。
 *         - 将全局 `i18n` 对象赋值给 `this.translations`，作为所有翻译数据的内部引用。
 *     - **设置语言 (`setLanguage(lang)`)**:
 *         - **输入**: `lang` (字符串)，表示要切换到的语言代码（如 'zh', 'en', 'ms'）。
 *         - **逻辑**: 首先检查 `this.translations` 中是否存在该语言的翻译数据。
 *         - 如果存在，则更新 `this.currentLang` 为新语言，并将新语言保存到 `localStorage` 中，实现语言偏好的持久化。
 *         - **控制流**: 成功设置语言后，会立即调用 `this.updatePageTexts()` 和 `this.updateDocumentLang()` 来更新页面UI。
 *     - **获取翻译文本 (`t(key, params = {})` / `getText(key, params = {})`)**:
 *         - **输入**: `key` (字符串)，表示翻译键；`params` (对象)，可选，用于替换文本中的占位符（如 `{count}`）。
 *         - **逻辑**: 从 `this.translations[this.currentLang]` 中查找对应 `key` 的文本。
 *         - 如果找到，则遍历 `params` 对象，将文本中形如 `{param}` 的占位符替换为 `params[param]` 的实际值。
 *         - 如果未找到 `key` 对应的翻译，则直接返回 `key` 本身作为回退。
 *         - `getText` 是 `t` 方法的别名，提供向后兼容性。
 *     - **更新页面所有文本 (`updatePageTexts()`)**:
 *         - **逻辑**: 遍历文档中所有带有 `data-i18n` 属性的HTML元素。
 *         - 对于每个元素，获取其 `data-i18n` 属性值作为翻译键，然后调用 `this.t()` 获取翻译后的文本，并更新元素的 `textContent`。
 *         - **特殊处理**: 额外处理 `searchInput` 元素的 `placeholder` 属性，以及 `document.title` (如果 `siteTitle` 有翻译)。
 *         - **可选局部标题**: 遍历带有 `data-i18n-title` 属性的元素，更新其 `textContent`。
 *     - **更新文档语言属性 (`updateDocumentLang()`)**:
 *         - **逻辑**: 将 `document.documentElement.lang` 属性设置为当前的 `this.currentLang`，这有助于浏览器和辅助技术正确识别页面语言。
 *     - **获取当前语言 (`getCurrentLanguage()`)**:
 *         - **输出**: 返回当前的 `this.currentLang`。
 *     - **格式化消息时间 (`formatMessageTime(date)`)**:
 *         - **输入**: `date` (Date对象或可解析为Date的字符串)。
 *         - **逻辑**: 计算当前时间与给定时间之间的差值。
 *         - **智能格式化**: 如果时间差小于45秒，显示“刚刚”；如果小于60分钟，尝试使用 `Intl.RelativeTimeFormat` 显示“X分钟前”，否则回退到简单字符串；对于更长的时间，使用 `Intl.DateTimeFormat` 显示本地化时间。
 *         - **依赖**: `Intl.RelativeTimeFormat` 和 `Intl.DateTimeFormat` (浏览器内置API)。
 *
 * 依赖关系 (更详细):
 *   - `I18nManager` 依赖于全局 `i18n` 对象作为其翻译数据源。
 *   - `I18nManager` 依赖于浏览器内置的 `localStorage` API 来存储和检索用户语言偏好。
 *   - `I18nManager` 依赖于 `document` 对象来查询和修改DOM元素，实现UI文本的更新。
 *   - `I18nManager` 依赖于 `Intl` 对象（如果可用）来提供更高级的日期时间格式化功能。
 *
 * 模块间数据与控制流:
 *   - **`app.js` -> `i18n.js`**: `app.js` 实例化 `I18nManager`，并调用其 `setLanguage` 方法来改变语言。`app.js` 也可能直接调用 `i18n.t` 或 `i18n.getText` 来获取特定文本。
 *   - **`i18n.js` -> `localStorage`**: `I18nManager` 在 `setLanguage` 中写入 `localStorage`，在 `constructor` 中读取 `localStorage`。
 *   - **`i18n.js` -> `document`**: `I18nManager` 在 `updatePageTexts` 和 `updateDocumentLang` 中直接操作 `document` 来更新UI和HTML属性。
 *   - **`i18n.js` -> `i18n` (全局对象)**: `I18nManager` 内部引用了全局 `i18n` 对象来获取翻译数据。
 *
 * 这个更深度的追踪记录揭示了 `i18n.js` 如何作为应用程序的多语言核心，通过管理翻译数据和动态更新UI来提供无缝的语言切换体验。
 */
// 多语言配置文件
const i18n = {
    // 中文
    zh: {
    // Site
    siteTitle: "GoMyHire 司机FAQ",
    siteSubtitle: "司机FAQ系统",
        // 导航和界面
        categories: "问题分类",
        quickAccess: "快速访问",
        favorites: "收藏夹",
        recent: "最近浏览",
        popular: "热门问题",
        searchResults: "搜索结果",
        loading: "加载中...",
        
        // 欢迎页面
        welcomeTitle: "欢迎使用GoMyHire司机FAQ系统",
        welcomeDesc: "这里汇集了司机朋友们最常遇到的问题和详细解答。您可以通过分类浏览或搜索快速找到需要的信息。",
        searchFeature: "智能搜索",
        searchFeatureDesc: "支持问题编号、关键词搜索",
        mobileFeature: "移动优化",
        mobileFeatureDesc: "完美适配手机和平板设备",
        multiLangFeature: "多语言支持",
        multiLangFeatureDesc: "中文、英文、马来文三语切换",
        quickStart: "快速开始",
        
        // 分类名称
        registration: "司机注册",
        appUsage: "APP使用",
        orderManagement: "订单管理",
        customerService: "客户沟通",
        payment: "支付财务",
        rating: "评价评分",
        training: "培训考试",
        vehicle: "车辆管理",
        safety: "安全合规",
        emergency: "紧急处理",
        
        // 操作按钮
        addToFavorites: "添加到收藏",
        removeFromFavorites: "取消收藏",
        share: "分享",
        feedback: "反馈",
        backToHome: "返回首页",
        previousQuestion: "上一题",
        nextQuestion: "下一题",
        
        // 搜索相关
        search: "搜索",
        searchPlaceholder: "搜索问题编号或关键词...",
        searchNoResults: "未找到相关问题",
        searchResultsCount: "找到 {count} 个相关问题",
        searchExactMatch: "精确匹配",
        searchAiRecommend: "AI推荐",
        searchFuzzyMatch: "模糊匹配",
        searchRelevance: "相关度",
        searchBackToHome: "返回首页",
        searchSuggestionTitle: "为了获得更好的搜索结果，您可以：",
        searchSuggestion1: "检查关键词拼写",
        searchSuggestion2: "尝试更简单的词汇",
        searchSuggestion3: "使用同义词搜索",
        searchNoResultsTitle: "没有找到匹配的问题",
        searchTryDifferentKeywords: "未找到相关问题，尝试使用不同的关键词",
        uncategorized: "未分类",
        searching: "正在搜索...",
        searchingDescription: "正在为您查找相关问题，支持中文、英文、马来文",
        
        // 相关问题
        relatedQuestions: "相关问题",
        seeMore: "查看更多",
        
        // 优先级
        priorityHigh: "高频",
        priorityMedium: "常见",
        priorityLow: "一般",
        
        // 底部信息
        contact: "联系我们",
        customerService: "客服热线：400-XXX-XXXX",
        email: "邮箱：<EMAIL>",
        quickLinks: "快速链接",
        driverApp: "司机APP下载",
        driverPortal: "司机门户",
        trainingCenter: "培训中心",
        allRightsReserved: "版权所有",
        
        // 错误信息
        errorLoadingData: "数据加载失败，请刷新页面重试",
        errorNotFound: "页面未找到",
        errorNetwork: "网络连接异常，请检查网络设置",
        
        // 提示信息
        addedToFavorites: "已添加到收藏夹",
        removedFromFavorites: "已从收藏夹移除",
        copiedToClipboard: "链接已复制到剪贴板",
        thankYouForFeedback: "感谢您的反馈",
    // 时间文本
    justNow: "现在",
    minutesAgo: "分钟前",

        // 欢迎页面动态文本
        welcomeFAQTitle: "欢迎使用GoMyHire司机FAQ",
        welcomeFAQDesc: "选择分类快速找到您需要的答案",
        questionsCount: "个问题",
        totalQuestions: "共 {count} 个问题",

        // 页面导航
        backToHomePage: "← 返回首页",
        unknownCategory: "未知分类",
        relatedQuestionsTitle: "相关问题",

        // 主题切换
        switchToDarkTheme: "切换到暗色主题",
        switchToLightTheme: "切换到亮色主题",

        // 搜索结果页面
        searchResultsTitle: "搜索结果",
        searchFor: "搜索",
        foundResults: "找到 {count} 个结果",
        noResultsFound: "没有找到相关结果",
        noResultsDesc: "请尝试使用其他关键词或浏览分类查找相关问题。",
        backToHomeBtn: "返回首页"
    ,
    // Chat module
    chatAssistantTitle: "GoMyHire AI助手",
    chatMinimize: "最小化",
    chatClose: "关闭",
    chatWelcome: "您好！我是GoMyHire AI助手，基于FAQ数据库为您提供智能解答。",
    chatInputPlaceholder: "输入您的问题...",
    chatToggleOpen: "打开AI聊天助手",
    chatToggleClose: "关闭AI聊天助手",
    chatErrorGeneric: "抱歉，处理您的请求时出现了问题。请稍后再试。"
    },
    
    // 英文
    en: {
    // Site
    siteTitle: "GoMyHire Driver FAQ",
    siteSubtitle: "Driver FAQ System",
        // Navigation and Interface
        categories: "Categories",
        quickAccess: "Quick Access",
        favorites: "Favorites",
        recent: "Recent",
        popular: "Popular",
        searchResults: "Search Results",
        loading: "Loading...",
        
        // Welcome Page
        welcomeTitle: "Welcome to GoMyHire Driver FAQ System",
        welcomeDesc: "Here you'll find answers to the most common questions drivers encounter. Browse by category or search to quickly find the information you need.",
        searchFeature: "Smart Search",
        searchFeatureDesc: "Search by question ID or keywords",
        mobileFeature: "Mobile Optimized",
        mobileFeatureDesc: "Perfect for phones and tablets",
        multiLangFeature: "Multi-language",
        multiLangFeatureDesc: "Chinese, English, Malay support",
        quickStart: "Quick Start",
        
        // Category Names
        registration: "Driver Registration",
        appUsage: "App Usage",
        orderManagement: "Order Management",
        customerService: "Customer Communication",
        payment: "Payment & Finance",
        rating: "Rating & Review",
        training: "Training & Exam",
        vehicle: "Vehicle Management",
        safety: "Safety & Compliance",
        emergency: "Emergency Handling",
        
        // Action Buttons
        addToFavorites: "Add to Favorites",
        removeFromFavorites: "Remove from Favorites",
        share: "Share",
        feedback: "Feedback",
        backToHome: "Back to Home",
        previousQuestion: "Previous",
        nextQuestion: "Next",
        
        // Search Related
        search: "Search",
        searchPlaceholder: "Search by question ID or keywords...",
        searchNoResults: "No results found",
        searchResultsCount: "Found {count} related questions",
        searchExactMatch: "Exact Match",
        searchAiRecommend: "AI Recommended",
        searchFuzzyMatch: "Fuzzy Match",
        searchRelevance: "Relevance",
        searchBackToHome: "Back to Home",
        searchSuggestionTitle: "For better search results, you can:",
        searchSuggestion1: "Check keyword spelling",
        searchSuggestion2: "Try simpler terms",
        searchSuggestion3: "Use synonyms",
        searchNoResultsTitle: "No matching questions found",
        searchTryDifferentKeywords: "No related questions found, try using different keywords",
        uncategorized: "Uncategorized",
        searching: "Searching...",
        searchingDescription: "Finding relevant questions for you, supports Chinese, English, Malay",
        
        // Related Questions
        relatedQuestions: "Related Questions",
        seeMore: "See More",
        
        // Priority
        priorityHigh: "High Frequency",
        priorityMedium: "Common",
        priorityLow: "General",
        
        // Footer
        contact: "Contact Us",
        customerService: "Hotline: 400-XXX-XXXX",
        email: "Email: <EMAIL>",
        quickLinks: "Quick Links",
        driverApp: "Driver App Download",
        driverPortal: "Driver Portal",
        trainingCenter: "Training Center",
        allRightsReserved: "All Rights Reserved",
        
        // Contact Information
        gomyhireCustomerService: "GoMyHire Customer Service",
        whatsappContact: "WhatsApp +60162234711",
        appCustomerService: "App Customer Service",
        appCustomerServicePath: "Through GoMyHire APP customer service entrance",
        platformMessages: "Platform Messages",
        platformMessagesDesc: "Use APP text message function (recommended)",
        
        // Error Messages
        errorLoadingData: "Failed to load data, please refresh and try again",
        errorNotFound: "Page not found",
        errorNetwork: "Network connection error, please check your connection",
        
        // Notifications
        addedToFavorites: "Added to favorites",
        removedFromFavorites: "Removed from favorites",
        copiedToClipboard: "Link copied to clipboard",
        thankYouForFeedback: "Thank you for your feedback",
    // time text
    justNow: "just now",
    minutesAgo: "minutes ago",

        // Welcome page dynamic text
        welcomeFAQTitle: "Welcome to GoMyHire Driver FAQ",
        welcomeFAQDesc: "Select a category to quickly find the answers you need",
        questionsCount: "questions",
        totalQuestions: "Total {count} questions",

        // Page navigation
        backToHomePage: "← Back to Home",
        unknownCategory: "Unknown Category",
        relatedQuestionsTitle: "Related Questions",

        // Theme switching
        switchToDarkTheme: "Switch to dark theme",
        switchToLightTheme: "Switch to light theme",

        // Search results page
        searchResultsTitle: "Search Results",
        searchFor: "Search for",
        foundResults: "Found {count} results",
        noResultsFound: "No results found",
        noResultsDesc: "Please try using other keywords or browse categories to find related questions.",
        backToHomeBtn: "Back to Home"
    ,
    // Chat module
    chatAssistantTitle: "GoMyHire AI Assistant",
    chatMinimize: "Minimize",
    chatClose: "Close",
    chatWelcome: "Hello! I'm the GoMyHire AI Assistant, answering based on our FAQ knowledge base.",
    chatInputPlaceholder: "Type your question...",
    chatToggleOpen: "Open AI Assistant",
    chatToggleClose: "Close AI Assistant",
    chatErrorGeneric: "Sorry, something went wrong. Please try again later."
    },
    
    // 马来文
    ms: {
    // Site
    siteTitle: "GoMyHire FAQ Pemandu",
    siteSubtitle: "Sistem FAQ Pemandu",
        // Navigasi dan Antara Muka
        categories: "Kategori",
        quickAccess: "Akses Pantas",
        favorites: "Kegemaran",
        recent: "Terkini",
        popular: "Popular",
        searchResults: "Hasil Carian",
        loading: "Memuatkan...",
        
        // Halaman Selamat Datang
        welcomeTitle: "Selamat Datang ke Sistem FAQ Pemandu GoMyHire",
        welcomeDesc: "Di sini anda akan menemui jawapan kepada soalan yang paling kerap dihadapi oleh pemandu. Layari mengikut kategori atau cari untuk mendapatkan maklumat yang diperlukan dengan pantas.",
        searchFeature: "Carian Pintar",
        searchFeatureDesc: "Cari mengikut ID soalan atau kata kunci",
        mobileFeature: "Dioptimumkan untuk Mudah Alih",
        mobileFeatureDesc: "Sempurna untuk telefon dan tablet",
        multiLangFeature: "Pelbagai Bahasa",
        multiLangFeatureDesc: "Sokongan Cina, Inggeris, Melayu",
        quickStart: "Mula Pantas",
        
        // Nama Kategori
        registration: "Pendaftaran Pemandu",
        appUsage: "Penggunaan Aplikasi",
        orderManagement: "Pengurusan Pesanan",
        customerService: "Komunikasi Pelanggan",
        payment: "Pembayaran & Kewangan",
        rating: "Penilaian & Ulasan",
        training: "Latihan & Peperiksaan",
        vehicle: "Pengurusan Kenderaan",
        safety: "Keselamatan & Pematuhan",
        emergency: "Pengendalian Kecemasan",
        
        // Butang Tindakan
        addToFavorites: "Tambah ke Kegemaran",
        removeFromFavorites: "Buang dari Kegemaran",
        share: "Kongsi",
        feedback: "Maklum Balas",
        backToHome: "Kembali ke Utama",
        previousQuestion: "Sebelumnya",
        nextQuestion: "Seterusnya",
        
        // Berkaitan Carian
        search: "Carian",
        searchPlaceholder: "Cari mengikut ID soalan atau kata kunci...",
        searchNoResults: "Tiada hasil ditemui",
        searchResultsCount: "Ditemui {count} soalan berkaitan",
        searchExactMatch: "Padanan Tepat",
        searchAiRecommend: "Cadangan AI",
        searchFuzzyMatch: "Padanan Kabur",
        searchRelevance: "Kaitan",
        searchBackToHome: "Kembali ke Utama",
        searchSuggestionTitle: "Untuk hasil carian yang lebih baik, anda boleh:",
        searchSuggestion1: "Semak ejaan kata kunci",
        searchSuggestion2: "Cuba istilah yang lebih mudah",
        searchSuggestion3: "Gunakan sinonim",
        searchNoResultsTitle: "Tiada soalan yang sepadan ditemui",
        searchTryDifferentKeywords: "Tiada soalan berkaitan ditemui, cuba gunakan kata kunci yang berbeza",
        uncategorized: "Tidak Berkategori",
        searching: "Mencari...",
        searchingDescription: "Mencari soalan berkaitan untuk anda, menyokong Bahasa Cina, Inggeris, Melayu",
        
        // Soalan Berkaitan
        relatedQuestions: "Soalan Berkaitan",
        seeMore: "Lihat Lagi",
        
        // Keutamaan
        priorityHigh: "Kekerapan Tinggi",
        priorityMedium: "Biasa",
        priorityLow: "Am",
        
        // Footer
        contact: "Hubungi Kami",
        customerService: "Talian Hotline: 400-XXX-XXXX",
        email: "E-mel: <EMAIL>",
        quickLinks: "Pautan Pantas",
        driverApp: "Muat Turun Aplikasi Pemandu",
        driverPortal: "Portal Pemandu",
        trainingCenter: "Pusat Latihan",
        allRightsReserved: "Hak Cipta Terpelihara",
        
        // Maklumat Hubungan
        gomyhireCustomerService: "Khidmat Pelanggan GoMyHire",
        whatsappContact: "WhatsApp +60162234711",
        appCustomerService: "Khidmat Pelanggan App",
        appCustomerServicePath: "Melalui pintu masuk khidmat pelanggan APP GoMyHire",
        platformMessages: "Mesej Platform",
        platformMessagesDesc: "Gunakan fungsi mesej teks dalam APP (disyorkan)",
        
        // Mesej Ralat
        errorLoadingData: "Gagal memuatkan data, sila muat semula dan cuba lagi",
        errorNotFound: "Halaman tidak ditemui",
        errorNetwork: "Ralat sambungan rangkaian, sila periksa sambungan anda",
        
        // Pemberitahuan
        addedToFavorites: "Ditambah ke kegemaran",
        removedFromFavorites: "Dibuang dari kegemaran",
        copiedToClipboard: "Pautan disalin ke papan keratan",
        thankYouForFeedback: "Terima kasih atas maklum balas anda",
    // masa
    justNow: "baru saja",
    minutesAgo: "minit yang lalu",

        // Teks dinamik halaman selamat datang
        welcomeFAQTitle: "Selamat Datang ke FAQ Pemandu GoMyHire",
        welcomeFAQDesc: "Pilih kategori untuk mencari jawapan yang anda perlukan dengan pantas",
        questionsCount: "soalan",
        totalQuestions: "Jumlah {count} soalan",

        // Navigasi halaman
        backToHomePage: "← Kembali ke Utama",
        unknownCategory: "Kategori Tidak Diketahui",
        relatedQuestionsTitle: "Soalan Berkaitan",

        // Penukaran tema
        switchToDarkTheme: "Tukar ke tema gelap",
        switchToLightTheme: "Tukar ke tema terang",

        // Halaman hasil carian
        searchResultsTitle: "Hasil Carian",
        searchFor: "Cari untuk",
        foundResults: "Ditemui {count} hasil",
        noResultsFound: "Tiada hasil ditemui",
        noResultsDesc: "Sila cuba menggunakan kata kunci lain atau layari kategori untuk mencari soalan berkaitan.",
        backToHomeBtn: "Kembali ke Utama"
    ,
    // Chat module
    chatAssistantTitle: "Pembantu AI GoMyHire",
    chatMinimize: "Minimakan",
    chatClose: "Tutup",
    chatWelcome: "Hai! Saya Pembantu AI GoMyHire, menjawab berdasarkan pangkalan pengetahuan FAQ.",
    chatInputPlaceholder: "Taip soalan anda...",
    chatToggleOpen: "Buka Pembantu AI",
    chatToggleClose: "Tutup Pembantu AI",
    chatErrorGeneric: "Maaf, berlaku ralat. Sila cuba lagi nanti."
    }
};

// 多语言管理器
class I18nManager {
    constructor() {
        this.currentLang = localStorage.getItem('language') || 'zh';
        this.translations = i18n;
    }
    
    // 设置语言
    setLanguage(lang) {
        if (this.translations[lang]) {
            this.currentLang = lang;
            localStorage.setItem('language', lang);
            this.updatePageTexts();
            this.updateDocumentLang();
        }
    }
    
    // 获取翻译文本
    t(key, params = {}) {
        let text = this.translations[this.currentLang][key] || key;
        
        // 替换参数
        Object.keys(params).forEach(param => {
            text = text.replace(`{${param}}`, params[param]);
        });
        
        return text;
    }
    
    // getText 方法别名，保持向后兼容性
    getText(key, params = {}) {
        return this.t(key, params);
    }
    
    // 更新页面所有文本
    updatePageTexts() {
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            element.textContent = this.t(key);
        });
        
        // 更新占位符文本
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.placeholder = this.t('searchPlaceholder');
        }

        // 通用：根据 data-i18n-placeholder 设置 placeholder 属性
        const placeholderEls = document.querySelectorAll('[data-i18n-placeholder]');
        placeholderEls.forEach(el => {
            const key = el.getAttribute('data-i18n-placeholder');
            if (key) el.placeholder = this.t(key);
        });

        // 通用：根据 data-i18n-title-attr 设置 title 属性
        const titleAttrEls = document.querySelectorAll('[data-i18n-title-attr]');
        titleAttrEls.forEach(el => {
            const key = el.getAttribute('data-i18n-title-attr');
            if (key) el.title = this.t(key);
        });

        // 更新文档标题（如果有翻译）
        if (this.translations[this.currentLang]['siteTitle']) {
            document.title = this.t('siteTitle');
        }

        // 更新可选的局部标题属性 data-i18n-title
        const titleElements = document.querySelectorAll('[data-i18n-title]');
        titleElements.forEach(el => {
            const key = el.getAttribute('data-i18n-title');
            el.textContent = this.t(key);
        });
    }
    
    // 更新文档语言属性
    updateDocumentLang() {
        document.documentElement.lang = this.currentLang;
    }
    
    // 获取当前语言
    getCurrentLanguage() {
        return this.currentLang;
    }

    // 格式化消息时间：优先显示相对时间（刚刚、几分钟前），否则显示本地化时间
    formatMessageTime(date) {
        if (!date) return '';
        const d = (date instanceof Date) ? date : new Date(date);
        const now = new Date();
        const diffMs = now - d;
        const diffSec = Math.round(diffMs / 1000);
        const diffMin = Math.round(diffSec / 60);

        const lang = this.getCurrentLanguage();

        // 刚刚（小于 45 秒）
        if (diffSec < 45) {
            return this.t('justNow');
        }

        // 分钟级别（小于 60 分钟）
        if (diffMin < 60) {
            // 使用 Intl.RelativeTimeFormat 如果可用
            try {
                if (Intl && Intl.RelativeTimeFormat) {
                    const rtf = new Intl.RelativeTimeFormat(lang, { numeric: 'auto' });
                    return rtf.format(-diffMin, 'minute');
                }
            } catch (e) {
                // fallback to simple string
                return `${diffMin} ${this.t('minutesAgo') || '分钟前'}`;
            }
        }

        // 否则使用本地时间格式（仅显示时间或日期时间）
        try {
            const dtf = new Intl.DateTimeFormat(lang, { hour: '2-digit', minute: '2-digit' });
            return dtf.format(d);
        } catch (e) {
            return d.toLocaleTimeString();
        }
    }
}

// 导出多语言管理器
window.I18nManager = I18nManager;
