<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .search-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .log-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .result-container {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: #f9f9f9;
            margin-top: 10px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>🔧 搜索修复测试页面</h1>
    
    <div class="test-container">
        <h2>搜索功能测试</h2>
        <input type="text" class="search-input" id="testSearchInput" placeholder="输入搜索关键词，如：提现、账号、接单等...">
        <br>
        <button class="test-button" onclick="testSearch()">🔍 测试搜索</button>
        <button class="test-button" onclick="testInitialization()">🔧 检查初始化</button>
        <button class="test-button" onclick="clearLog()">🧹 清空日志</button>
        <button class="test-button" onclick="openMainApp()">📱 打开主应用</button>
        
        <div id="resultContainer" class="result-container">
            <em>搜索结果将在这里显示...</em>
        </div>
    </div>
    
    <div class="test-container">
        <h2>系统日志</h2>
        <div id="logOutput" class="log-output">等待测试开始...\n</div>
    </div>

    <script>
        let originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            debug: console.debug
        };
        
        function addToLog(level, ...args) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${level.toUpperCase()}: ${args.join(' ')}\n`;
            logOutput.textContent += message;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // 调用原始console方法
            originalConsole[level](...args);
        }
        
        // 拦截console输出
        console.log = (...args) => addToLog('log', ...args);
        console.error = (...args) => addToLog('error', ...args);
        console.warn = (...args) => addToLog('warn', ...args);
        console.debug = (...args) => addToLog('debug', ...args);
        
        function clearLog() {
            document.getElementById('logOutput').textContent = '日志已清空...\n';
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
        }
        
        function testInitialization() {
            console.log('🔧 开始检查初始化状态...');
            
            // 检查关键对象是否存在
            const checks = [
                { name: 'UnifiedSearchEngine', exists: typeof UnifiedSearchEngine !== 'undefined' },
                { name: 'SearchUIRenderer', exists: typeof SearchUIRenderer !== 'undefined' },
                { name: 'DataManager', exists: typeof DataManager !== 'undefined' },
                { name: 'window.app', exists: typeof window.app !== 'undefined' },
                { name: 'searchResults容器', exists: !!document.getElementById('searchResults') }
            ];
            
            checks.forEach(check => {
                console.log(`📦 ${check.name}: ${check.exists ? '✅ 存在' : '❌ 不存在'}`);
            });
            
            if (window.app) {
                console.log('🔍 应用对象状态:');
                console.log(`  - unifiedSearchEngine: ${!!window.app.unifiedSearchEngine ? '✅' : '❌'}`);
                console.log(`  - searchUIRenderer: ${!!window.app.searchUIRenderer ? '✅' : '❌'}`);
                console.log(`  - dataManager: ${!!window.app.dataManager ? '✅' : '❌'}`);
            }
        }
        
        function testSearch() {
            const query = document.getElementById('testSearchInput').value.trim();
            if (!query) {
                console.warn('⚠️ 请输入搜索关键词');
                return;
            }
            
            console.log(`🔍 开始测试搜索: "${query}"`);
            
            // 检查主应用是否已加载
            if (typeof window.app === 'undefined') {
                console.error('❌ 主应用未加载，请先打开主应用');
                return;
            }
            
            // 创建搜索结果容器（如果不存在）
            if (!document.getElementById('searchResults')) {
                const container = document.createElement('div');
                container.id = 'searchResults';
                container.style.display = 'none';
                document.body.appendChild(container);
                console.log('📦 创建临时搜索结果容器');
            }
            
            // 执行搜索
            try {
                console.log('🚀 调用搜索方法...');
                window.app.performSearch(query, true);
                
                setTimeout(() => {
                    console.log('⏰ 3秒后检查搜索结果...');
                    checkSearchResults(query);
                }, 3000);
                
            } catch (error) {
                console.error('❌ 搜索执行出错:', error);
            }
        }
        
        function checkSearchResults(query) {
            const resultContainer = document.getElementById('resultContainer');
            
            if (window.app && window.app.searchUIRenderer) {
                const currentResults = window.app.searchUIRenderer.getCurrentResults();
                const currentStage = window.app.searchUIRenderer.getCurrentStage();
                
                console.log(`📊 搜索状态: ${currentStage}`);
                console.log(`📊 结果数量: ${currentResults?.length || 0}`);
                
                if (currentResults && currentResults.length > 0) {
                    console.log('✅ 搜索结果获取成功！');
                    resultContainer.innerHTML = `
                        <h3>✅ 搜索结果 (${currentResults.length} 条)</h3>
                        <ul>
                            ${currentResults.slice(0, 3).map((result, index) => `
                                <li><strong>结果 ${index + 1}:</strong> ${result.title?.zh || result.title || '无标题'}</li>
                            `).join('')}
                            ${currentResults.length > 3 ? `<li><em>...还有 ${currentResults.length - 3} 条结果</em></li>` : ''}
                        </ul>
                    `;
                } else {
                    console.warn('⚠️ 未获取到搜索结果');
                    resultContainer.innerHTML = '<p>⚠️ 未获取到搜索结果，请检查日志</p>';
                }
            } else {
                console.error('❌ 搜索UI渲染器不存在');
                resultContainer.innerHTML = '<p>❌ 搜索系统未正确初始化</p>';
            }
        }
        
        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 搜索修复测试页面已加载');
            console.log('📝 使用说明:');
            console.log('   1. 点击"打开主应用"加载完整系统');
            console.log('   2. 在主应用中尝试搜索，观察是否显示结果');
            console.log('   3. 或在此页面直接测试搜索功能');
        });
    </script>
</body>
</html>