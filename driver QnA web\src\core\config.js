/*
 * 文件路径: src/core/config.js
 * 文件描述: 应用程序的核心配置文件，定义了全局配置项（如Gemini API、搜索、移动端设置）和通用工具函数。
 * 依赖关系:
 *   - 间接依赖: `local-env-loader.js` 或 `netlify-env-loader.js` (通过 `window.localEnvironmentLoader` 或 `window.netlifyEnvironmentLoader` 进行API密钥的异步初始化)。
 *   - 浏览器API: `localStorage` (用于配置持久化), `window` (全局对象)。
 *   - 无直接模块导入/导出依赖，通过全局 `window` 对象暴露接口。
 * 初始化时机:
 *   - 在 `index.html` 中作为第一个JavaScript文件加载。
 *   - `appConfig` 对象立即定义。
 *   - `initializeContentSecurityPolicy()` 在浏览器环境下立即执行。
 *   - API密钥的初始化 (`initializeSecureApiKey`) 会等待环境变量加载完成并触发 `environmentLoaded` 事件后执行。
 *   - 模块加载后，通过立即执行函数 (IIFE) 将 `appConfig` 及相关工具函数挂载到 `window` 对象上。
 * 暴露的全局变量/函数:
 *   - `window.CONFIG`: 应用程序的配置对象。
 *   - `window.saveConfig`: 保存当前配置到 `localStorage`。
 *   - `window.validateInput`: 输入验证工具函数。
 *   - `window.sanitizeError`: 错误信息清理工具函数。
 *   - `window.ApiRateLimiter`: API请求限流器类。
 *   - `window.initializeApiKeyWhenReady`: 供环境加载器调用的API密钥初始化函数。
 * 功能:
 *   - 集中管理应用配置，便于统一调整和维护。
 *   - 提供输入安全验证、错误处理和API限流等基础安全功能。
 *   - 动态加载和验证Gemini API密钥。
 */
// 环境配置文件
(function() {
    'use strict';
    
    // 创建配置对象
    const appConfig = {
        // Gemini API 配置
        gemini: {
            apiKey: null, // API密钥将从安全环境变量或服务端获取
            model: 'gemini-2.5-flash',
            endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/',
            enabled: false, // 默认禁用，需要有效密钥才能启用
            temperature: 0.1,
            maxTokens: 4000, // 增加token限制以获得更详细的响应
            maxRequestsPerMinute: 60, // API调用限流
            timeoutMs: 15000 // 相应增加请求超时时间
        },
        
        // 搜索配置
        search: {
            enableAI: true, // 默认启用AI搜索
            enableStreaming: true, // 启用流式搜索分析
            fallbackToTraditional: true, // 如果AI搜索失败，回退到传统搜索
            maxResults: 10,
            
            // 智能建议配置
            suggestions: {
                enabled: true, // 启用智能建议
                maxSuggestions: 8, // 最大建议数量
                maxHistory: 50, // 最大历史记录
                minSearchLength: 2, // 最小搜索长度
                showDefaultSuggestions: true, // 显示默认建议
                enableLearning: true // 启用学习功能
            },
            
            // 流式搜索特殊配置
            streaming: {
                enabled: true, // 流式搜索总开关
                debounceMs: 300, // 防抖延迟（从500ms优化到300ms）
                stages: {
                    basic: { timeoutMs: 2000 }, // 基础搜索超时
                    aiAnalysis: { timeoutMs: 5000 }, // AI分析超时
                    suggestions: { timeoutMs: 3000 }, // 建议生成超时
                    optimization: { timeoutMs: 4000 } // 结果优化超时
                },
                cache: {
                    maxSize: 50, // 缓存条目数
                    ttlMs: 300000 // 5分钟TTL
                },
                ui: {
                    animationDelay: 100, // 结果动画间隔（毫秒）
                    enableTouchFeedback: true, // 触摸反馈
                    showProgress: true, // 显示进度指示器
                    enableCancelGesture: true, // 支持手势取消
                    maxConcurrentRequests: 2 // 最大并发请求数
                }
            }
        },
        
        // 移动端配置
        mobile: {
            haptic: {
                enabled: true, // 启用触觉反馈
                patterns: {
                    selection: [10], // 选择反馈
                    impact: [50], // 冲击反馈
                    notification: [30, 50, 30] // 通知反馈
                }
            },
            gestures: {
                timeout: 300, // 手势超时时间
                longPress: {
                    timeout: 800, // 长按超时时间
                    enabled: true
                },
                swipe: {
                    threshold: 100, // 滑动阈值（像素）
                    enabled: true
                },
                doubleTap: {
                    timeout: 300, // 双击超时时间
                    enabled: true
                }
            },
            virtualKeyboard: {
                adaptUI: true, // 自动适配虚拟键盘
                hideNavigationOnOpen: true, // 键盘打开时隐藏底部导航
                threshold: 150, // 键盘检测阈值（像素）
                iosKeyboardFix: true, // iOS键盘修复
                androidKeyboardFix: true // Android键盘修复
            },
            safeArea: {
                enabled: true, // 启用安全区域支持
                adaptForNotch: true // 适配刘海屏
            },
            performance: {
                reducedMotion: false, // 减少动画（自动检测）
                touchOptimization: true, // 触摸优化
                disableUserZoom: false // 禁用用户缩放
            }
        },

        // 调试和测试配置
        debug: false, // 调试模式开关
        testing: {
            enabled: false, // 测试功能启用
            autoRunHealthCheck: false, // 自动运行健康检查
            performanceThresholds: {
                searchResponseTime: 2000,
                suggestionResponseTime: 500,
                uiRenderTime: 100,
                memoryUsageLimit: 100 * 1024 * 1024
            }
        }
    };

    // 浏览器环境下的配置处理
    if (typeof window !== 'undefined') {
        // 在客户端，我们从 localStorage 或默认值获取配置
        const savedConfig = localStorage.getItem('faq-config');
        if (savedConfig) {
            try {
                const parsed = JSON.parse(savedConfig);
                Object.assign(appConfig, parsed);
            } catch (e) {
                console.warn('Failed to parse saved config:', e);
            }
        }
        
        // 推迟API密钥初始化到环境变量加载完成后
        // 使用本地环境加载器 - 延迟检测以确保加载器已就绪
        function initializeEnvironmentLoader() {
            const envLoader = window.localEnvironmentLoader || window.netlifyEnvironmentLoader || window.environmentLoader;
            if (envLoader) {
                const applyConfig = () => {
                    try {
                        console.log('🔄 正在应用环境变量到CONFIG...');
                        envLoader.applyToConfig(appConfig);
                        console.log('✅ 环境变量已应用，当前状态:', {
                            apiKey: appConfig.gemini.apiKey ? '已设置' : '未设置',
                            enabled: appConfig.gemini.enabled
                        });
                        
                        // 重新初始化Gemini助手（如果应用已加载）
                        if (window.app && window.app.initializeGeminiAssistant) {
                            console.log('🔄 环境变量加载完成，重新初始化Gemini助手...');
                            window.app.initializeGeminiAssistant();
                        }
                    } catch (e) {
                        console.error('❌ 应用环境变量时出错:', e);
                    }
                    initializeSecureApiKey(appConfig);
                };

                if (envLoader.isLoaded) {
                    // 环境变量已加载，直接应用并初始化
                    console.log('📋 环境变量已加载，立即应用...');
                    applyConfig();
                } else {
                    // 等待环境变量加载事件
                    console.log('⏳ 等待环境变量加载...');
                    window.addEventListener('environmentLoaded', applyConfig);
                }
            } else {
                // 没有环境加载器，使用默认配置
                console.warn('⚠️ 未找到环境加载器，使用默认配置');
                initializeSecureApiKey(appConfig);
            }
        }

        // 延迟执行环境加载器初始化，确保加载器脚本已执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(initializeEnvironmentLoader, 0);
            });
        } else {
            setTimeout(initializeEnvironmentLoader, 0);
        }
    }

    // 保存配置到 localStorage
    function saveConfig() {
        if (typeof window !== 'undefined') {
            localStorage.setItem('faq-config', JSON.stringify(appConfig));
        }
    }

    // 安全的API密钥初始化函数
    // This function now relies on EnvironmentLoader to have already populated the config.
    async function initializeSecureApiKey(config) {
        // We just need to validate the result from the loader.
        if (config.gemini.apiKey && validateApiKey(config.gemini.apiKey)) {
            config.gemini.enabled = true;
            console.log('✅ API密钥已确认，AI功能已启用');
        } else {
            // If the key is still not here, it means all loading methods failed.
            console.warn('⚠️ 未找到有效的API密钥，AI功能已禁用');
            config.gemini.enabled = false;
        }
    }
    
    // API密钥验证函数
    function validateApiKey(key) {
        if (!key || typeof key !== 'string') return false;
        // Google API密钥格式验证
        if (!/^AIza[0-9A-Za-z_-]{35}$/.test(key)) return false;
        // 检查是否为示例密钥
        const examplePatterns = ['XXXXX', 'your-api-key', 'example', 'test', 'demo'];
        return !examplePatterns.some(pattern => key.toLowerCase().includes(pattern.toLowerCase()));
    }
    
    // 输入验证和清理函数
    function validateInput(input, options = {}) {
        const {
            maxLength = 1000,
            minLength = 0,
            allowedChars = null,
            stripHtml = true,
            trimWhitespace = true
        } = options;
        
        if (typeof input !== 'string') {
            throw new Error('输入必须是字符串类型');
        }
        
        let cleaned = input;
        
        // 去除首尾空白字符
        if (trimWhitespace) {
            cleaned = cleaned.trim();
        }
        
        // 长度验证
        if (cleaned.length < minLength) {
            throw new Error(`输入长度不能少于${minLength}个字符`);
        }
        if (cleaned.length > maxLength) {
            throw new Error(`输入长度不能超过${maxLength}个字符`);
        }
        
        // HTML清理（防止XSS）
        if (stripHtml) {
            cleaned = cleaned
                .replace(/<[^>]*>/g, '') // 移除HTML标签
                .replace(/javascript:/gi, '') // 移除javascript:协议
                .replace(/on\w+\s*=/gi, '') // 移除事件处理器
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&amp;/g, '&')
                .replace(/&quot;/g, '"')
                .replace(/&#x27;/g, "'");
        }
        
        // 字符白名单验证
        if (allowedChars) {
            const regex = new RegExp(`[^${allowedChars}]`, 'g');
            if (regex.test(cleaned)) {
                throw new Error('输入包含不允许的字符');
            }
        }
        
        return cleaned;
    }
    
    // 安全的错误信息处理
    function sanitizeError(error) {
        // 创建安全的错误对象，不暴露敏感信息
        const safeError = {
            message: '系统错误，请稍后重试',
            code: 'SYSTEM_ERROR',
            timestamp: new Date().toISOString()
        };
        
        // 开发环境下显示详细错误信息
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            safeError.debug = {
                originalMessage: error.message,
                stack: error.stack
            };
        }
        
        return safeError;
    }
    
    // API调用限流器
    class ApiRateLimiter {
        constructor(maxRequests = 60, timeWindowMs = 60000) {
            this.maxRequests = maxRequests;
            this.timeWindowMs = timeWindowMs;
            this.requests = [];
        }
        
        canMakeRequest() {
            const now = Date.now();
            // 清理过期的请求记录
            this.requests = this.requests.filter(time => now - time < this.timeWindowMs);
            
            return this.requests.length < this.maxRequests;
        }
        
        recordRequest() {
            if (this.canMakeRequest()) {
                this.requests.push(Date.now());
                return true;
            }
            return false;
        }
        
        getTimeUntilNextRequest() {
            if (this.canMakeRequest()) return 0;
            const oldestRequest = Math.min(...this.requests);
            return this.timeWindowMs - (Date.now() - oldestRequest);
        }
    }
    
    // 内容安全策略配置
    function initializeContentSecurityPolicy() {
        // 仅在支持CSP的环境中设置
        if ('SecurityPolicyViolationEvent' in window) {
            // 监听CSP违规事件
            document.addEventListener('securitypolicyviolation', (e) => {
                console.warn('CSP违规:', {
                    directive: e.violatedDirective,
                    source: e.sourceFile,
                    line: e.lineNumber
                });
            });
        }
        
        // 设置安全响应头（如果可以控制服务器）
        const meta = document.createElement('meta');
        meta.setAttribute('http-equiv', 'Content-Security-Policy');
        meta.setAttribute('content', [
            "default-src 'self'",
            "script-src 'self' 'unsafe-eval'", // 允许必要的eval调用，但已移除所有内联事件处理器
            "style-src 'self' 'unsafe-inline'", // 允许内联样式
            "img-src 'self' data: https:",
            "connect-src 'self' https://generativelanguage.googleapis.com",
            "font-src 'self'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ].join('; '));
        document.head.appendChild(meta);
    }
    
    // 导出配置和安全函数
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = {
            config: appConfig,
            validateInput,
            sanitizeError,
            ApiRateLimiter
        };
    } else if (typeof window !== 'undefined') {
        window.CONFIG = appConfig;
        window.saveConfig = saveConfig;
        window.validateInput = validateInput;
        window.sanitizeError = sanitizeError;
        window.ApiRateLimiter = ApiRateLimiter;
        
        // 初始化安全措施
        initializeContentSecurityPolicy();
        
        // 提供回调供环境加载器使用（避免过早初始化）
        window.initializeApiKeyWhenReady = function() {
            // 首先应用环境变量到配置
            const envLoader = window.localEnvironmentLoader || window.netlifyEnvironmentLoader || window.environmentLoader;
            if (envLoader) {
                try {
                    console.log('🔄 通过回调应用环境变量到CONFIG...');
                    envLoader.applyToConfig(appConfig);
                    console.log('✅ 环境变量已应用，当前状态:', {
                        apiKey: appConfig.gemini.apiKey ? '已设置' : '未设置',
                        enabled: appConfig.gemini.enabled
                    });
                } catch (e) {
                    console.error('❌ 应用环境变量时出错:', e);
                }
            }
            // 然后初始化API密钥
            initializeSecureApiKey(appConfig);
        };
    }

})();
