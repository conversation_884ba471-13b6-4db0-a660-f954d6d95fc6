{"ai_assistant_config": {"version": "5.0.0", "last_updated": "2025-09-01T00:00:00Z", "api_configuration": {"endpoints": {"knowledge_query": {"endpoint": "/api/v1/knowledge/query", "method": "POST", "description": "查询知识库获取相关信息"}, "booking_assistance": {"endpoint": "/api/v1/booking/assist", "method": "POST", "description": "AI助手预订协助"}, "customer_support": {"endpoint": "/api/v1/support/assist", "method": "POST", "description": "客户支持问题处理"}, "real_time_coordination": {"driver_location": "/api/v1/driver/location", "flight_status": "/api/v1/flight/status", "order_modification": "/api/v1/order/modify"}}, "authentication": {"api_key_required": true, "rate_limiting": "1000_requests_per_hour", "security": "JWT_token_based"}}, "vector_database": {"embedding_model": {"model_name": "text-embedding-3-large", "dimension": 3072, "max_tokens": 8192, "supported_languages": ["zh", "en", "ms", "ta"]}, "search_configuration": {"semantic_search": {"enabled": true, "similarity_threshold": 0.75, "max_results": 10}, "keyword_search": {"enabled": true, "fuzzy_matching": true, "synonym_expansion": true}, "ranking_algorithm": {"semantic_similarity": 0.5, "relevance_score": 0.3, "freshness": 0.1, "popularity": 0.1}}, "collections": {"company_policies": "公司政策和操作指南", "service_information": "服务详情和价格", "customer_interactions": "客户对话记录", "operational_procedures": "日常运营流程", "emergency_protocols": "紧急情况处理", "historical_data": "历史订单数据"}}, "high_frequency_optimization": {"scenario_detection": {"vehicle_capacity_keywords": ["车", "车型", "座位", "行李", "空间", "装", "载", "多少人", "几个行李"], "route_inquiry_keywords": ["多久", "多远", "路线", "时间", "路程", "距离", "几点到", "什么时候"], "pricing_keywords": ["多少钱", "费用", "收费", "价格", "贵", "便宜", "怎么算", "多少马币"], "upgrade_keywords": ["升级", "更好", "豪华", "高级", "VIP", "MPV", "换车", "更好"], "detection_accuracy": "95%+", "response_prioritization": "high_frequency_first"}, "optimized_responses": {"vehicle_selection": {"priority": "highest", "response_template": "基于您的{passengers}人和{luggage}个行李，推荐使用{recommended_vehicle}。{vehicle_details}。{additional_recommendations}", "quick_answers": {"4人4行李": "推荐MPV，标准轿车空间不足", "2人2行李": "经济型或标准轿车均可", "6人以上": "必须使用MPV或更大车型", "特殊物品": "根据物品类型推荐合适车型"}, "follow_up_questions": ["有特殊尺寸行李吗？", "对车型有偏好吗？", "预算范围是多少？"]}, "route_inquiry": {"priority": "high", "response_template": "从{origin}到{destination}：距离{distance}，行程{duration}，费用{price_range}。{traffic_notes} {recommendations}", "quick_answers": {"机场到市区": "55公里，45-60分钟，80-120马币", "机场到云顶": "85公里，90-120分钟，150-200马币", "机场到布城": "40公里，30-45分钟，60-90马币", "吉隆坡到新加坡": "350公里，4-5小时，600-800马币"}, "traffic_considerations": ["高峰期拥堵", "天气影响", "周末车流", "道路施工"]}, "pricing_inquiry": {"priority": "high", "response_template": "{service_type}费用：{base_price}。{additional_charges}。{calculation_example}。{tips}", "quick_answers": {"机场等待": "90分钟免费，超出每30分钟35马币", "中途停留": "15分钟免费，超出每30分钟35马币", "夜班服务": "晚上11点后附加20马币", "假日附加": "公共假日附加20%费用"}, "transparency_notes": "所有费用提前告知，无隐藏收费"}, "upgrade_inquiry": {"priority": "medium", "response_template": "升级选项：{upgrade_options}。{benefits}。{price_difference}。{recommendations}", "quick_answers": {"经济到标准": "+20-30马币，更舒适空间", "标准到MPV": "+40-60马币，7座空间", "MPV到豪华": "+80-120马币，VIP体验", "任何到面包车": "+100-200马币，超大空间"}, "value_propositions": ["更舒适", "更多空间", "更好服务", "更安全"]}}, "response_generation": {"parameters": {"temperature": 0.3, "max_tokens": 1000, "top_p": 0.9, "frequency_penalty": 0.1, "presence_penalty": 0.1}, "conversation_context": {"context_window_size": 10, "memory_retention": "persistent", "personalization_level": "high", "multi_language_support": true}, "knowledge_retrieval": {"retrieval_strategy": "hybrid", "min_relevance_score": 0.6, "max_sources": 5, "source_diversity": true, "cross_reference_enabled": true}}}, "integration_points": {"customer_service_chatbot": {"description": "客服聊天机器人集成", "features": ["多语言支持", "上下文理解", "预订流程引导", "问题升级处理"]}, "internal_staff_assistant": {"description": "内部员工AI助手", "features": ["政策查询", "操作流程指导", "问题诊断", "培训支持"]}, "voice_assistant": {"description": "语音助手集成", "features": ["语音识别", "自然语言理解", "语音合成", "实时响应"]}}, "performance_targets": {"response_time": {"query_processing": "<_100ms", "retrieval": "<_500ms", "response_generation": "<_2s", "end_to_end": "<_3s"}, "availability": {"uptime_sla": "99.9%", "disaster_recovery": "<_15分钟", "backup_frequency": "real_time"}, "scalability": {"concurrent_users": "10000+", "query_volume": "1M+_per_day", "storage_capacity": "100TB+"}}}}