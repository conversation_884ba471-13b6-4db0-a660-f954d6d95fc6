/*
 * 文件路径: src/styles/base/globals.css
 * 文件描述: 定义了应用于整个应用程序的全局样式，这些样式建立在CSS重置的基础上。它为 `html` 和 `body` 元素设置了基础字体大小、颜色和背景。此外，还包含了针对移动设备的响应式调整、可访问性工具以及用于页面布局和状态指示的通用工具类。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于在 `src/styles/tokens/colors.css` 中定义的CSS变量（`--text-body`, `--font-weight-normal`, `--text-primary`, `--background-color`, `--primary-200`, `--primary-900`, `--primary-600`, `--text-inverse`），以及在 `src/styles/tokens/spacing.css` 中定义的变量（`--header-height`, `--bottom-nav-height`, `--safe-left`, `--safe-right`, `--container-padding`），在 `src/styles/tokens/radius.css` 中定义的变量（`--radius-md`），以及在 `src/styles/tokens/transitions.css` 中定义的变量（`--transition-all`）。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 为整个应用程序建立一致的视觉基础。
 *   - 确保基本的响应性，并根据移动设备调整布局。
 *   - 提供基本的可访问性功能和用于常见UI模式的工具类。
 * 关键规则/部分:
 *   - `html`: 设置 `rem` 单位的基础 `font-size` (16px)，并启用平滑滚动。
 *   - `body`: 应用全局字体样式、文本颜色和背景颜色，使用CSS变量。设置 `min-height` 为 `100vh` 以填充整个视口高度，并设置 `overflow-x: hidden` 以防止水平滚动。
 *   - `@media (max-width: 767px)`: 定义针对移动设备的 `body` 内边距调整，包括头部/底部导航高度和安全区域插边。
 *   - `::selection`: 使用主色调令牌样式化选中文本的背景和颜色。
 *   - `.page-container`: 用于主要内容区域的工具类，确保最小高度和一致的内边距。
 *   - `.sr-only`: 一个可访问性工具类，用于视觉上隐藏内容，但仍可供屏幕阅读器访问。
 *   - `.skip-to-content`: 一个可访问性功能，允许键盘用户跳过重复导航，直接跳转到主要内容。
 *   - `.loading`: 一个工具类，用于指示加载状态（例如，降低不透明度）。
 *   - `.page-hidden`: 一个工具类，使用 `display: none !important;` 隐藏元素。
 * 使用约定:
 *   - 这些样式全局应用，或通过定义的工具类应用于特定元素。
 */
/* 基础样式 - 全局样式 */

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font: var(--text-body);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background-color: var(--background-color);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* 移动端专用样式 */
@media (max-width: 767px) {
  body {
    font-size: var(--font-sm);
    padding-top: var(--header-height);
    padding-bottom: var(--bottom-nav-height);
    padding-left: var(--safe-left);
    padding-right: var(--safe-right);
  }
}

/* 文字选择样式 */
::selection {
  background-color: var(--primary-200);
  color: var(--primary-900);
}

/* 页面基础布局 */
.page-container {
  min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
  padding: var(--container-padding);
}

/* 无障碍辅助 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 跳转到主内容的链接（无障碍） */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-600);
  color: var(--text-inverse);
  padding: 8px;
  border-radius: var(--radius-md);
  text-decoration: none;
  z-index: 1000;
  transition: var(--transition-all);
}

.skip-to-content:focus {
  top: 6px;
}

/* 加载状态 */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

/* 隐藏状态 */
.page-hidden {
  display: none !important;
}