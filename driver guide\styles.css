/* =========================
     Mobile-First Redesign
     Base = small screens
     Enhancements via min-width
     ========================= */

* { margin:0; padding:0; box-sizing:border-box; }

/* Ensure scrolling works */
html, body {
    overflow: auto !important;
    height: auto !important;
    max-height: none !important;
}

html { font-size:16px; scroll-behavior:smooth; overflow:auto; }
body { font-family:'Segoe UI', Tahoma, sans-serif; line-height:1.55; color:#222; background:#fafbfe; min-height:100vh; overflow:auto; }

/* Color system */
:root {
    --brand:#B02E9A;
    --brand-alt:#8B2485;
    --bg-card:#ffffff;
    --bg-soft:#f3f4fa;
    --border:#e2e6f2;
    --radius:14px;
    --shadow:0 4px 14px rgba(0,0,0,.08);
    --text:#222;
    --text-soft:#555;
    --gradient:linear-gradient(135deg,var(--brand) 0%, var(--brand-alt) 100%);
}

body.dark-mode {
    --bg-card:#2a2430;
    --bg-soft:#322b39;
    --border:#473e50;
    --shadow:0 4px 20px rgba(0,0,0,.5);
    --text:#ece6f1;
    --text-soft:#c9c0d2;
    background:#1e1823;
    color:var(--text);
}
body.dark-mode header { background:linear-gradient(135deg,#5e1a53,#44123d); }
body.dark-mode footer { background:linear-gradient(135deg,#5e1a53,#44123d); }
body.dark-mode #company-logo { background:#fff; }
body.dark-mode .content-section { border-color:#3d3443; }
body.dark-mode .workflow-box, body.dark-mode .points-system, body.dark-mode .waiting-time, body.dark-mode .timeout-process, body.dark-mode .auto-cancel-conditions, body.dark-mode .passenger-lost-process, body.dark-mode .evidence-requirements, body.dark-mode .evidence-completion, body.dark-mode .service-standards, body.dark-mode .reward-punishment, body.dark-mode .violation-penalties, body.dark-mode .serious-violations, body.dark-mode .penalty-standards, body.dark-mode .emergency-process, body.dark-mode .contact-info, body.dark-mode .appeal-flow, body.dark-mode .flight-tools, body.dark-mode .gps-camera-guide { background:linear-gradient(135deg,#342d3a,#3e3645); border-color:#4a4150; }
body.dark-mode #toc-list li { background:#342d3a; border-color:#4a4150; }
body.dark-mode #toc-list a { color:var(--text-soft); }
body.dark-mode #toc-list a.active { background:rgba(176,46,154,.25); color:#fff; }
body.dark-mode .lang-btn { border-color:rgba(255,255,255,.25); }
body.dark-mode .lang-btn.active { color:var(--brand); }
body.dark-mode #back-to-top { background:linear-gradient(135deg,#5e1a53,#44123d); }
body.dark-mode .checklist li { background:linear-gradient(135deg,#342d3a,#3e3645); }

/* Header Styles */
header { background:linear-gradient(135deg,var(--brand) 0%, var(--brand-alt) 100%); color:#fff; padding:.75rem .9rem; position:sticky; top:0; z-index:1000; box-shadow:0 2px 10px rgba(176,46,154,.25); }
.header-container { display:flex; flex-direction:column; gap:.75rem; max-width:100%; }
.logo { display:flex; align-items:center; justify-content:center; gap:.6rem; width:100%; }
#company-logo { width:90px; height:90px; object-fit:contain; border-radius:10px; background:#fff; padding:4px; flex-shrink:0; }
#site-title { font-size:1rem; font-weight:700; line-height:1.2; letter-spacing:.5px; text-align:center; }

/* Language Switcher */
/* Language & menu buttons (compact pill group) */
.language-switcher { display:flex; gap:.25rem; flex-wrap:wrap; justify-content:center; width:100%; }
.lang-btn { background:rgba(255,255,255,.18); border:1px solid rgba(255,255,255,.35); color:#fff; padding:.45rem .65rem; border-radius:16px; font-weight:600; font-size:.7rem; cursor:pointer; line-height:1; min-width:50px; min-height:34px; display:flex; align-items:center; justify-content:center; -webkit-tap-highlight-color:transparent; -webkit-user-select:none; user-select:none; transition:.25s; white-space:nowrap; position:relative; z-index:10; }
.lang-btn.active { background:#fff; color:var(--brand); box-shadow:0 2px 6px rgba(0,0,0,.18); }
.lang-btn:active, .lang-btn.touching { transform:scale(.95); }
.lang-btn:hover { background:rgba(255,255,255,.25); }
.menu-btn { font-size:.9rem; min-width:40px; }
.menu-btn { display:none !important; }

/* Navigation - Table of Contents */
/* TOC collapsible on mobile */
.table-of-contents { background:var(--bg-card); margin:.9rem .9rem 1.25rem; padding:1rem 1rem 1.25rem; border-radius:var(--radius); box-shadow:var(--shadow); border:1px solid var(--border); }
#toc-title { color:var(--brand); font-size:1.05rem; font-weight:700; margin-bottom:.85rem; cursor:pointer; display:flex; align-items:center; gap:.4rem; -webkit-user-select:none; user-select:none; }
#toc-title::after { content:'▾'; font-size:.9rem; transition:transform .35s; }
.table-of-contents.open #toc-title::after { transform:rotate(180deg); }
#toc-list { list-style:none; display:grid; grid-template-columns:1fr; gap:.55rem; max-height:0; overflow:hidden; transition:max-height .45s ease; }
.table-of-contents.open #toc-list { max-height:1600px; }
#toc-list li { background:var(--bg-soft); border:1px solid var(--border); border-radius:10px; }
#toc-list a { display:flex; padding:.75rem .9rem; font-size:.78rem; text-decoration:none; color:#222; font-weight:600; align-items:center; gap:.4rem; }
#toc-list a.active { background:rgba(176,46,154,.12); color:var(--brand); }
#toc-list a:active { background:rgba(176,46,154,.08); }

/* Main Content */
/* Content */
.content { padding:0 .9rem 3.5rem; }
/* Compact spacing adjustments (further tightened) */
.content-section { background:var(--bg-card); margin:.5rem 0; padding:.85rem .75rem .9rem; border-radius:var(--radius); box-shadow:var(--shadow); border:1px solid var(--border); }
.content-section h2 { color:var(--brand); font-size:1.22rem; margin:0 0 .55rem; padding-bottom:.3rem; border-bottom:2px solid var(--brand); font-weight:700; }
.content-section h3 { color:var(--brand-alt); font-size:.95rem; margin:.65rem 0 .4rem; font-weight:600; }
.content-section h4 { color:var(--brand); font-size:.9rem; margin:.55rem 0 .35rem; font-weight:600; }
.content-section h5 { color:var(--brand-alt); font-size:.8rem; margin:.45rem 0 .3rem; font-weight:600; }

#general-chat-img {
    max-width: 100%;
    height: auto;
    margin-top: 5px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: block;
}

/* Special Boxes */
/* Feature blocks */
.workflow-box, .points-system, .waiting-time, .timeout-process, .auto-cancel-conditions, .passenger-lost-process, .evidence-requirements, .evidence-completion, .service-standards, .reward-punishment, .violation-penalties, .serious-violations, .penalty-standards, .emergency-process, .contact-info, .appeal-flow, .flight-tools, .gps-camera-guide { background:linear-gradient(135deg,#f8f9ff 0%, #f0f2ff 100%); border:1px solid var(--border); border-radius:12px; padding:.7rem .75rem .8rem; margin:.55rem 0; position:relative; }

.workflow-box::before, .points-system::before, .waiting-time::before,
.timeout-process::before, .auto-cancel-conditions::before,
.passenger-lost-process::before, .evidence-requirements::before,
.evidence-completion::before, .service-standards::before,
.reward-punishment::before, .violation-penalties::before,
.serious-violations::before, .penalty-standards::before,
.emergency-process::before, .contact-info::before,
.appeal-flow::before, .flight-tools::before, .gps-camera-guide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #B02E9A, #8B2485);
    border-radius: 12px 12px 0 0;
}

/* Lists */
ul, ol { margin:.4rem 0 .5rem; padding-left:1.05rem; }
li { margin:.2rem 0; line-height:1.45; font-size:.8rem; }
li strong { color:var(--brand); }

/* Compact restructuring utilities */
.compact-block { padding:.6rem .6rem .7rem; }
.info-table { display:grid; width:100%; border:1px solid var(--border); border-radius:10px; overflow:hidden; background:var(--bg-soft); font-size:.7rem; }
.info-table.scrollable { overflow-x:auto; }
.info-table .it-row { display:grid; grid-template-columns:repeat(auto-fit,minmax(90px,1fr)); border-top:1px solid var(--border); }
.info-table .it-row > div { padding:.45rem .55rem; display:flex; align-items:center; gap:.25rem; }
.info-table .it-head { background:var(--brand); color:#fff; font-weight:600; border-top:none; }
.info-table .it-row:nth-child(even):not(.it-head) { background:rgba(0,0,0,.025); }
.info-table.responsive .it-row { grid-template-columns: repeat(auto-fit,minmax(70px,1fr)); }
.badge { display:inline-flex; align-items:center; padding:.25rem .5rem; border-radius:999px; font-size:.6rem; line-height:1; font-weight:600; background:var(--bg-soft); border:1px solid var(--border); color:var(--text-soft); }
.badge.ok { background:#e6f9ed; border-color:#b7e9c8; color:#1c7d3c; }
.badge.danger { background:#ffecec; border-color:#ffc4c4; color:#c92a2a; }
.badge.label { background:var(--brand); color:#fff; border-color:var(--brand-alt); }
.badge.pill { background:#fff; }
.badge-block { display:flex; flex-wrap:wrap; gap:.4rem; align-items:center; margin:.35rem 0; }
.badge-groups { display:flex; flex-direction:column; gap:.25rem; }
.raw-toggle { margin-top:.4rem; }
.raw-toggle summary { cursor:pointer; font-size:.65rem; color:var(--brand); font-weight:600; }
.raw-toggle[open] summary { opacity:.75; }
.raw-content { margin-top:.4rem; }
code { background:rgba(0,0,0,.05); padding:0 .3rem; border-radius:4px; font-size:.65rem; }

/* Grid Layouts */
.standards-grid, .mechanism-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.standard-item, .reward-section {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border: 2px solid #e0e6ff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.penalty-levels, .penalty-progression {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.penalty-level, .penalty-step {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #B02E9A;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.condition-group {
    margin: 1.5rem 0;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border-left: 3px solid #B02E9A;
}

.evidence-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.evidence-item {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e0e6ff;
}

.tool-section {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    margin: 1rem 0;
    border-left: 3px solid #B02E9A;
}

.gps-requirements {
    margin-top: 1.5rem;
}

/* Checklists */
.checklist {
    list-style: none;
    padding: 0;
}

.checklist li { background:linear-gradient(135deg,#f8f9ff 0%, #f0f2ff 100%); margin:.4rem 0; padding:.65rem .7rem .65rem 2.1rem; border-radius:8px; border-left:4px solid var(--brand); position:relative; font-size:.8rem; }
.checklist li::before { content:'□'; position:absolute; left:.75rem; color:var(--brand); font-size:1rem; font-weight:700; }

/* Footer */
footer { background:linear-gradient(135deg,var(--brand) 0%, var(--brand-alt) 100%); color:#fff; padding:1.6rem 0 2.2rem; margin-top:2.5rem; }
.footer-content { max-width:900px; margin:0 auto; padding:0 1.1rem; text-align:center; }
.footer-content p { margin:.4rem 0; line-height:1.4; font-size:.75rem; }
#footer-note { font-size:.8rem; margin-bottom:.7rem; }
#footer-company { font-weight:700; font-size:.9rem; }
#footer-date { font-style:italic; opacity:.85; }

/* Responsive Design */
/* Mobile adjustments */
@media (max-width:639px){
    .header-container { gap:.5rem; }
    .language-switcher { gap:.2rem; }
    .lang-btn { padding:.4rem .5rem; font-size:.65rem; min-width:45px; min-height:32px; }
    #site-title { font-size:.85rem; }
    #company-logo { width:75px; height:75px; }
}

/* Desktop / larger screens */
@media (min-width:640px){
    #site-title { font-size:1.4rem; }
    .content-section h2 { font-size:1.45rem; }
    li { font-size:.84rem; }
    #toc-list { grid-template-columns:repeat(auto-fit,minmax(220px,1fr)); }
    #toc-list a { font-size:.75rem; }
}
@media (min-width:900px){
    body { background:linear-gradient(135deg,#f5f7fa 0%, #c3cfe2 100%); }
    .content { max-width:1180px; margin:0 auto; padding:0 1.6rem 4rem; }
    .table-of-contents { max-width:1180px; margin:1.2rem auto 1.6rem; }
    .header-container { max-width:1180px; margin:0 auto; flex-direction:row; align-items:center; justify-content:space-between; }
    .logo { justify-content:flex-start; width:auto; flex:1; }
    .language-switcher { justify-content:flex-end; width:auto; }
    .content-section { padding:2rem 1.8rem 2.2rem; }
    .content-section h2 { font-size:1.8rem; }
    .content-section h3 { font-size:1.2rem; }
    .content-section h4 { font-size:1.05rem; }
    .content-section h5 { font-size:.95rem; }
    #toc-list a { font-size:.82rem; padding:.85rem 1rem; }
    #company-logo { width:135px; height:135px; }
    #site-title { font-size:1.4rem; text-align:left; }
    li { font-size:.9rem; }
    .checklist li { font-size:.85rem; }
    /* Remove forced expansion on desktop; keep collapsible behavior */
    .table-of-contents { position:relative; }
    /* Keep menu button visible on desktop for manual toggle */
    .menu-btn { display:inline-flex; }
}

/* Animation for smooth scrolling */
body.dark-mode a { color:#d6b4e3; }
body.dark-mode a:hover { color:#f2d9ff; }

/* Highlight animation for active sections */
.content-section:target {
    animation: highlight 2s ease-in-out;
}

@keyframes highlight {
    0% { background-color: rgba(176, 46, 154, 0.1); }
    50% { background-color: rgba(176, 46, 154, 0.2); }
    100% { background-color: white; }
}

/* Timeout highlight styles */
.highlight-time {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.highlight-charge {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.highlight-step {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.highlight-important {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    color: #856404;
    border: 1px solid #ffeaa7;
    animation: pulse 2s infinite;
}

.highlight-must {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    color: #721c24;
    border: 1px solid #f5c6cb;
    text-decoration: underline;
}

.timeout-judgment-note {
    background: linear-gradient(135deg, #e7f3ff, #d4edda);
    border: 2px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timeout-judgment-note p {
    font-weight: bold;
    color: #0c5460;
    margin-bottom: 10px;
}

.timeout-judgment-note ul {
    margin-left: 20px;
}

.timeout-judgment-note li {
    margin-bottom: 5px;
    line-height: 1.6;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Dark mode support for highlights */
body.dark-mode .highlight-time {
    background: linear-gradient(135deg, #4a3c1d, #5d4a26);
    color: #ffd700;
    border-color: #5d4a26;
}

body.dark-mode .highlight-charge {
    background: linear-gradient(135deg, #4a1c1c, #5d2626);
    color: #ff6b6b;
    border-color: #5d2626;
}

body.dark-mode .highlight-step {
    background: linear-gradient(135deg, #1a3d3d, #265050);
    color: #4ecdc4;
    border-color: #265050;
}

body.dark-mode .highlight-important {
    background: linear-gradient(135deg, #4a3c1d, #5d4a26);
    color: #ffd700;
    border-color: #5d4a26;
}

body.dark-mode .highlight-must {
    background: linear-gradient(135deg, #4a1c1c, #5d2626);
    color: #ff6b6b;
    border-color: #5d2626;
}

body.dark-mode .timeout-judgment-note {
    background: linear-gradient(135deg, #2a2a3d, #3d3d4a);
    border-color: #4a4a5d;
}

body.dark-mode .timeout-judgment-note p {
    color: #4ecdc4;
}

/* Flight time alert styles */
.flight-time-alert {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #f39c12;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.flight-time-alert h5 {
    color: #8b4513;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.flight-time-alert ul {
    margin-left: 20px;
}

.flight-time-alert li {
    margin-bottom: 8px;
    line-height: 1.6;
    color: #5d4a26;
}

body.dark-mode .flight-time-alert {
    background: linear-gradient(135deg, #4a3c1d, #5d4a26);
    border-color: #f39c12;
}

body.dark-mode .flight-time-alert h5 {
    color: #ffd700;
}

body.dark-mode .flight-time-alert li {
    color: #e6d7b3;
}

/* Address confirmation alert styles */
.address-confirmation-alert {
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    border: 2px solid #e17055;
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
    margin-bottom: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.address-confirmation-alert h5 {
    color: #d63031;
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 1em;
}

.address-confirmation-alert ul {
    margin-left: 15px;
}

.address-confirmation-alert li {
    margin-bottom: 6px;
    line-height: 1.5;
    color: #636e72;
    font-size: 0.9em;
}

body.dark-mode .address-confirmation-alert {
    background: linear-gradient(135deg, #4a3c1d, #5d2626);
    border-color: #e17055;
}

body.dark-mode .address-confirmation-alert h5 {
    color: #ff7675;
}

body.dark-mode .address-confirmation-alert li {
    color: #ddd;
}

/* Communication rules alert styles */
.communication-rules-alert {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    border: 3px solid #d63031;
    border-radius: 10px;
    padding: 15px;
    margin-top: 15px;
    box-shadow: 0 4px 12px rgba(214, 48, 49, 0.3);
    animation: urgent-pulse 2s infinite;
}

.communication-rules-alert h4 {
    color: #d63031;
    font-weight: bold;
    margin-bottom: 12px;
    font-size: 1.2em;
    text-align: center;
}

.communication-rules-alert ul {
    margin-left: 0;
    padding-left: 0;
}

.communication-rules-alert li {
    margin-bottom: 12px;
    line-height: 1.6;
    color: #2d3436;
    font-size: 0.95em;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    border-left: 4px solid #d63031;
}

.highlight-critical {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.9em;
}

.highlight-emergency {
    background: linear-gradient(135deg, #fd79a8, #fdcb6e);
    color: #d63031;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.9em;
    animation: emergency-flash 1.5s infinite;
}

.highlight-channel {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.9em;
}

@keyframes urgent-pulse {
    0% { transform: scale(1); box-shadow: 0 4px 12px rgba(214, 48, 49, 0.3); }
    50% { transform: scale(1.02); box-shadow: 0 6px 16px rgba(214, 48, 49, 0.5); }
    100% { transform: scale(1); box-shadow: 0 4px 12px rgba(214, 48, 49, 0.3); }
}

@keyframes emergency-flash {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

body.dark-mode .communication-rules-alert {
    background: linear-gradient(135deg, #4a1c1c, #5d2626);
    border-color: #ff7675;
}

body.dark-mode .communication-rules-alert h4 {
    color: #ff7675;
}

body.dark-mode .communication-rules-alert li {
    color: #ddd;
    background: rgba(74, 28, 28, 0.8);
}

body.dark-mode .highlight-critical {
    background: linear-gradient(135deg, #d63031, #e17055);
}

body.dark-mode .highlight-emergency {
    background: linear-gradient(135deg, #e17055, #fdcb6e);
    color: #ff7675;
}

body.dark-mode .highlight-channel {
    background: linear-gradient(135deg, #0984e3, #74b9ff);
}

/* Emergency contact note styles */
.emergency-contact-note {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    border: 2px solid #d63031;
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
    box-shadow: 0 2px 8px rgba(214, 48, 49, 0.2);
}

.emergency-contact-note h5 {
    color: #d63031;
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 1em;
}

.emergency-contact-note ul {
    margin-left: 15px;
}

.emergency-contact-note li {
    margin-bottom: 6px;
    line-height: 1.5;
    color: #636e72;
    font-size: 0.9em;
}

body.dark-mode .emergency-contact-note {
    background: linear-gradient(135deg, #4a1c1c, #5d2626);
    border-color: #ff7675;
}

body.dark-mode .emergency-contact-note h5 {
    color: #ff7675;
}

body.dark-mode .emergency-contact-note li {
    color: #ddd;
}

/* Loading animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Print styles */
@media print { header,.language-switcher,.table-of-contents,#menu-toggle { display:none !important; } body { background:#fff; } .content-section { box-shadow:none; border:1px solid #ccc; margin:1rem 0; break-inside:avoid; } }

/* Menu open button active state */
#menu-toggle[aria-expanded="true"] { background:#fff; color:var(--brand); }

/* Back to top button */
#back-to-top { position:fixed; right:.85rem; bottom:.95rem; width:46px; height:46px; border:none; border-radius:50%; background:linear-gradient(135deg,var(--brand) 0%, var(--brand-alt) 100%); color:#fff; font-size:1.1rem; font-weight:700; cursor:pointer; display:flex; align-items:center; justify-content:center; box-shadow:0 4px 14px rgba(0,0,0,.25); opacity:0; pointer-events:none; transition:.35s; z-index:1100; }
#back-to-top.visible { opacity:1; pointer-events:auto; }
#back-to-top:active { transform:scale(.88); }

/* Reading progress */
#reading-progress { position:fixed; top:0; left:0; height:4px; width:0; background:linear-gradient(90deg,var(--brand),var(--brand-alt)); z-index:1200; transition:width .15s ease-out; }
body.dark-mode #reading-progress { background:linear-gradient(90deg,#78306b,#5a214e); }

/* Command palette */
#command-palette { position:fixed; inset:0; background:rgba(20,16,25,.55); -webkit-backdrop-filter:blur(6px); backdrop-filter:blur(6px); display:flex; align-items:flex-start; justify-content:center; padding:5vh 1rem 2rem; z-index:1300; }
#command-palette[hidden] { display:none; }
.cp-panel { width:clamp(300px,720px,70vw); background:var(--bg-card); border:1px solid var(--border); border-radius:16px; box-shadow:0 12px 50px -10px rgba(0,0,0,.4); display:flex; flex-direction:column; max-height:80vh; }
body.dark-mode .cp-panel { border-color:#3d3443; }
.cp-header { display:flex; align-items:center; justify-content:space-between; padding:.9rem 1.1rem .4rem; }
#cp-title { font-size:.95rem; font-weight:600; color:var(--brand); }
#cp-close { background:none; border:none; cursor:pointer; font-size:1rem; line-height:1; color:var(--text-soft); }
#cp-input { margin:.35rem 1.1rem .7rem; padding:.65rem .75rem; border:1px solid var(--border); border-radius:8px; font-size:.85rem; background:var(--bg-soft); color:inherit; }
body.dark-mode #cp-input { border-color:#4a4150; }
#cp-results { list-style:none; margin:0; padding:.2rem 0 .5rem; overflow:auto; flex:1; }
#cp-results li { padding:.55rem 1rem; cursor:pointer; font-size:.8rem; display:flex; align-items:center; gap:.5rem; color:var(--text-soft); }
#cp-results li strong { color:var(--brand); }
#cp-results li:hover, #cp-results li.active { background:rgba(176,46,154,.12); color:var(--brand); }
body.dark-mode #cp-results li:hover, body.dark-mode #cp-results li.active { background:rgba(176,46,154,.25); color:#fff; }
.cp-hint { padding:.55rem 1rem .8rem; font-size:.65rem; text-transform:uppercase; letter-spacing:.5px; opacity:.7; }

/* Desktop only visibility */
.desktop-only { display:none; }
@media (min-width:900px){ .desktop-only { display:inline-flex; } }

/* Collapse toggle visual state */
#collapse-toggle[data-state="collapsed"] { background:#fff; color:var(--brand); }
body.dark-mode #collapse-toggle[data-state="collapsed"] { background:#3e3645; color:#fff; }