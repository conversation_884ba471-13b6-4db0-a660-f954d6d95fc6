/*
 * 文件路径: .claude/settings.local.json
 * 文件描述: 此JSON文件配置了Claude AI助手的本地设置，特别是定义了其执行shell命令和访问网络搜索的权限。它列出了允许（以及可能拒绝或需要询问）的命令，表明了Claude在此项目环境中可以执行的操作范围。
 *
 * 深度追踪记录:
 *
 * 1.  **用途**:
 *     *   **安全配置**: 限制Claude AI可以执行的操作，防止意外或恶意行为。
 *     *   **权限管理**: 明确列出Claude被允许使用的shell命令和工具（如 `WebSearch`）。
 *     *   **本地开发设置**: 这些是 `local.json` 设置，表明它们是开发者本地环境特有的，不应提交到版本控制或部署到生产环境。
 *
 * 2.  **关键部分**:
 *     *   `permissions`: 定义权限的根对象。
 *         *   `allow`: 允许的命令/工具数组。模式指示了各种 `Bash` 命令（例如，`rg`、`findstr`、`dir`、`node`、`curl`、`rm`、`cat`、`del`、`cp`、`mkdir`、`mv`）和 `WebSearch`。
 *         *   `deny`: 明确拒绝的命令数组（在此示例中为空）。
 *         *   `ask`: 需要明确用户确认才能执行的命令数组（在此示例中为空）。
 *
 * 3.  **使用约定**:
 *     *   此文件通常由AI助手的本地配置管理，不属于应用程序的核心代码库。它通常被排除在版本控制之外。
 */
{
  "permissions": {
    "allow": [
      "Bash(.\\tools\\rg:*)",
      "Bash(findstr:*)",
      "Bash(.\\rg:*)",
      "Bash(.\\rg:*)",
      "Bash(.\\rg:*)",
      "WebSearch",
      "Bash(dir:*)",
      "Bash(node:*)",
      "Bash(curl:*)",
      "Bash(rg:*)",
      "Bash(start index.html)",
      "Bash(del test-fix.js)",
      "Bash(rm:*)",
      "Bash(cat:*)",
      "Bash(del:*)",
      "Bash(copy app.js app.js.backup)",
      "Bash(cp:*)",
      "Bash(start simple-index.html)",
      "Bash(mkdir:*)",
      "Bash(mv:*)"
    ],
    "deny": [],
    "ask": []
  }
}