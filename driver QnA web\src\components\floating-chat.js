/*
 * 文件路径: src/components/floating-chat.js
 * 文件描述: 定义了 `FloatingChatWindow` 类，用于创建和管理一个可拖动、可调整大小的浮动聊天界面。该聊天窗口与应用程序的FAQ数据和Gemini AI助手集成，为用户查询提供智能响应。
 *
 * 深度追踪记录:
 *
 * 1.  **构造函数 (`constructor(appInstance)`)**:
 *     - **输入**: 接收 `appInstance` (通常是 `src/core/app.js` 的实例)，将其存储为 `this.app`，这是与主应用通信的关键引用。
 *     - **初始化**: 初始化内部状态变量 (`isVisible`, `isDragging`, `isResizing`, `dragStartX`, `dragStartY`, `initialX`, `initialY`, `initialWidth`, `initialHeight`) 和 `windowConfig` (定义窗口的默认和限制尺寸)。
 *     - **控制流**: 立即调用 `initialize()` 方法，启动浮窗的创建和设置过程。
 *
 * 2.  **初始化 (`initialize()`)**:
 *     - **控制流**: 依次调用 `createWindow()` (创建DOM结构), `setupEventListeners()` (设置事件监听器), `loadWindowState()` (从本地存储加载状态)。
 *
 * 3.  **创建窗口DOM结构 (`createWindow()`)**:
 *     - **逻辑**: 动态创建一个 `div` 元素作为浮窗的容器 (`this.container`)。
 *     - **数据流**: 使用模板字符串 (`innerHTML`) 填充浮窗的内部HTML结构，包括标题栏、消息显示区、输入区和调整大小手柄。
 *     - **控制流**: 将创建的容器添加到 `document.body` 中，使其在页面上可见（初始为隐藏）。
 *
 * 4.  **设置事件监听器 (`setupEventListeners()`)**:
 *     - **职责**: 为浮窗的各个交互元素（标题栏、输入框、发送按钮、关闭/最小化按钮、调整大小手柄）绑定事件监听器。
 *     - **拖拽事件**: 
 *         - `header.addEventListener('mousedown', this.startDrag.bind(this))`: 当鼠标在标题栏按下时，触发 `startDrag`。
 *         - `document.addEventListener('mousemove', this.handleMouseMove.bind(this))`: 全局监听鼠标移动，用于实时更新窗口位置。
 *         - `document.addEventListener('mouseup', this.handleMouseUp.bind(this))`: 全局监听鼠标释放，结束拖拽。
 *     - **调整大小事件**: 
 *         - `resizeHandles.forEach(handle => handle.addEventListener('mousedown', this.startResize.bind(this)))`: 当鼠标在调整大小手柄按下时，触发 `startResize`。
 *         - `document.addEventListener('mousemove', this.handleMouseMove.bind(this))`: 同拖拽事件，用于实时更新窗口大小。
 *         - `document.addEventListener('mouseup', this.handleMouseUp.bind(this))`: 同拖拽事件，结束调整大小。
 *     - **消息发送**: 
 *         - `sendBtn.addEventListener('click', () => this.sendMessage())`: 点击发送按钮触发 `sendMessage`。
 *         - `input.addEventListener('keydown', (e) => { if (e.key === 'Enter') this.sendMessage(); })`: 在输入框按回车键触发 `sendMessage`。
 *     - **窗口控制**: 
 *         - `closeBtn.addEventListener('click', () => this.hide())`: 关闭按钮触发 `hide`。
 *         - `minimizeBtn.addEventListener('click', () => this.toggleMinimize())`: 最小化按钮触发 `toggleMinimize`。
 *
 * 5.  **拖拽和调整大小逻辑 (`startDrag`, `startResize`, `handleMouseMove`, `handleMouseUp`, `keepInViewport`)**:
 *     - **状态管理**: 使用 `isDragging` 和 `isResizing` 布尔值来控制拖拽和调整大小的状态。
 *     - **坐标计算**: 记录鼠标按下时的初始坐标 (`dragStartX`, `dragStartY`) 和窗口的初始位置/大小 (`initialX`, `initialY`, `initialWidth`, `initialHeight`)。
 *     - **实时更新**: `handleMouseMove` 根据鼠标的位移量 (`dx`, `dy`) 实时更新窗口的 `left`, `top`, `width`, `height` 样式。
 *     - **边界检查**: `handleMouseMove` 包含边界检查逻辑，确保窗口不会拖出可视区域。
 *     - **视图内保持 (`keepInViewport`)**: 在调整大小后，确保窗口仍在可视区域内，防止部分内容超出屏幕。
 *     - **状态保存**: `handleMouseUp` 在拖拽或调整大小结束后，调用 `saveWindowState()` 将当前窗口状态保存到 `localStorage`。
 *
 * 6.  **消息发送与AI回复处理 (`sendMessage`, `addMessage`, `processAIResponse`)**:
 *     - **`sendMessage()`**:
 *         - **数据流**: 获取用户在输入框中输入的文本 `message`。
 *         - **控制流**: 调用 `addMessage(message, 'user')` 将用户消息显示在聊天窗口中，清空输入框，然后调用 `processAIResponse(message)` 启动AI回复流程。
 *     - **`addMessage(content, type)`**:
 *         - **逻辑**: 动态创建新的消息DOM元素，根据 `type` (user/bot) 添加相应的CSS类。
 *         - **数据流**: 将 `content` 插入到消息体中，并添加当前时间戳。
 *         - **控制流**: 将新消息添加到 `messagesContainer`，并自动滚动到底部，确保最新消息可见。
 *     - **`processAIResponse(message)`**:
 *         - **控制流**: 
 *             - 调用 `showTypingIndicator()` 显示“正在输入”提示。
 *             - 异步调用 `searchAndAnalyzeWithGemini(message)` 获取AI回复。
 *             - 成功获取回复后，调用 `hideTypingIndicator()` 隐藏提示，并调用 `addMessage(reply, 'bot')` 显示AI回复。
 *             - 包含错误处理，如果AI回复失败，则显示通用错误消息。
 *
 * 7.  **搜索与AI分析 (`searchAndAnalyzeWithGemini`, `generateGeminiResponse`, `formatAIResponse`, `generateBasicFAQResponse`)**:
 *     - **`searchAndAnalyzeWithGemini(query)`**:
 *         - **数据流**: 接收用户 `query`。
 *         - **控制流**: 
 *             - 调用 `this.app.dataManager.searchQuestions(query)` 进行静默搜索，获取与查询相关的FAQ数据。
 *             - **条件分支**: 
 *                 - 如果 `searchResults` 为空，返回一个引导用户尝试其他关键词或联系客服的提示。
 *                 - 如果 `this.app.geminiAssistant` 存在且可用，则调用 `generateGeminiResponse()` 利用AI生成专业回答。
 *                 - 否则，回退到 `generateBasicFAQResponse()` 生成一个基于本地FAQ数据的基本回复。
 *     - **`generateGeminiResponse(results, originalQuery)`**:
 *         - **数据流**: 接收 `searchResults` 和 `originalQuery`。
 *         - **控制流**: 调用 `this.app.geminiAssistant.generateFAQBasedResponse(originalQuery, results.slice(0, 3), 'zh')`，将相关FAQ内容作为上下文传递给Gemini AI，以生成更精准的回答。
 *         - 调用 `formatAIResponse()` 对AI的原始回答进行美化。
 *     - **`formatAIResponse(response, results)`**:
 *         - **逻辑**: 对AI的原始文本回复进行格式化，例如添加表情符号、确保友好的开头，并引用相关FAQ的ID作为来源。
 *     - **`generateBasicFAQResponse(results, originalQuery)`**:
 *         - **逻辑**: 当AI不可用或AI回答失败时的备用方案，根据搜索结果的数量（单个或多个）生成不同格式的基本FAQ回复。
 *
 * 8.  **窗口状态持久化 (`saveWindowState`, `loadWindowState`)**:
 *     - **数据流**: 
 *         - `saveWindowState()`: 将浮窗的 `isVisible`, `left`, `top`, `width`, `height` 等状态保存到 `localStorage` 中，键名为 `floatingChatWindowState`。
 *         - `loadWindowState()`: 在初始化时从 `localStorage` 读取 `floatingChatWindowState`，并尝试恢复浮窗的可见性、位置和大小。
 *     - **控制流**: 确保用户关闭或刷新页面后，浮窗能保持其上次的状态。
 *
 * 9.  **最小化功能 (`toggleMinimize()`)**:
 *     - **逻辑**: 切换浮窗容器的 `minimized` CSS类，实现最小化/最大化效果，并调用 `saveWindowState()` 保存状态。
 *
 * 10. **销毁 (`destroy()`)**:
 *     - **逻辑**: 从DOM中移除浮窗容器，释放资源。
 *
 * 模块间数据与控制流:
 *   - **`app.js` -> `FloatingChatWindow`**: `app.js` 实例化 `FloatingChatWindow` 并传入自身引用 (`this`)。
 *   - **`FloatingChatWindow` -> `app.js`**: `FloatingChatWindow` 通过其内部的 `this.app` 引用，直接调用 `app.dataManager.searchQuestions` 进行静默搜索，并访问 `app.geminiAssistant` 来进行AI交互。
 *   - **`FloatingChatWindow` -> `localStorage`**: `FloatingChatWindow` 使用 `localStorage` 来持久化和加载其UI状态。
 *   - **`FloatingChatWindow` -> DOM**: `FloatingChatWindow` 直接操作DOM来创建、显示、隐藏和更新聊天窗口的UI元素。
 *
 * 这个更深度的追踪记录详细阐述了 `floating-chat.js` 如何作为一个独立的UI组件，通过与主应用 (`app.js`) 的紧密协作，提供一个功能丰富且用户友好的AI聊天界面。
 */
// GoMyHire 浮窗对话系统
// 可拖动、可调整大小、基于FAQ数据库的智能对话助手

class FloatingChatWindow {
    constructor(appInstance) {
        this.app = appInstance;
        this.isVisible = false;
        this.isDragging = false;
        this.isResizing = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.initialX = 0;
        this.initialY = 0;
        this.initialWidth = 0;
        this.initialHeight = 0;
        
        // 默认窗口设置
        this.windowConfig = {
            width: 400,
            height: 500,
            minWidth: 300,
            minHeight: 400,
            maxWidth: 600,
            maxHeight: 700
        };
        
        this.initialize();
    }
    
    // 初始化浮窗
    initialize() {
        this.createWindow();
        this.setupEventListeners();
        this.loadWindowState();
        console.log('💬 浮窗对话系统初始化完成');
    }
    
    // 创建浮窗DOM结构
    createWindow() {
        this.container = document.createElement('div');
        this.container.className = 'floating-chat-window';
        this.container.style.display = 'none';
        
    const i18n = this.app?.i18n;
    const t = (key) => (i18n?.t ? i18n.t(key) : key);
    this.container.innerHTML = `
            <!-- 窗口标题栏 -->
            <div class="chat-window-header" id="chatWindowHeader">
                <div class="window-title">
                    <span class="title-icon">🤖</span>
            <span class="title-text" data-i18n="chatAssistantTitle">${t('chatAssistantTitle')}</span>
                </div>
                <div class="window-controls">
            <button class="control-btn minimize-btn" data-i18n-title-attr="chatMinimize" title="${t('chatMinimize')}">−</button>
            <button class="control-btn close-btn" data-i18n-title-attr="chatClose" title="${t('chatClose')}">×</button>
                </div>
            </div>
            
            <!-- 消息容器 -->
            <div class="chat-messages-container" id="chatMessages">
                <div class="welcome-message">
                    <div class="message bot-message">
                        <div class="avatar"></div>
                        <div class="message-body">
                            <div class="message-content" data-i18n="chatWelcome">
                                ${t('chatWelcome')}
                            </div>
                            <div class="message-meta">
                <span class="message-time">${i18n?.formatMessageTime ? i18n.formatMessageTime(new Date()) : new Date().toLocaleTimeString()}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 输入区域 -->
            <div class="chat-input-area">
                <div class="input-container">
            <input type="text" class="chat-input" id="floatingChatInput" data-i18n-placeholder="chatInputPlaceholder" placeholder="${t('chatInputPlaceholder')}">
                    <button class="send-btn" id="floatingChatSend">
                        <span class="send-icon">📤</span>
                    </button>
                </div>
            </div>
            
            <!-- 调整大小手柄 -->
            <div class="resize-handle resize-handle-se"></div>
            <div class="resize-handle resize-handle-s"></div>
            <div class="resize-handle resize-handle-e"></div>
        `;
        
        document.body.appendChild(this.container);
    }
    
    // 设置事件监听器
    setupEventListeners() {
        const header = this.container.querySelector('.chat-window-header');
        const input = this.container.querySelector('.chat-input');
        const sendBtn = this.container.querySelector('.send-btn');
        const closeBtn = this.container.querySelector('.close-btn');
        const minimizeBtn = this.container.querySelector('.minimize-btn');
        const resizeHandles = this.container.querySelectorAll('.resize-handle');
    const messagesContainer = this.container.querySelector('.chat-messages-container');
        
        // 拖拽事件
        header.addEventListener('mousedown', this.startDrag.bind(this));
        
        // 调整大小事件
        resizeHandles.forEach(handle => {
            handle.addEventListener('mousedown', this.startResize.bind(this));
        });
        
        // 发送消息
        sendBtn.addEventListener('click', () => this.sendMessage());
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });
        
        // 窗口控制
        closeBtn.addEventListener('click', () => this.hide());
        minimizeBtn.addEventListener('click', () => this.toggleMinimize());
        
        // 全局事件
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));

        // 消息内链接事件委托（支持点击跳转到FAQ详情）
        messagesContainer.addEventListener('click', (e) => {
            console.debug('🔗 [FloatingChat] 检测到点击事件:', {
                target: e.target.tagName,
                className: e.target.className,
                hasClosest: !!e.target.closest('.chat-faq-link')
            });
            
            const link = e.target.closest('.chat-faq-link');
            if (link) {
                e.preventDefault();
                const id = link.getAttribute('data-faq-id');
                console.debug('🔗 [FloatingChat] 找到FAQ链接:', {
                    id: id,
                    hasShowQuestionDetail: !!this.app?.showQuestionDetail
                });
                
                if (id && this.app?.showQuestionDetail) {
                    console.debug('🔗 [FloatingChat] 调用showQuestionDetail:', id);
                    this.app.showQuestionDetail(id);
                } else {
                    console.warn('🔗 [FloatingChat] 无法跳转 - ID或方法缺失:', {
                        id: id,
                        hasMethod: !!this.app?.showQuestionDetail
                    });
                }
            }
        });
    }
    
    // 显示浮窗
    show() {
        this.container.style.display = 'flex';
        this.isVisible = true;
        this.container.querySelector('.chat-input').focus();
        this.saveWindowState();
    }
    
    // 隐藏浮窗
    hide() {
        this.container.style.display = 'none';
        this.isVisible = false;
        this.saveWindowState();
    }
    
    // 切换显示状态
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    // 开始拖拽
    startDrag(e) {
        if (e.target.closest('.control-btn')) return;
        
        this.isDragging = true;
        this.dragStartX = e.clientX;
        this.dragStartY = e.clientY;
        this.initialX = parseInt(this.container.style.left || 0);
        this.initialY = parseInt(this.container.style.top || 0);
        
        this.container.style.cursor = 'grabbing';
        this.container.style.userSelect = 'none';
    }
    
    // 开始调整大小
    startResize(e) {
        this.isResizing = true;
        this.dragStartX = e.clientX;
        this.dragStartY = e.clientY;
        this.initialWidth = this.container.offsetWidth;
        this.initialHeight = this.container.offsetHeight;
        this.initialX = parseInt(this.container.style.left || 0);
        this.initialY = parseInt(this.container.style.top || 0);
        
        e.preventDefault();
    }
    
    // 处理鼠标移动
    handleMouseMove(e) {
        if (this.isDragging) {
            const dx = e.clientX - this.dragStartX;
            const dy = e.clientY - this.dragStartY;
            
            let newX = this.initialX + dx;
            let newY = this.initialY + dy;
            
            // 边界检查
            newX = Math.max(0, Math.min(newX, window.innerWidth - this.container.offsetWidth));
            newY = Math.max(0, Math.min(newY, window.innerHeight - this.container.offsetHeight));
            
            this.container.style.left = newX + 'px';
            this.container.style.top = newY + 'px';
        }
        
        if (this.isResizing) {
            const dx = e.clientX - this.dragStartX;
            const dy = e.clientY - this.dragStartY;
            
            let newWidth = this.initialWidth + dx;
            let newHeight = this.initialHeight + dy;
            
            // 限制最小和最大尺寸
            newWidth = Math.max(this.windowConfig.minWidth, Math.min(newWidth, this.windowConfig.maxWidth));
            newHeight = Math.max(this.windowConfig.minHeight, Math.min(newHeight, this.windowConfig.maxHeight));
            
            this.container.style.width = newWidth + 'px';
            this.container.style.height = newHeight + 'px';
            
            // 保持窗口在可视区域内
            this.keepInViewport();
        }
    }
    
    // 处理鼠标释放
    handleMouseUp() {
        if (this.isDragging || this.isResizing) {
            this.isDragging = false;
            this.isResizing = false;
            this.container.style.cursor = '';
            this.container.style.userSelect = '';
            this.saveWindowState();
        }
    }
    
    // 保持窗口在可视区域内
    keepInViewport() {
        const rect = this.container.getBoundingClientRect();
        
        if (rect.right > window.innerWidth) {
            this.container.style.left = (window.innerWidth - rect.width) + 'px';
        }
        
        if (rect.bottom > window.innerHeight) {
            this.container.style.top = (window.innerHeight - rect.height) + 'px';
        }
        
        if (rect.left < 0) {
            this.container.style.left = '0px';
        }
        
        if (rect.top < 0) {
            this.container.style.top = '0px';
        }
    }
    
    // 发送消息
    async sendMessage() {
        const input = this.container.querySelector('.chat-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        // 添加用户消息
        this.addMessage(message, 'user');
        input.value = '';
        
        // 处理AI回复
        await this.processAIResponse(message);
    }
    
    // 添加消息到聊天窗口
    addMessage(content, type, relatedQuestions = []) {
        const messagesContainer = this.container.querySelector('.chat-messages-container');
        const messageDiv = document.createElement('div');
        
        messageDiv.className = `message ${type}-message`;
        const i18n = this.app?.i18n;

        // 生成相关问题按钮HTML
        const relatedHTML = relatedQuestions.map(q => {
            // 兼容两种结构：直接问题对象 或 包含 { question: {...} } 的包裹对象
            const item = q && q.question ? q.question : q;
            const title = (item && item.title && (item.title[this.app.currentLanguage] || item.title.zh || item.title.en)) || `问题 ${item?.id || ''}`;
            const questionId = item?.id || '';
            return `<button class="chat-faq-link" data-faq-id="${questionId}">${title}</button>`;
        }).join('');

        messageDiv.innerHTML = `
            <div class="avatar"></div>
            <div class="message-body">
                <div class="message-content">${content}</div>
                ${relatedHTML ? `<div class="related-questions-container">${relatedHTML}</div>` : ''}
                <div class="message-meta">
                    <span class="message-time">${i18n?.formatMessageTime ? i18n.formatMessageTime(new Date()) : new Date().toLocaleTimeString()}</span>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // 处理AI回复（基于FAQ数据库）
    async processAIResponse(message) {
        console.debug('🎯 [FloatingChat] 开始处理AI回复:', { message });
        
        // 显示正在输入状态
        this.showTypingIndicator();
        
        try {
            // 自动检测语言
            const detector = this.app?.geminiAssistant?.detectLanguage?.bind(this.app.geminiAssistant);
            const langDetected = detector ? detector(message) : { language: (this.app.currentLanguage || 'zh'), isMixed: false };
            console.debug('🎯 [FloatingChat] 语言检测完成:', langDetected);

            // 模糊/不可理解检测，直接返回澄清提示
            const ambiguity = this.app?.geminiAssistant?.detectAmbiguity?.(message) || { ambiguous: false, reasons: [] };
            console.debug('🎯 [FloatingChat] 模糊性检测完成:', ambiguity);
            
            if (ambiguity.ambiguous) {
                console.debug('🎯 [FloatingChat] 检测到模糊查询，返回澄清提示');
                const clarify = this.app?.geminiAssistant?.generateClarificationPrompt?.(ambiguity.reasons, langDetected.language) || (this.app?.i18n?.t ? this.app.i18n.t('chatErrorGeneric') : '请提供更具体的信息，以便我更好地帮助您。');
                this.hideTypingIndicator();
                this.addMessage(clarify, 'bot');
                return;
            }

            console.debug('🎯 [FloatingChat] 开始调用searchAndAnalyzeWithGemini...');
            const reply = await this.searchAndAnalyzeWithGemini(message, langDetected.language);
            console.debug('🎯 [FloatingChat] searchAndAnalyzeWithGemini调用完成:', {
                hasReply: !!reply,
                replyType: typeof reply,
                replyStructure: reply ? Object.keys(reply) : []
            });
            
            // 移除输入指示器并显示回复
            this.hideTypingIndicator();
            
            // 检查回复是否为对象且包含内容
            if (typeof reply === 'object' && reply.content) {
                this.addMessage(reply.content, 'bot', reply.related);
            } else if (typeof reply === 'string') {
                this.addMessage(reply, 'bot');
            } else {
                // 如果回复格式不正确，显示通用错误
                const t = this.app?.i18n?.t?.bind(this.app.i18n);
                this.addMessage(t ? t('chatErrorGeneric') : '抱歉，处理您的请求时出现了问题。请稍后再试。', 'bot');
            }
            
        } catch (error) {
            console.error('AI回复处理错误:', error);
            this.hideTypingIndicator();
            const t = this.app?.i18n?.t?.bind(this.app.i18n);
            this.addMessage(t ? t('chatErrorGeneric') : '抱歉，处理您的请求时出现了问题。请稍后再试。', 'bot');
        }
    }
    
    // 搜索FAQ数据库并调用Gemini进行专业分析
    async searchAndAnalyzeWithGemini(query, language = 'zh') {
        try {
            // 1. 静默搜索FAQ数据库
            console.debug('🔍 [FloatingChat] 开始静默搜索FAQ数据库...');
            const searchParams = { 
                query: `"${query}"`, 
                queryLength: query.length,
                queryTrimmed: `"${query.trim()}"`,
                language: language || this.app.currentLanguage || 'zh' 
            };
            console.debug('🔍 [FloatingChat] 搜索参数:', searchParams);
            console.log('🔍 [FloatingChat] 搜索参数详情:', JSON.stringify(searchParams, null, 2));
            
            // 使用与主搜索栏相同的搜索方法，确保结果一致性
            const basicResults = this.app.basicSearch(query);
            
            // 转换为标准搜索结果格式，兼容现有处理逻辑
            const searchResults = {
                success: true,
                query: query,
                language: language || this.app.currentLanguage || 'zh',
                phase: 'local',
                results: basicResults.map(question => ({
                    question: question,
                    score: 1, // 基础搜索没有评分，使用默认值
                    matchedTerms: [query], // 简化的匹配词
                    categoryId: question.category,
                    searchPhase: 'local'
                })),
                totalResults: basicResults.length,
                searchTime: 0,
                cached: false
            };
            
            console.debug('🔍 [FloatingChat] 使用主搜索逻辑，结果数量:', basicResults.length);
            
            const searchResultInfo = {
                hasResults: !!searchResults,
                resultType: typeof searchResults,
                resultStructure: searchResults ? Object.keys(searchResults) : [],
                resultsCount: searchResults?.results?.length || (Array.isArray(searchResults) ? searchResults.length : 0),
                queryFromResult: searchResults?.query,
                searchPhase: searchResults?.phase,
                cached: searchResults?.cached,
                firstResultTitle: searchResults?.results?.[0]?.question?.title || searchResults?.[0]?.question?.title || 'N/A'
            };
            
            console.debug('🔍 [FloatingChat] 搜索完成，结果:', searchResultInfo);
            console.log('🔍 [FloatingChat] 搜索结果详情:', JSON.stringify(searchResultInfo, null, 2));
            
            if (searchResults?.results?.length > 0) {
                console.log('🔍 [FloatingChat] 前3个搜索结果:', JSON.stringify(
                    searchResults.results.slice(0, 3).map(r => ({
                        id: r.question?.id,
                        title: r.question?.title,
                        score: r.score,
                        matchedTerms: r.matchedTerms
                    })), null, 2
                ));
            }

            // 2. 检查是否有Gemini助手可用
            console.log('🔍 检查Gemini助手状态:', {
                hasGeminiAssistant: !!this.app.geminiAssistant,
                isAvailable: this.app.geminiAssistant?.isAvailable(),
                geminiConfig: {
                    enabled: window.CONFIG?.gemini?.enabled,
                    hasApiKey: !!(window.CONFIG?.gemini?.apiKey)
                }
            });
            
            if (this.app.geminiAssistant && this.app.geminiAssistant.isAvailable()) {
                console.log('✅ 使用Gemini生成回复...');
                // 即使没有本地搜索结果，也尝试让AI回答，AI或许能提供通用建议
                return await this.generateGeminiResponse(searchResults || [], query, language);
            }

            console.log('⚠️ Gemini不可用，使用基础FAQ回复...');
            // 3. 如果没有Gemini，根据搜索结果生成基础回复
            // 确保searchResults是数组格式（兼容新旧API格式）
            let resultsArray = searchResults;
            if (searchResults && typeof searchResults === 'object' && searchResults.results) {
                // 如果是新API格式（包含success, query等属性的对象）
                resultsArray = searchResults.results;
            }
            
            // 确保resultsArray是数组
            if (!Array.isArray(resultsArray)) {
                resultsArray = [];
            }
            
            if (!resultsArray || resultsArray.length === 0) {
                // 无本地结果且无AI时的引导
                const t = this.app?.i18n?.t?.bind(this.app.i18n);
                return t ? t('chatNoResultsFallback') : `关于"${query}"，我没有找到相关的FAQ信息。建议您尝试其他关键词或联系客服。`;
            }
            return this.generateBasicFAQResponse(resultsArray, query, language);

        } catch (error) {
            console.error('搜索分析失败:', error);
            const t = this.app?.i18n?.t?.bind(this.app.i18n);
            return t ? t('chatErrorGeneric') : '抱歉，处理您的请求时出现了问题。请稍后再试。';
        }
    }
    
    // 使用Gemini生成专业客服回答（增强提示词整合）
    async generateGeminiResponse(results, originalQuery, language = 'zh') {
        try {
            // 确保results是数组格式（兼容新旧API格式）
            let searchResults = results;
            if (results && typeof results === 'object' && results.results) {
                // 如果是新API格式（包含success, query等属性的对象）
                searchResults = results.results;
            }

            // 确保searchResults是数组
            if (!Array.isArray(searchResults)) {
                console.warn('搜索结果格式异常，使用空数组:', searchResults);
                searchResults = [];
            }

            console.debug('🤖 [FloatingChat] 准备调用Gemini，传递的搜索结果:', {
                originalQuery,
                searchResultsCount: searchResults.length,
                searchResultsSample: searchResults.slice(0, 3).map(q => ({
                    id: q.id,
                    title: q.title,
                    category: q.category
                })),
                language
            });

            // 调用Gemini进行分析和回答，直接返回其响应对象
            const response = await this.app.geminiAssistant.generateFAQBasedResponse(originalQuery, searchResults.slice(0, 3), language);

            console.debug('🤖 [FloatingChat] Gemini响应接收完成:', {
                hasResponse: !!response,
                responseType: typeof response,
                responseStructure: response ? Object.keys(response) : []
            });

            return response || this.generateBasicFAQResponse(searchResults, originalQuery, language);
            
        } catch (error) {
            console.warn('Gemini分析失败，使用基础回复:', error);
            // 回退时也确保传入的是数组
            const searchResults = Array.isArray(results) ? results : (results && results.results && Array.isArray(results.results) ? results.results : []);
            return this.generateBasicFAQResponse(searchResults, originalQuery, language);
        }
    }
    
    // 美化AI回答显示格式
    formatAIResponse(response, results, language = 'zh') {
        if (!response) return '';

        // 确保results是数组格式（兼容新旧API格式）
        let searchResults = results;
        if (results && typeof results === 'object' && results.results) {
            // 如果是新API格式（包含success, query等属性的对象）
            searchResults = results.results;
        }

        // 确保searchResults是数组
        if (!Array.isArray(searchResults)) {
            console.warn('搜索结果格式异常，使用空数组:', searchResults);
            searchResults = [];
        }

        // 兼容对象/字符串两种返回格式
        const extractText = (val) => {
            if (val == null) return '';
            if (typeof val === 'string') return val;
            if (Array.isArray(val)) return val.map(extractText).filter(Boolean).join('\n');
            if (typeof val === 'object') {
                if (typeof val.content === 'string') return val.content;
                if (val.content != null) return extractText(val.content);
                // 常见Gemini结构兜底
                if (val.candidates && val.candidates[0]) {
                    const c = val.candidates[0];
                    const t = c?.content?.parts?.[0]?.text || c?.content?.text || c?.text;
                    if (t) return String(t);
                }
                try {
                    return JSON.stringify(val);
                } catch (_) {
                    return String(val);
                }
            }
            return String(val);
        };

        let formattedResponse = extractText(response).trim();

        // 将AI回复中的FAQ编号转换为可点击链接
        formattedResponse = this.convertFAQNumbersToLinks(formattedResponse);

        // 清洗和优化回复内容
        formattedResponse = this.cleanResponseContent(formattedResponse);

        // 确保回答以友好的语气开始
        const lower = formattedResponse.toLowerCase();
        const startsZh = formattedResponse.startsWith('您好') || formattedResponse.startsWith('你好');
        const startsEn = lower.startsWith('hi') || lower.startsWith('hello');
        const startsMs = lower.startsWith('hai') || lower.startsWith('halo');
        if (!startsZh && !startsEn && !startsMs && !formattedResponse.startsWith('💡')) {
            formattedResponse = `💡 ${formattedResponse}`;
        }

        // 添加资料来源引用
        const refs = searchResults.slice(0, 3).map(r => {
            const id = r.id || r.question?.id;
            return id ? `<a href="#" class="chat-faq-link" data-faq-id="${id}">#${id}</a>` : '';
        }).filter(Boolean).join(', ');
        if (refs && !formattedResponse.includes('相关FAQ来源') && !formattedResponse.includes('Related FAQs') && !formattedResponse.includes('FAQ berkaitan')) {
            if (language === 'en') {
                formattedResponse += `\n\n📋 Related FAQs: ${refs}`;
            } else if (language === 'ms') {
                formattedResponse += `\n\n📋 FAQ berkaitan: ${refs}`;
            } else {
                formattedResponse += `\n\n📋 相关FAQ来源: ${refs}`;
            }
        }

        return formattedResponse;
    }
    
    // 将AI回复中的FAQ编号转换为可点击链接
    convertFAQNumbersToLinks(text) {
        // 匹配FAQ编号模式：FC-XX-XX, FC-XXX-XX 等
        const faqPattern = /\b(FC-[A-Z]{2,3}-\d{2})\b/g;
        
        // 替换FAQ编号为可点击链接
        return text.replace(faqPattern, (match, faqId) => {
            return `<a href="#" class="chat-faq-link" data-faq-id="${faqId}" title="点击查看FAQ详情">${faqId}</a>`;
        });
    }

    // 清洗和优化AI回复内容
    cleanResponseContent(text) {
        if (!text || typeof text !== 'string') return text;

        let cleaned = text;

        // 1. 移除Markdown格式符号（更强力的清洗）
        // 移除粗体标记 **text** -> text
        cleaned = cleaned.replace(/\*\*(.*?)\*\*/g, '$1');
        // 移除斜体标记 *text* -> text  
        cleaned = cleaned.replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '$1');
        // 移除代码标记 `code` -> code
        cleaned = cleaned.replace(/`([^`\n]+?)`/g, '$1');
        // 移除链接标记 [text](url) -> text
        cleaned = cleaned.replace(/\[([^\]\n]+?)\]\([^)\n]+?\)/g, '$1');
        // 移除下划线强调 _text_ -> text
        cleaned = cleaned.replace(/(?<!_)_([^_\n]+?)_(?!_)/g, '$1');
        
        // 2. 移除标题标记和其他符号
        cleaned = cleaned.replace(/^#{1,6}\s+/gm, ''); // # ## ### 等标题标记
        cleaned = cleaned.replace(/^\s*[\-\=]{3,}\s*$/gm, ''); // 分隔线
        cleaned = cleaned.replace(/^\s*[\*]{3,}\s*$/gm, ''); // 星号分隔线
        
        // 3. 清理列表项标记
        cleaned = cleaned.replace(/^[\*\-\+]\s+/gm, '• '); // 统一项目符号
        cleaned = cleaned.replace(/^(\d+)\.\s+/gm, '$1. '); // 保持数字列表格式
        
        // 4. 移除多余的符号和空白
        cleaned = cleaned.replace(/^[\*#\-_=\+]+\s*/gm, ''); // 行首符号
        cleaned = cleaned.replace(/\s+[\*#\-_=\+]+\s*$/gm, ''); // 行尾符号
        cleaned = cleaned.replace(/^\s*[\*#\-_=\+\|]+\s*$/gm, ''); // 整行符号
        
        // 5. 清理空白字符和重复内容
        cleaned = cleaned.replace(/\n\s*\n\s*\n+/g, '\n\n'); // 多空行压缩
        cleaned = cleaned.replace(/[ \t]+/g, ' '); // 多空格压缩
        cleaned = cleaned.replace(/^\s+|\s+$/gm, ''); // 行首尾空格
        
        // 6. 移除残留的Markdown符号
        cleaned = cleaned.replace(/[~]{1,2}([^~\n]+?)[~]{1,2}/g, '$1'); // 删除线
        cleaned = cleaned.replace(/\^([^\s\^]+)\^/g, '$1'); // 上标
        cleaned = cleaned.replace(/~([^\s~]+)~/g, '$1'); // 下标
        
        // 7. 规范化标点符号
        cleaned = cleaned.replace(/([。！？])([^"\s\n])/g, '$1 $2'); // 句号后加空格
        cleaned = cleaned.replace(/([，：；])([^\s\n])/g, '$1 $2'); // 逗号冒号后加空格
        cleaned = cleaned.replace(/([。！？]){2,}/g, '$1'); // 重复标点
        cleaned = cleaned.replace(/([，：；]){2,}/g, '$1');
        
        // 8. 最终清理
        cleaned = cleaned.trim();
        
        // 9. 调试输出（临时添加）
        if (text !== cleaned) {
            console.debug('🧹 [FloatingChat] 内容清洗完成:', {
                原始长度: text.length,
                清洗后长度: cleaned.length,
                原始预览: text.substring(0, 100),
                清洗后预览: cleaned.substring(0, 100)
            });
        }
        
        return cleaned;
    }    
    // 生成基础FAQ回复（无AI时使用）
    generateBasicFAQResponse(results, originalQuery, language = 'zh') {
        const t = this.app?.i18n?.t?.bind(this.app.i18n);
        const lang = language || this.app.currentLanguage || 'zh';

        if (!results || results.length === 0) {
            return t ? t('chatNoResultsFallback') : `关于"${originalQuery}"，我没有找到相关的FAQ信息。建议您尝试其他关键词或联系客服。`;
        }

        let responseText = t ? t('chatBasicResponseHeader') : `关于"${originalQuery}"，我找到了以下相关信息：\n\n`;
        
        // 兼容输入为问题对象数组或包装为 { question: {...} } 的数组
        const normalized = results.map(item => item?.question ? item.question : item);
        
        const related = normalized.slice(0, 3).map(q => {
            const title = (q.title && (q.title[lang] || q.title.zh || q.title.en)) || q.id || '';
            responseText += `- **${title}** (编号: ${q.id})\n`;
            return q; // 返回纯问题对象，便于渲染点击跳转
        });

        responseText += `\n${t ? t('chatBasicResponseFooter') : '您可以点击下方按钮查看详情，或继续提问。'}`;

        return {
            type: 'basic',
            content: responseText,
            language: lang,
            related: related
        };
    }
    
    // 显示正在输入指示器
    showTypingIndicator() {
        const messagesContainer = this.container.querySelector('.chat-messages-container');
        const typingDiv = document.createElement('div');
        
        typingDiv.className = 'message bot-message typing';
        typingDiv.innerHTML = `
            <div class="avatar"></div>
            <div class="message-body">
                <div class="message-content">
                    <div class="typing-dots">
                        <span></span><span></span><span></span>
                    </div>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(typingDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        this.typingIndicator = typingDiv;
    }
    
    // 隐藏正在输入指示器
    hideTypingIndicator() {
        if (this.typingIndicator && this.typingIndicator.parentNode) {
            this.typingIndicator.parentNode.removeChild(this.typingIndicator);
        }
    }
    
    // 保存窗口状态
    saveWindowState() {
        if (!this.isVisible) return;
        
        const state = {
            visible: this.isVisible,
            left: this.container.style.left,
            top: this.container.style.top,
            width: this.container.style.width,
            height: this.container.style.height
        };
        
        localStorage.setItem('floatingChatWindowState', JSON.stringify(state));
    }
    
    // 加载窗口状态
    loadWindowState() {
        try {
            const saved = localStorage.getItem('floatingChatWindowState');
            if (saved) {
                const state = JSON.parse(saved);
                
                if (state.visible) {
                    this.container.style.left = state.left || '20px';
                    this.container.style.top = state.top || '20px';
                    this.container.style.width = state.width || this.windowConfig.width + 'px';
                    this.container.style.height = state.height || this.windowConfig.height + 'px';
                }
            }
        } catch (error) {
            console.warn('加载窗口状态失败:', error);
        }
    }
    
    // 切换最小化状态
    toggleMinimize() {
        this.container.classList.toggle('minimized');
        this.saveWindowState();
    }
    
    // 销毁浮窗
    destroy() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
    }
}
