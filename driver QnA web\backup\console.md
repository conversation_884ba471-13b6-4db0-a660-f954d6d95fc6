data.js:25729 DataManager initialized with unified category system
app.js:11 📋 DataManager初始化完成
app.js:13 📊 可用分类数量: 6
app.js:14 📊 分类列表: (6) ['technical', 'financial', 'service', 'registration', 'communication', 'emergency']
app.js:22 ✅ 分类系统验证通过
app.js:36 ✅ Gemini助手已初始化，API连接将在首次使用时测试
app.js:343 ✅ 全局错误处理器设置完成
unified-search-engine.js:345 UnifiedSearchEngine initialized
unified-search-engine.js:359 初始化高性能RAG向量搜索引擎...
rag-vector-engine.js:426 高性能RAG向量搜索引擎初始化
rag-vector-engine.js:427 配置: {maxResults: 15, similarityThreshold: 0.05, enableCache: true, cacheSize: 100, enableParallelProcessing: true, …}
rag-vector-engine.js:432 从FAQ数据初始化向量引擎...
rag-vector-engine.js:182 构建高性能词汇表...
rag-vector-engine.js:224 高性能词汇表构建完成: 980 个词汇, 11ms
rag-vector-engine.js:466 并行向量化文档...
rag-vector-engine.js:740 并行向量化处理 94 个文档...
app.js:63 ✅ 统一搜索引擎初始化成功
search-ui-renderer.js:26 SearchUIRenderer initialized
app.js:73 ✅ 搜索UI渲染器初始化成功
app.js:89 ✅ 流式搜索引擎初始化成功 (兼容模式)
app.js:103 ✅ 搜索降级策略管理器初始化成功
performance-optimizer.js:31 ✅ 性能优化器初始化完成
app.js:126 ✅ 性能优化器初始化成功
mobile-interaction.js:43 📱 初始化移动端交互管理器...
mobile-interaction.js:63 ✅ 移动端交互管理器初始化完成
app.js:143 ✅ 移动端交互管理器初始化成功
smart-suggestions.js:38 ✅ 智能搜索建议管理器初始化完成
app.js:165 ✅ 智能搜索建议管理器初始化成功
mobile-optimization.js:43 📱 移动端优化管理器初始化完成 {isIOS: false, isAndroid: false, isMobile: false, isTablet: false, isSafari: false, …}
app.js:183 ✅ 移动端优化管理器初始化成功
system-validator.js:35 🧪 系统验证器初始化完成
app.js:198 ✅ 系统验证器初始化成功
app.js:213 ⚠️ PerformanceBenchmarkManager未加载
app.js:521 🚀 开始初始化FAQ应用...
mobile-interaction.js:109 📱 长按监听器已添加
mobile-interaction.js:123 📱 双击监听器已添加
mobile-interaction.js:137 📱 滑动监听器已添加
mobile-interaction.js:137 📱 滑动监听器已添加
mobile-interaction.js:137 📱 滑动监听器已添加
app.js:1997 ✅ 移动端交互功能设置完成
mobile-optimization.js:58 ✅ 移动端优化设置完成
app.js:2397 ✅ 移动端优化功能设置完成
app.js:524 ✅ 事件监听器设置完成
app.js:1506 ✅ 渲染了 6 个分类卡片到欢迎页面
app.js:531 ✅ 快速开始按钮渲染完成
app.js:534 ✅ 欢迎页面显示完成
app.js:537 ✅ 返回顶部按钮设置完成
app.js:540 ✅ 国际化文本更新完成
app.js:1544 ⚠️ 问题不存在: 登录
showQuestion @ app.js:1544
handleUrlParams @ app.js:1787
init @ app.js:543
FAQApp @ app.js:216
initializeApp @ app.js:3977
app.js:544 ✅ URL参数处理完成
app.js:546 🎉 FAQ应用初始化成功！
app.js:3979 FAQ App initialized successfully
app.js:2402 ⌨️ 移动端键盘打开: 0px
mobile-interaction.js:817 📱 触觉反馈被跳过：等待用户首次交互
mobile-optimization.js:170 ⌨️ 键盘状态变化: 打开 高度: 0px
rag-vector-engine.js:756 向量化进度: 50/94
rag-vector-engine.js:760 并行向量化完成
rag-vector-engine.js:845 执行向量内存压缩...
rag-vector-engine.js:857 向量压缩完成
rag-vector-engine.js:487 RAG向量引擎初始化完成: 94 个文档, 81ms
unified-search-engine.js:380 ✅ RAG向量搜索引擎初始化成功: {success: true, documentsCount: 94, vocabularySize: 980, initTime: 81}
app.js:2420 ⌨️ 移动端键盘关闭
mobile-optimization.js:170 ⌨️ 键盘状态变化: 关闭 高度: 0px
app.js:248 🧠 RAG向量搜索引擎初始化完成
app.js:253 📊 RAG统计: 94 个文档, 980 个词汇
app.js:283 🔄 预加载常用向量查询...
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:604 RAG高性能语义搜索: "登录问题" -> 3 个结果, 2ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "支付问题" -> 3 个结果, 1ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "接单流程" -> 3 个结果, 1ms
data.js:26387 🚀 GoMyHire FAQ 统一分类系统已加载
data.js:26388 📋 可用组件:
data.js:26389   - DataManager: 主数据管理器
data.js:26390   - EnhancedDataManager: 增强数据管理器
data.js:26391   - EnhancedSearchManager: 增强搜索管理器
data.js:26392   - CategoryAdapter: 分类适配器
data.js:26393   - unifiedCategorySystem: 统一分类系统
data.js:26394   - unifiedSearchTags: 统一搜索标签
data.js:26364 🔍 快速分类映射检查...
data.js:26372 📊 FAQ问题中使用的分类ID:
data.js:26378   service -> service ✅
data.js:26378   communication -> communication ✅
data.js:26378   technical -> technical ✅
data.js:26378   registration -> registration ✅
data.js:26378   emergency -> emergency ✅
data.js:26378   financial -> financial ✅
data.js:26256 🔍 开始验证统一分类系统...
data.js:25729 DataManager initialized with unified category system
data.js:26263 
📋 检查FAQ问题分类映射:
data.js:26269 📊 FAQ问题中使用的分类ID:
data.js:26273   service -> service ✅
data.js:26273   communication -> communication ✅
data.js:26273   technical -> technical ✅
data.js:26273   registration -> registration ✅
data.js:26273   emergency -> emergency ✅
data.js:26273   financial -> financial ✅
data.js:26279 
📊 验证结果:
data.js:26280 ✅ 总分类数: 6
data.js:26281 ✅ 总问题数: 94
data.js:26282 ✅ 已映射问题: 94
data.js:26283 ❌ 未映射问题: 0
data.js:26286 🎉 统一分类系统验证通过！
data.js:26304 
📋 可用分类:
data.js:26309   📱 技术问题 (technical): 23 个问题
data.js:26309   💰 财务问题 (financial): 6 个问题
data.js:26309   🛎️ 服务流程 (service): 33 个问题
data.js:26309   🚗 注册入门 (registration): 14 个问题
data.js:26309   💬 沟通管理 (communication): 7 个问题
data.js:26309   🚨 紧急处理 (emergency): 11 个问题
data.js:26313 
📈 分类统计:
data.js:26318   📱 技术问题: 23 个问题
data.js:26318   💰 财务问题: 6 个问题
data.js:26318   🛎️ 服务流程: 33 个问题
data.js:26318   🚗 注册入门: 14 个问题
data.js:26318   💬 沟通管理: 7 个问题
data.js:26318   🚨 紧急处理: 11 个问题
data.js:26404 💡 提示: 如需测试搜索功能，请手动调用 testSearchFunctionality()
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "提现失败" -> 3 个结果, 1ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "login issue" -> 1 个结果, 1ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:604 RAG高性能语义搜索: "payment problem" -> 3 个结果, 1ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "order flow" -> 3 个结果, 1ms
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "withdrawal failed" -> 2 个结果, 5ms
app.js:1283 开始预加载常用搜索...
unified-search-engine.js:945 开始预加载 15 个常用查询...
data.js:25848 Quick search completed in 0ms, found 0 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 2ms, found 0 results
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.05过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.015
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "如何接单" -> 3 个结果, 2ms
unified-search-engine.js:954 预加载完成 (1/15): "如何接单" -> 3 个结果
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "masalah log masuk" -> 1 个结果, 1ms
data.js:25848 Quick search completed in 1ms, found 3 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 2ms, found 5 results
rag-vector-engine.js:604 RAG高性能语义搜索: "提现" -> 2 个结果, 2ms
unified-search-engine.js:954 预加载完成 (2/15): "提现" -> 5 个结果
data.js:25848 Quick search completed in 1ms, found 2 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 2ms, found 15 results
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:604 RAG高性能语义搜索: "账号" -> 4 个结果, 4ms
unified-search-engine.js:954 预加载完成 (3/15): "账号" -> 15 个结果
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:604 RAG高性能语义搜索: "masalah pembayaran" -> 3 个结果, 1ms
data.js:25848 Quick search completed in 0ms, found 6 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 0ms, found 15 results
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.05过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.015
rag-vector-engine.js:806 📊 RAG搜索调试: 返回Top3最相似结果作为fallback
rag-vector-engine.js:604 RAG高性能语义搜索: "车辆" -> 3 个结果, 2ms
unified-search-engine.js:954 预加载完成 (4/15): "车辆" -> 17 个结果
rag-vector-engine.js:766 使用并行相似度计算...
rag-vector-engine.js:793 📊 RAG搜索调试: 阈值0.2过滤后0个结果
rag-vector-engine.js:796 📊 RAG搜索调试: 尝试放宽阈值到0.06
rag-vector-engine.js:604 RAG高性能语义搜索: "aliran pesanan" -> 3 个结果, 1ms
app.js:301 ✅ 向量查询预加载完成
data.js:25848 Quick search completed in 0ms, found 3 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 1ms, found 15 results
rag-vector-engine.js:604 RAG高性能语义搜索: "客服" -> 5 个结果, 1ms
unified-search-engine.js:954 预加载完成 (5/15): "客服" -> 19 个结果
data.js:25848 Quick search completed in 0ms, found 0 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 18ms, found 15 results
rag-vector-engine.js:604 RAG高性能语义搜索: "how to receive orders" -> 7 个结果, 18ms
unified-search-engine.js:954 预加载完成 (6/15): "how to receive orders" -> 21 个结果
data.js:25848 Quick search completed in 0ms, found 3 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 3ms, found 3 results
rag-vector-engine.js:604 RAG高性能语义搜索: "withdrawal" -> 3 个结果, 4ms
unified-search-engine.js:954 预加载完成 (7/15): "withdrawal" -> 4 个结果
app.js:2402 ⌨️ 移动端键盘打开: 0px
mobile-interaction.js:817 📱 触觉反馈被跳过：等待用户首次交互
mobile-optimization.js:170 ⌨️ 键盘状态变化: 打开 高度: 0px
data.js:25848 Quick search completed in 0ms, found 3 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 2ms, found 3 results
rag-vector-engine.js:604 RAG高性能语义搜索: "account" -> 7 个结果, 3ms
unified-search-engine.js:954 预加载完成 (8/15): "account" -> 9 个结果
app.js:2420 ⌨️ 移动端键盘关闭
mobile-optimization.js:170 ⌨️ 键盘状态变化: 关闭 高度: 0px
app.js:2402 ⌨️ 移动端键盘打开: 0px
mobile-interaction.js:817 📱 触觉反馈被跳过：等待用户首次交互
mobile-optimization.js:170 ⌨️ 键盘状态变化: 打开 高度: 0px
data.js:25848 Quick search completed in 0ms, found 5 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 2ms, found 6 results
rag-vector-engine.js:604 RAG高性能语义搜索: "vehicle" -> 8 个结果, 3ms
unified-search-engine.js:954 预加载完成 (9/15): "vehicle" -> 11 个结果
data.js:25848 Quick search completed in 0ms, found 0 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 10ms, found 10 results
rag-vector-engine.js:604 RAG高性能语义搜索: "customer service" -> 8 个结果, 11ms
unified-search-engine.js:954 预加载完成 (10/15): "customer service" -> 16 个结果
app.js:2420 ⌨️ 移动端键盘关闭
mobile-optimization.js:170 ⌨️ 键盘状态变化: 关闭 高度: 0px
data.js:25848 Quick search completed in 0ms, found 0 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 24ms, found 11 results
rag-vector-engine.js:604 RAG高性能语义搜索: "cara menerima pesanan" -> 4 个结果, 26ms
unified-search-engine.js:954 预加载完成 (11/15): "cara menerima pesanan" -> 13 个结果
data.js:25848 Quick search completed in 1ms, found 3 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 3ms, found 3 results
rag-vector-engine.js:604 RAG高性能语义搜索: "pengeluaran" -> 2 个结果, 4ms
unified-search-engine.js:954 预加载完成 (12/15): "pengeluaran" -> 3 个结果
data.js:25848 Quick search completed in 0ms, found 2 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 1ms, found 2 results
rag-vector-engine.js:604 RAG高性能语义搜索: "akaun" -> 2 个结果, 2ms
unified-search-engine.js:954 预加载完成 (13/15): "akaun" -> 3 个结果
data.js:25848 Quick search completed in 0ms, found 6 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 2ms, found 6 results
rag-vector-engine.js:604 RAG高性能语义搜索: "kenderaan" -> 3 个结果, 2ms
unified-search-engine.js:954 预加载完成 (14/15): "kenderaan" -> 6 个结果
data.js:25848 Quick search completed in 0ms, found 0 results
rag-vector-engine.js:766 使用并行相似度计算...
data.js:25925 Fuzzy search completed in 8ms, found 10 results
rag-vector-engine.js:604 RAG高性能语义搜索: "khidmat pelanggan" -> 6 个结果, 9ms
unified-search-engine.js:954 预加载完成 (15/15): "khidmat pelanggan" -> 14 个结果
unified-search-engine.js:957 所有预加载任务完成，缓存已温热
