{
  "gomyhire_ai_knowledge_base": {
    "metadata": {
      "version": "4.0.0",
      "last_updated": "2025-09-01T00:00:00Z",
      "purpose": "GoMyHire AI助手核心知识库 - 基于实际对话记录优化",
      "data_sources": ["order_chats_database", "customer_service_records"],
      "coverage": ["Malaysia", "Singapore"],
      "languages": ["zh", "en", "ms", "ta"]
    },
    "company_profile": {
      "name": "GoMyHire Transport Services",
      "founded": "2018",
      "headquarters": "Kuala Lumpur, Malaysia",
      "fleet_size": 477,
      "service_areas": ["KLIA", "KLIA2", "Klang Valley", "Genting Highlands", "Singapore"],
      "core_services": ["airport_transfer", "city_transfer", "hotel_transfer", "hourly_charter"]
    },
    "service_operations": {
      "airport_procedures": {
        "pickup_process": [
          "客户到达后联系司机",
          "司机15-20分钟内到达接机点",
          "机场规定禁止停车，需随到随走",
          "司机在指定出口和柱子等候",
          "协助搬运行李"
        ],
        "waiting_time_policy": {
          "free_waiting": "90分钟（从降落时间计算）",
          "additional_charges": "每30分钟35马币",
          "flight_monitoring": "国内航班提前2小时，国际航班提前3小时"
        },
        "pickup_points": {
          "KLIA_T1": ["出口1-8号", "对应柱子区域"],
          "KLIA_T2": ["Level 1门口1-5号", "注意JPJ执法"]
        }
      },
      "hotel_procedures": {
        "pickup_process": [
          "司机提前15-30分钟到达酒店附近",
          "客户准备好后通知司机",
          "酒店大堂不允许停车等候",
          "司机1分钟内到达酒店门口"
        ],
        "dropoff_process": [
          "安全送达酒店门口",
          "协助卸载行李",
          "服务完成确认"
        ]
      }
    },
    "customer_service_standards": {
      "communication_flow": {
        "booking_confirmation": [
          "系统自动发送订单确认信息",
          "客服主动联系确认订单详情",
          "确认人数、行李尺寸、特殊需求",
          "发送车辆信息和司机联系方式"
        ],
        "key_information_required": [
          "订单号 (Order Number)",
          "OTA参考号 (OTA Reference)",
          "航班号 (Flight Number)",
          "接送地址 (Pickup/Destination Address)",
          "车辆类型 (Car Type)",
          "联系方式 (Customer/Driver Contact)"
        ]
      },
      "standard_responses": {
        "booking_confirmation": "感谢您的预订。您的订单已确认，司机信息将在出发前24小时发送给您。",
        "airport_waiting_info": "🔥 请连接机场免费WiFi保持联系。提取行李后通知我们，司机将在15-20分钟内到达。免费等待时间：机场接送90分钟，酒店接送30分钟。",
        "luggage_inquiry": "请您确认人数和行李尺寸，包括每个行李的大小。这将帮助我们安排合适的车辆。",
        "delay_notification": "尊敬的客户，我们注意到您的航班延误。司机将根据新的到达时间调整接机时间。",
        "vehicle_information": "司机详细信息：姓名：{name}，车牌：{plate}，车型：{model}，电话：{phone}"
      },
      "multilingual_templates": {
        "driver_introduction": {
          "chinese": "您好！我是您的司机{姓名}，车牌：{车牌}，车型：{车型}，电话：{电话}。",
          "english": "Hello! I'm your driver {name}, plate: {plate}, model: {model}, phone: {phone}.",
          "malay": "Hai! Saya pemandu anda {name}, plat: {plate}, model: {model}, telefon: {phone}."
        },
        "pickup_location": {
          "chinese": "我在{地点}{出口}号门{柱子}号柱子等候，请到了联系我。",
          "english": "I'm waiting at {location} door {door} pillar {pillar}, please contact me when you arrive.",
          "malay": "Saya menunggu di {location} pintu {door} tiang {pillar}, sila hubungi saya apabila anda tiba."
        }
      }
    },
    "vehicle_fleet": {
      "economy": {
        "models": ["Proton Saga", "Perodua Myvi", "Toyota Vios"],
        "capacity": 4,
        "luggage_capacity": "2个中号行李箱",
        "suitable_for": "经济型旅客，短途出行，1-2人"
      },
      "sedan": {
        "models": ["Honda City", "Toyota Corolla", "Nissan Almera"],
        "capacity": 4,
        "luggage_capacity": "3个中号行李箱",
        "suitable_for": "商务旅客，舒适出行"
      },
      "mpv": {
        "models": ["Toyota Innova", "Honda BR-V", "Mitsubishi Xpander"],
        "capacity": 7,
        "luggage_capacity": "4个大号行李箱",
        "suitable_for": "家庭，小团体，中等行李"
      },
      "luxury_mpv": {
        "models": ["Toyota Alphard", "Hyundai Starex", "Vellfire"],
        "capacity": 7,
        "luggage_capacity": "6个大号行李箱",
        "suitable_for": "商务人士，VIP客户，特殊场合"
      },
      "van": {
        "models": ["Toyota Hiace", "Hyundai Starex Van"],
        "capacity": 10-18,
        "luggage_capacity": "10+个大号行李箱",
        "suitable_for": "大型团体，会议用车"
      }
    },
    "problem_resolution": {
      "common_issues": {
        "flight_delay": {
          "solution": "自动监控系统检测延误，实时调整司机到达时间",
          "customer_notification": "实时发送延误信息",
          "driver_instruction": "根据新时间调整到达时间"
        },
        "terminal_change": {
          "solution": "立即更新订单信息，通知司机变更",
          "response_time": "5分钟内处理",
          "confirmation": "与客户确认新的接送地点"
        },
        "extra_waiting_time": {
          "policy": "免费等待时间后每30分钟35马币",
          "notification": "提前15、30、45分钟分别提醒",
          "calculation": "从预订时间或降落时间开始计算"
        },
        "additional_stops": {
          "policy": "标准停留15分钟免费，超出时间额外收费",
          "popular_stops": ["Batu Caves", "Genting Highlands", "Putrajaya"],
          "pricing": "每30分钟35马币"
        }
      },
      "real_case_examples": {
        "case_69416": "航班延误处理 - 司机提前到达，实时监控，灵活调整",
        "case_72036": "航站楼变更 - 5分钟内完成变更，客户满意度高",
        "case_76536": "中途停留 - 黑风洞停留，明确收费政策",
        "case_98389": "多语言服务 - 马来语沟通，提升客户体验"
      }
    },
    "api_integration": {
      "endpoints": {
        "knowledge_query": "/api/v1/knowledge/query",
        "driver_location": "/api/v1/driver/location",
        "flight_status": "/api/v1/flight/status",
        "order_modification": "/api/v1/order/modify",
        "customer_support": "/api/v1/support/assist"
      },
      "webhook_events": {
        "flight_delay": "实时航班延误通知",
        "driver_arrival": "司机到达通知",
        "order_completion": "订单完成反馈",
        "customer_feedback": "客户满意度调查"
      }
    },
    "performance_metrics": {
      "service_quality": {
        "on_time_pickup_rate": "96.8%",
        "customer_satisfaction": "4.76/5.0",
        "response_time": "2-5分钟",
        "success_rate": "98.2%"
      },
      "operational_efficiency": {
        "average_trip_time": "11.5分钟",
        "driver_utilization": "85.7%",
        "vehicle_downtime": "2.3%",
        "fuel_efficiency": "优化中"
      }
    }
  }
}