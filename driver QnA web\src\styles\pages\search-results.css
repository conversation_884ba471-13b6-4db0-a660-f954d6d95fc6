/*
 * 文件路径: src/styles/pages/search-results.css
 * 文件描述: 搜索结果页面专用样式，包含搜索结果列表、单个结果项、匹配标识、分数指示器等完整的视觉呈现
 * 依赖关系:
 *   - 依赖设计令牌系统 (tokens/*)
 *   - 依赖主题变量 (themes/variables.css)
 *   - 依赖基础组件样式 (components/cards.css, components/glassmorphism.css)
 * 
 * 功能特性:
 *   - 现代玻璃态设计风格
 *   - 响应式布局适配
 *   - 丰富的交互动效
 *   - 清晰的信息层次
 *   - 优秀的用户体验
 */

/* ===== 搜索结果页面容器 ===== */
.search-page {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    var(--primary-50) 0%, 
    var(--background-primary) 30%, 
    var(--secondary-50) 100%);
  padding: var(--space-lg) 0;
}

.search-results {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

/* ===== 搜索结果头部 ===== */
.search-header {
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-2xl);
  text-align: center;
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
}

.search-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    var(--primary-500), 
    var(--secondary-500), 
    var(--accent-500)
  );
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.search-header .back-btn {
  position: absolute;
  top: var(--space-lg);
  left: var(--space-lg);
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  padding: var(--space-sm) var(--space-lg);
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--font-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-medium);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.search-header .back-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateX(-2px);
  box-shadow: var(--shadow-md);
}

.search-header h2 {
  font-size: var(--font-2xl);
  font-weight: var(--font-weight-bold);
  margin: var(--space-lg) 0 var(--space-md);
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.search-header p {
  font-size: var(--font-base);
  color: var(--text-secondary);
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  display: inline-block;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* ===== 搜索结果列表容器 ===== */
.search-results-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== 单个搜索结果项 ===== */
.search-result-item {
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all var(--transition-medium);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-card);
  animation: slideInRight 0.5s ease-out;
  animation-fill-mode: both;
}

.search-result-item:nth-child(1) { animation-delay: 0.1s; }
.search-result-item:nth-child(2) { animation-delay: 0.15s; }
.search-result-item:nth-child(3) { animation-delay: 0.2s; }
.search-result-item:nth-child(4) { animation-delay: 0.25s; }
.search-result-item:nth-child(5) { animation-delay: 0.3s; }
.search-result-item:nth-child(n+6) { animation-delay: 0.35s; }

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.search-result-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, 
    var(--primary-400), 
    var(--secondary-400)
  );
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.search-result-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: rgba(168, 85, 247, 0.3);
  background: rgba(255, 255, 255, 0.12);
}

.search-result-item:hover::before {
  opacity: 1;
}

.search-result-item:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* ===== 搜索结果头部信息 ===== */
.search-result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
  flex-wrap: wrap;
  gap: var(--space-sm);
}

.search-result-id {
  font-size: var(--font-xs);
  color: var(--text-muted);
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-result-match-info {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  flex: 1;
  justify-content: center;
}

.search-result-category {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.08);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== 匹配类型标识 ===== */
.match-type-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.02em;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.match-type-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent
  );
  transition: left 0.6s ease;
}

.search-result-item:hover .match-type-badge::before {
  left: 100%;
}

/* ===== 相关度分数指示器 ===== */
.score-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.score-bar {
  width: 60px;
  height: 6px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, 
    var(--success-400), 
    var(--warning-400), 
    var(--primary-400)
  );
  border-radius: var(--radius-full);
  transition: width 1s ease-out 0.3s;
  position: relative;
}

.score-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.6), 
    transparent
  );
  animation: scoreShine 2s ease-in-out 1s;
}

@keyframes scoreShine {
  from { left: -100%; }
  to { left: 100%; }
}

/* ===== 搜索结果标题 ===== */
.search-result-title {
  font-size: var(--font-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  line-height: var(--leading-tight);
  transition: color var(--transition-fast);
}

.search-result-item:hover .search-result-title {
  color: var(--primary-600);
}

.search-result-title mark {
  background: linear-gradient(135deg, 
    rgba(168, 85, 247, 0.2), 
    rgba(59, 130, 246, 0.2)
  );
  color: var(--primary-700);
  padding: 0.1em 0.2em;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-bold);
}

/* ===== 搜索结果摘要 ===== */
.search-result-excerpt {
  font-size: var(--font-base);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-md);
}

.search-result-excerpt mark {
  background: linear-gradient(135deg, 
    rgba(168, 85, 247, 0.15), 
    rgba(59, 130, 246, 0.15)
  );
  color: var(--text-primary);
  padding: 0.1em 0.2em;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
}

/* ===== 搜索结果元信息 ===== */
.search-result-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--font-sm);
  color: var(--text-muted);
  padding-top: var(--space-sm);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.result-timestamp {
  opacity: 0.7;
}

/* ===== 无结果状态优化 ===== */
.no-results {
  text-align: center;
  padding: var(--space-4xl) var(--space-lg);
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.no-results .icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
  opacity: 0.6;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.no-results h3 {
  font-size: var(--font-2xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-lg);
  color: var(--text-primary);
}

.no-results p {
  font-size: var(--font-base);
  color: var(--text-secondary);
  margin-bottom: var(--space-md);
}

.no-results ul {
  list-style: none;
  padding: 0;
  margin: var(--space-lg) 0;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.no-results li {
  padding: var(--space-sm) 0;
  color: var(--text-secondary);
  font-size: var(--font-sm);
}

.no-results li::before {
  content: '💡';
  margin-right: var(--space-sm);
}

.back-to-home-btn {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-medium);
  box-shadow: var(--shadow-md);
  margin-top: var(--space-lg);
}

.back-to-home-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

.back-to-home-btn:active {
  transform: translateY(0);
}

/* ===== 加载状态样式 ===== */
.search-loading {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  animation: fadeIn 0.3s ease-out;
}

.search-loading-item {
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

.search-loading-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent
  );
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  50% { left: -100%; }
  100% { left: 100%; }
}

.loading-header {
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
  width: 70%;
}

.loading-title {
  height: 24px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-sm);
  width: 90%;
}

.loading-content {
  height: 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-xs);
}

.loading-content:nth-child(3) { width: 85%; }
.loading-content:nth-child(4) { width: 60%; }

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .search-page {
    padding: var(--space-md) 0;
  }
  
  .search-results {
    padding: 0 var(--space-md);
  }
  
  .search-header {
    padding: var(--space-lg);
    margin-bottom: var(--space-lg);
  }
  
  .search-header .back-btn {
    position: static;
    margin-bottom: var(--space-md);
    align-self: flex-start;
  }
  
  .search-header h2 {
    font-size: var(--font-xl);
    margin: 0 0 var(--space-sm);
  }
  
  .search-result-item {
    padding: var(--space-lg);
  }
  
  .search-result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
  
  .search-result-match-info {
    justify-content: flex-start;
    width: 100%;
  }
  
  .search-result-title {
    font-size: var(--font-base);
  }
  
  .score-bar {
    width: 50px;
    height: 4px;
  }
  
  .no-results {
    padding: var(--space-2xl) var(--space-md);
  }
  
  .no-results .icon {
    font-size: 3rem;
  }
  
  .no-results h3 {
    font-size: var(--font-xl);
  }
}

@media (max-width: 480px) {
  .search-results {
    padding: 0 var(--space-sm);
  }
  
  .search-header {
    padding: var(--space-md);
  }
  
  .search-result-item {
    padding: var(--space-md);
  }
  
  .search-result-header {
    gap: var(--space-xs);
  }
  
  .match-type-badge {
    font-size: var(--font-2xs);
    padding: var(--space-2xs) var(--space-xs);
  }
  
  .score-bar {
    width: 40px;
  }
  
  .search-result-title {
    font-size: var(--font-sm);
  }
  
  .search-result-excerpt {
    font-size: var(--font-sm);
  }
}

/* ===== 无障碍和高对比度支持 ===== */
@media (prefers-reduced-motion: reduce) {
  .search-result-item,
  .search-results-list,
  .no-results,
  .score-fill,
  .match-type-badge::before,
  .search-loading-item::before {
    animation: none !important;
    transition: none !important;
  }
  
  .search-result-item:hover {
    transform: none;
  }
}

@media (prefers-contrast: high) {
  .search-result-item {
    border-width: 2px;
    background: var(--background-primary);
  }
  
  .search-result-item:hover {
    border-color: var(--primary-600);
  }
  
  .match-type-badge {
    background: var(--background-secondary) !important;
    border-width: 2px !important;
  }
  
  .search-result-title mark,
  .search-result-excerpt mark {
    background: var(--warning-200) !important;
    color: var(--gray-900) !important;
  }
}

/* ===== 暗色主题适配 ===== */
[data-theme="dark"] .search-page {
  background: linear-gradient(135deg, 
    var(--gray-900) 0%, 
    var(--gray-800) 30%, 
    var(--gray-900) 100%);
}

[data-theme="dark"] .search-result-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(168, 85, 247, 0.4);
}

[data-theme="dark"] .search-result-title mark {
  background: rgba(168, 85, 247, 0.3);
  color: var(--primary-300);
}

[data-theme="dark"] .search-result-excerpt mark {
  background: rgba(168, 85, 247, 0.2);
  color: var(--primary-200);
}
