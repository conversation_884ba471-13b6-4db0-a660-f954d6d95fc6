/*
 * 文件路径: src/styles/components/navigation.css
 * 文件描述: 定义了应用程序中各种导航组件的样式，包括浮动底部导航栏、面包屑导航、标签页导航以及侧边栏导航（如果实现）。它融合了玻璃拟态效果、响应式设计和可访问性考虑。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于在以下文件中定义的CSS变量（设计令牌）：
 *     - `src/styles/tokens/spacing.css` (用于间距)。
 *     - `src/styles/tokens/typography.css` (用于字体大小和字重)。
 *     - `src/styles/tokens/colors.css` (用于文本和背景颜色)。
 *     - `src/styles/tokens/radius.css` (用于 `border-radius`)。
 *     - `src/styles/tokens/shadows.css` (用于 `box-shadow`)。
 *     - `src/styles/tokens/transitions.css` (用于 `transition` 属性)。
 *     - `src/styles/themes/variables.css` (用于渐变和玻璃拟态效果的颜色和模糊值)。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 提供一致且直观的导航元素。
 *   - 将导航组件与整体玻璃拟态设计视觉融合。
 *   - 确保在不同设备和用户偏好下的响应性和可访问性。
 *   - 为激活和悬停状态提供清晰的视觉反馈。
 * 关键部分/规则:
 *   - `.bottom-nav`: 样式化浮动底部导航栏，包括其固定位置、玻璃拟态背景、圆角和阴影。包含用于微妙移动和阴影变化的悬停效果。
 *   - `.nav-item`: 样式化底部导航中的单个项目，定义其大小、布局、文本和图标样式。包含激活和悬停状态的视觉反馈。
 *   - `.breadcrumb`: 样式化面包屑导航，为用户提供清晰的路径。使用玻璃拟态背景和边框，并处理长路径的溢出。
 *   - `.breadcrumb-item`, `.breadcrumb-separator`: 单个面包屑链接和分隔符的样式。
 *   - `.tabs`: 样式化标签页导航，使用玻璃拟态背景和边框。
 *   - `.tab-item`: 样式化单个标签页，具有激活和悬停状态。
 *   - `.sidebar-nav`: 样式化潜在的侧边栏导航（目前在 `index.html` 中被注释掉，但在此处定义了样式），包括其固定位置、背景、边框、阴影和滚动行为。
 *   - `.sidebar-nav-item`: 样式化侧边栏导航中的单个项目，具有激活和悬停状态。
 *   - `.nav-overlay`: 样式化全屏覆盖层，通常与模态框或侧边栏一起使用，具有半透明背景。
 *   - `.pagination`: 样式化分页控件。
 *   - `.pagination-item`: 样式化单个分页按钮，具有激活、悬停和禁用状态。
 *   - **响应式优化 (`@media`)**: 调整小屏幕的尺寸、内边距和溢出行为。
 *   - **可访问性 (`@media (prefers-reduced-motion: reduce)`, `focus-visible`, `@media (prefers-contrast: high)`)**: 禁用变换动画，提供清晰的焦点轮廓，并调整边框宽度以适应可访问性偏好。
 * 使用约定:
 *   - HTML中与导航相关的元素应使用这些类（例如，`<nav class="bottom-nav">`，`<a class="nav-item">`）。
 *   - `active` 类通常由JavaScript添加/移除，以指示当前选定的项目。
 */
/* 组件样式 - 导航组件 */

/* 底部导航 - 浮动式设计 */
.bottom-nav {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: auto;
  height: auto;
  background: var(--gradient-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-primary), var(--shadow-lg);
  z-index: 1000;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transition: var(--transition-all);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bottom-nav:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary), var(--shadow-2xl);
}

/* 导航项目 */
.nav-item {
  width: 56px;
  height: 56px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  color: var(--text-inverse);
  font-size: var(--font-xs);
  cursor: pointer;
  transition: var(--transition-all);
  text-decoration: none;
  padding: var(--space-sm);
  border-radius: var(--radius-full);
  background: var(--glass-background-subtle);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
}

.nav-item.active {
  background: var(--glass-background-intense);
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(168, 85, 247, 0.3);
}

.nav-item:hover {
  background: var(--glass-background);
  transform: scale(1.05);
  color: var(--text-inverse);
}

.nav-item .icon {
  font-size: var(--font-lg);
  transition: var(--transition-transform);
}

.nav-item:hover .icon {
  transform: scale(1.1);
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-lg);
  padding: var(--space-sm) var(--space-md);
  background: var(--glass-background-subtle);
  border: 1px solid var(--glass-border-subtle);
  border-radius: var(--radius-full);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  width: fit-content;
}

.breadcrumb-item {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--font-sm);
  transition: var(--transition-colors);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
}

.breadcrumb-item:hover {
  color: var(--primary-600);
  background: var(--background-secondary);
}

.breadcrumb-item.active {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.breadcrumb-separator {
  color: var(--text-tertiary);
  font-size: var(--font-sm);
  margin: 0 var(--space-xs);
}

/* 标签页导航 */
.tabs {
  display: flex;
  background: var(--glass-background-subtle);
  border: 1px solid var(--glass-border-subtle);
  border-radius: var(--radius-xl);
  padding: var(--space-xs);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  margin-bottom: var(--space-lg);
}

.tab-item {
  flex: 1;
  padding: var(--space-sm) var(--space-lg);
  text-align: center;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-all);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-sm);
  color: var(--text-secondary);
  background: transparent;
  border: none;
}

.tab-item:hover {
  color: var(--text-primary);
  background: var(--glass-background);
}

.tab-item.active {
  color: var(--text-primary);
  background: var(--surface-color);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

/* 侧边导航（如果需要） */
.sidebar-nav {
  width: 280px;
  height: 100vh;
  position: fixed;
  left: -280px;
  top: 0;
  background: var(--surface-color);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  z-index: 1100;
  transition: var(--transition-all);
  overflow-y: auto;
}

.sidebar-nav.open {
  left: 0;
}

.sidebar-nav-header {
  padding: var(--space-2xl) var(--space-lg);
  border-bottom: 1px solid var(--border-light);
}

.sidebar-nav-content {
  padding: var(--space-lg);
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-all);
  margin-bottom: var(--space-xs);
}

.sidebar-nav-item:hover {
  color: var(--text-primary);
  background: var(--background-secondary);
}

.sidebar-nav-item.active {
  color: var(--primary-600);
  background: var(--primary-50);
  font-weight: var(--font-weight-medium);
}

[data-theme="dark"] .sidebar-nav-item.active {
  background: var(--primary-900);
  color: var(--primary-300);
}

/* 导航覆盖层 */
.nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-color);
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-modal);
}

.nav-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 分页导航 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-sm);
  margin-top: var(--space-2xl);
}

.pagination-item {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition-all);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.pagination-item:hover {
  color: var(--primary-600);
  background: var(--primary-50);
  border-color: var(--primary-200);
}

.pagination-item.active {
  color: var(--text-inverse);
  background: var(--primary-600);
  border-color: var(--primary-600);
}

.pagination-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .bottom-nav {
    bottom: 15px;
    right: 15px;
  }
  
  .nav-item {
    width: 48px;
    height: 48px;
    font-size: var(--font-xs);
  }
  
  .breadcrumb {
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .tabs {
    overflow-x: auto;
  }
  
  .tab-item {
    white-space: nowrap;
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .bottom-nav {
    bottom: 10px;
    right: 10px;
  }
  
  .nav-item {
    width: 44px;
    height: 44px;
  }
  
  .nav-item .icon {
    font-size: var(--font-base);
  }
  
  .breadcrumb {
    padding: var(--space-xs) var(--space-sm);
  }
  
  .breadcrumb-item {
    font-size: var(--font-xs);
    padding: var(--space-xs);
  }
  
  .pagination-item {
    width: 36px;
    height: 36px;
    font-size: var(--font-sm);
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .bottom-nav,
  .nav-item,
  .breadcrumb-item,
  .tab-item,
  .sidebar-nav-item,
  .pagination-item {
    transition: var(--transition-colors);
  }
  
  .bottom-nav:hover,
  .nav-item:hover,
  .nav-item.active {
    transform: none;
  }
  
  .nav-item:hover .icon {
    transform: none;
  }
}

/* 键盘导航支持 */
.nav-item:focus-visible,
.breadcrumb-item:focus-visible,
.tab-item:focus-visible,
.sidebar-nav-item:focus-visible,
.pagination-item:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .bottom-nav,
  .breadcrumb,
  .tabs,
  .sidebar-nav {
    border-width: 2px;
  }
  
  .nav-item,
  .breadcrumb-item,
  .tab-item,
  .sidebar-nav-item,
  .pagination-item {
    border-width: 2px;
  }
}

/* 聊天按钮定位样式 */
.chat-button-above-nav {
  position: fixed !important;
  bottom: 120px !important; /* 导航栏上方20px */
  right: 20px !important;
  z-index: 1000 !important;
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  color: white !important;
  font-size: 24px !important;
  cursor: pointer !important;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.chat-button-above-nav:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6) !important;
}

.chat-button-above-nav:active {
  transform: scale(0.95) !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chat-button-above-nav {
    bottom: 100px !important; /* 在移动端调整位置 */
    right: 15px !important;
    width: 55px !important;
    height: 55px !important;
    font-size: 20px !important;
  }
}