/**
 * FAQ内容批量美化工具
 * 用途：自动优化data.js中的FAQ问题内容，应用新的视觉样式和增强展示效果
 * 技术栈：纯JavaScript + CSS类名映射
 * 相关文件：src/core/data.js, src/styles/components/enhanced-content.css
 * 核心功能：内容分析、样式转换、多语言支持、向后兼容
 */

class FAQBeautifier {
    constructor() {
        this.styleMappings = {
            // 旧样式到新样式的映射
            'highlight-box': 'info-box',
            'alert-box': 'warning-box',
            'note-box': 'tip-box',
            'step-box': 'procedure-box',
            'success-message': 'success-box',
            'error-message': 'error-box',
            
            // 标题样式映射
            'section-title': 'info-box h3',
            'subsection-title': 'info-box h4',
            'important-text': 'warning-box strong',
            
            // 列表样式映射
            'step-list': 'procedure-box ol',
            'bullet-list': 'info-box ul',
            'checklist': 'success-box ul'
        };

        this.iconMappings = {
            // 自动添加图标映射
            '联系客服': '📞',
            '重要提醒': '⚠️',
            '操作步骤': '📋',
            '温馨提示': '💡',
            '注意事项': '⚠️',
            '最佳实践': '✅',
            '常见问题': '❓',
            '解决方法': '🔧',
            '时间要求': '⏰',
            '费用说明': '💰',
            '违规后果': '❌'
        };

        this.enhancementRules = {
            // 内容增强规则
            phoneNumbers: {
                pattern: /(\+?\d{1,4}[-\s]?\d{1,4}[-\s]?\d{1,4}[-\s]?\d{1,4})/g,
                replacement: '<a href="tel:$1" class="phone-link">📱 $1</a>'
            },
            whatsappLinks: {
                pattern: /WhatsApp[:\s]*(\+?\d+)/gi,
                replacement: '<a href="https://wa.me/$1" class="whatsapp-link" target="_blank">📱 WhatsApp: $1</a>'
            },
            emailLinks: {
                pattern: /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/gi,
                replacement: '<a href="mailto:$1" class="email-link">📧 $1</a>'
            },
            timeReferences: {
                pattern: /(\d+)分钟/g,
                replacement: '<span class="time-highlight">⏰ $1分钟</span>'
            },
            moneyReferences: {
                pattern: /RM\s*(\d+(?:\.\d{2})?)/g,
                replacement: '<span class="money-highlight">💰 RM$1</span>'
            }
        };
    }

    /**
     * 分析现有内容结构
     */
    analyzeContent(content) {
        const analysis = {
            hasOldStyles: false,
            hasPlainText: false,
            hasBasicHtml: false,
            needsEnhancement: false,
            languages: [],
            issues: []
        };

        // 检查是否需要美化
        if (typeof content === 'string') {
            analysis.hasPlainText = true;
            analysis.needsEnhancement = true;
        } else if (typeof content === 'object') {
            // 多语言内容
            Object.keys(content).forEach(lang => {
                analysis.languages.push(lang);
                const langContent = content[lang];
                
                if (typeof langContent === 'string') {
                    // 检查是否包含旧的样式类
                    if (langContent.includes('class="') && 
                        (langContent.includes('highlight') || 
                         langContent.includes('alert') ||
                         langContent.includes('note'))) {
                        analysis.hasOldStyles = true;
                    }
                    
                    // 检查是否为基础HTML
                    if (langContent.includes('<p>') || langContent.includes('<br>')) {
                        analysis.hasBasicHtml = true;
                    }
                    
                    // 检查是否需要增强
                    if (!langContent.includes('class="info-box"') && 
                        !langContent.includes('class="procedure-box"') &&
                        langContent.length > 100) {
                        analysis.needsEnhancement = true;
                    }
                }
            });
        }

        return analysis;
    }

    /**
     * 转换单个内容块
     */
    beautifyContent(content, language = 'zh') {
        if (typeof content === 'string') {
            return this.enhancePlainContent(content, language);
        } else if (typeof content === 'object') {
            const enhanced = {};
            Object.keys(content).forEach(lang => {
                enhanced[lang] = this.enhancePlainContent(content[lang], lang);
            });
            return enhanced;
        }
        return content;
    }

    /**
     * 增强纯文本内容
     */
    enhancePlainContent(html, language) {
        let enhanced = html;

        // 1. 替换旧的样式类
        enhanced = this.replaceOldStyles(enhanced);

        // 2. 添加缺失的容器
        enhanced = this.wrapWithAppropriateContainer(enhanced, language);

        // 3. 添加图标和视觉元素
        enhanced = this.addVisualEnhancements(enhanced, language);

        // 4. 增强链接和交互元素
        enhanced = this.enhanceInteractiveElements(enhanced);

        // 5. 优化格式和间距
        enhanced = this.optimizeFormatting(enhanced);

        return enhanced;
    }

    /**
     * 替换旧的样式类
     */
    replaceOldStyles(content) {
        let updated = content;
        
        Object.entries(this.styleMappings).forEach(([oldStyle, newStyle]) => {
            const regex = new RegExp(`class="${oldStyle}"`, 'g');
            updated = updated.replace(regex, `class="${newStyle}"`);
        });

        return updated;
    }

    /**
     * 根据内容类型添加适当的容器
     */
    wrapWithAppropriateContainer(content, language) {
        // 如果已经是增强内容，跳过
        if (content.includes('class="info-box"') || 
            content.includes('class="procedure-box"') ||
            content.includes('class="warning-box"')) {
            return content;
        }

        // 根据内容关键词判断容器类型
        const keywords = this.getKeywordsByLanguage(language);
        
        if (this.containsKeywords(content, keywords.warning)) {
            return `<div class="warning-box">${content}</div>`;
        } else if (this.containsKeywords(content, keywords.procedure)) {
            return `<div class="procedure-box">${content}</div>`;
        } else if (this.containsKeywords(content, keywords.tips)) {
            return `<div class="tip-box">${content}</div>`;
        } else if (this.containsKeywords(content, keywords.success)) {
            return `<div class="success-box">${content}</div>`;
        } else {
            return `<div class="info-box">${content}</div>`;
        }
    }

    /**
     * 获取不同语言的关键词
     */
    getKeywordsByLanguage(language) {
        const keywords = {
            zh: {
                warning: ['注意', '警告', '严禁', '禁止', '后果', '违规', '风险'],
                procedure: ['步骤', '流程', '操作', '方法', '指南', '教程', '如何'],
                tips: ['建议', '提示', '技巧', '窍门', '最佳', '优化', '高效'],
                success: ['成功', '完成', '感谢', '恭喜', '已解决', '已确认']
            },
            en: {
                warning: ['warning', 'caution', 'forbidden', 'prohibited', 'consequence', 'violation', 'risk'],
                procedure: ['step', 'process', 'procedure', 'method', 'guide', 'tutorial', 'how to'],
                tips: ['tip', 'suggestion', 'advice', 'recommendation', 'best practice', 'optimize', 'efficient'],
                success: ['success', 'completed', 'thank you', 'congratulations', 'resolved', 'confirmed']
            },
            ms: {
                warning: ['amaran', 'berhati-hati', 'dilarang', 'kesan', 'melanggar', 'risiko'],
                procedure: ['langkah', 'proses', 'prosedur', 'kaedah', 'panduan', 'tutorial'],
                tips: ['cadangan', 'tip', 'nasihat', 'amalan terbaik', 'optimum', 'cekap'],
                success: ['jaya', 'selesai', 'terima kasih', 'tahniah', 'diselesaikan', 'disahkan']
            }
        };

        return keywords[language] || keywords.zh;
    }

    /**
     * 检查内容是否包含特定关键词
     */
    containsKeywords(content, keywords) {
        return keywords.some(keyword => 
            content.toLowerCase().includes(keyword.toLowerCase())
        );
    }

    /**
     * 添加视觉增强元素
     */
    addVisualEnhancements(content, language) {
        let enhanced = content;

        // 为标题添加图标
        Object.entries(this.iconMappings).forEach(([text, icon]) => {
            const regex = new RegExp(`<h[34][^>]*>${text}</h[34]>`, 'gi');
            enhanced = enhanced.replace(regex, (match) => 
                match.replace('>', `>${icon} `)
            );
        });

        // 增强时间、金钱等关键信息
        Object.entries(this.enhancementRules).forEach(([_, rule]) => {
            enhanced = enhanced.replace(rule.pattern, rule.replacement);
        });

        return enhanced;
    }

    /**
     * 增强交互元素
     */
    enhanceInteractiveElements(content) {
        let enhanced = content;

        // 为纯文本电话号码添加链接
        enhanced = enhanced.replace(
            /(\+?\d{1,4}[-\s]?\d{1,4}[-\s]?\d{1,4}[-\s]?\d{1,4})(?![^<]*>)/g,
            '<a href="tel:$1" class="enhanced-link">📱 $1</a>'
        );

        // 为纯文本时间添加高亮
        enhanced = enhanced.replace(
            /(\d+)分钟(?![^<]*>)/g,
            '<span class="time-highlight">⏰ $1分钟</span>'
        );

        // 为纯文本金钱添加高亮
        enhanced = enhanced.replace(
            /RM\s*(\d+(?:\.\d{2})?)(?![^<]*>)/g,
            '<span class="money-highlight">💰 RM$1</span>'
        );

        return enhanced;
    }

    /**
     * 优化格式和间距
     */
    optimizeFormatting(content) {
        let optimized = content;

        // 规范化换行
        optimized = optimized.replace(/\n{3,}/g, '\n\n');

        // 移除多余的空格
        optimized = optimized.replace(/[ \t]+$/gm, '');

        // 确保段落之间有适当间距
        optimized = optimized.replace(/<\/p>\s*<p>/g, '</p>\n\n<p>');

        return optimized.trim();
    }

    /**
     * 批量处理FAQ数据
     */
    async beautifyFAQData(faqData) {
        console.log('🎨 开始批量美化FAQ内容...');
        
        const stats = {
            total: 0,
            enhanced: 0,
            skipped: 0,
            issues: []
        };

        if (!faqData || !faqData.questions) {
            throw new Error('无效的FAQ数据结构');
        }

        for (const question of faqData.questions) {
            stats.total++;
            
            try {
                const analysis = this.analyzeContent(question.content);
                
                if (analysis.needsEnhancement) {
                    question.content = this.beautifyContent(question.content);
                    question._enhanced = true;
                    stats.enhanced++;
                    console.log(`✅ 已美化: ${question.id} - ${question.title?.zh || ''}`);
                } else {
                    question._skipped = true;
                    stats.skipped++;
                    console.log(`⏭️ 跳过: ${question.id} - 已是最新格式`);
                }

                // 记录问题以便调试
                if (analysis.issues.length > 0) {
                    stats.issues.push({
                        id: question.id,
                        issues: analysis.issues
                    });
                }

            } catch (error) {
                console.error(`❌ 处理失败: ${question.id}`, error);
                stats.issues.push({
                    id: question.id,
                    error: error.message
                });
            }
        }

        return { data: faqData, stats };
    }

    /**
     * 生成美化报告
     */
    generateReport(stats) {
        const report = `
## 📊 FAQ内容美化报告

### 处理统计
- **总计问题数**: ${stats.total}
- **已美化**: ${stats.enhanced}
- **已跳过**: ${stats.skipped}
- **处理问题**: ${stats.issues.length}

### 美化效果
- ✅ 应用了新的视觉样式
- 🎨 添加了图标和视觉元素  
- 📱 优化了移动端显示
- 🔗 增强了交互链接
- 🌐 支持多语言内容

### 样式应用
- info-box: 信息展示
- procedure-box: 操作步骤
- warning-box: 警告提醒
- tip-box: 提示建议
- success-box: 成功消息
- error-box: 错误提示

### 后续建议
1. 在浏览器中测试美化效果
2. 验证多语言显示
3. 检查移动端响应式
4. 收集用户反馈
        `;

        return report.trim();
    }
}

// 使用示例和导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FAQBeautifier;
} else {
    // 浏览器环境
    window.FAQBeautifier = FAQBeautifier;
}

// 命令行使用示例
async function runBeautification() {
    try {
        // 注意：这是示例代码，实际使用时需要加载真实的data.js
        console.log('🚀 FAQ内容美化工具已启动');
        console.log('请确保已加载 src/core/data.js');
        
        const beautifier = new FAQBeautifier();
        
        // 在实际环境中，这里应该加载真实的faqData
        // const result = await beautifier.beautifyFAQData(faqData);
        // console.log(beautifier.generateReport(result.stats));
        
        console.log('💡 使用方法：');
        console.log('1. 在浏览器控制台加载data.js');
        console.log('2. 创建FAQBeautifier实例');
        console.log('3. 调用beautifyFAQData方法');
        
    } catch (error) {
        console.error('美化过程出错:', error);
    }
}

// 如果直接运行
if (typeof window === 'undefined') {
    runBeautification();
}