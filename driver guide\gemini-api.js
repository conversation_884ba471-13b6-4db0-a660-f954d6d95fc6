// Gemini API 集成类
class GeminiAPI {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent';
    }

    async generateQuestion(rule, category, difficulty = 'medium') {
        const prompt = `
你是一个专业的GoMyHire平台规则培训师。基于以下规则信息，生成一个场景案例选择题：

规则类别：${category}
规则内容：${rule}
难度等级：${difficulty}

要求：
1. 创建一个真实的工作场景案例，描述一个具体的情况
2. 基于4个选择答案，其中只有1个是正确的
3. 选项要包含常见的误解和错误做法
4. 场景要贴近实际工作，具有代表性
5. 案例描述长度控制在80-120字

请严格按照以下JSON格式返回：
{
  "scenario": "场景描述",
  "options": ["选项A", "选项B", "选项C", "选项D"],
  "correctAnswer": 0,
  "explanation": "正确答案的解释"
}

注意：correctAnswer的值是正确选项的索引（0-3）。
        `;

        try {
            const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.3,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 8000,
                    }
                })
            });

            const data = await response.json();
            
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                let responseText = data.candidates[0].content.parts[0].text.trim();
                
                // 处理markdown代码块格式
                if (responseText.startsWith('```json')) {
                    responseText = responseText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
                } else if (responseText.startsWith('```')) {
                    responseText = responseText.replace(/^```\s*/, '').replace(/\s*```$/, '');
                }
                
                try {
                    // 尝试解析JSON格式
                    return JSON.parse(responseText);
                } catch (parseError) {
                    // 如果JSON解析失败，返回备用问题
                    console.error('JSON解析失败:', parseError, '原始响应:', responseText);
                    return this.getFallbackScenarioQuestion(rule, category);
                }
            } else {
                throw new Error('Invalid API response');
            }
        } catch (error) {
            console.error('Gemini API Error:', error);
            // 返回备用问题
            return this.getFallbackScenarioQuestion(rule, category);
        }
    }

    async evaluateAnswer(question, rule, userAnswer, options = [], correctAnswer = -1) {
        const prompt = `
你是一个专业的GoMyHire平台规则评估师。请评估学员对场景案例选择题的回答：

场景案例：${question.scenario || question}
考核规则：${rule}
学员选择的答案：${userAnswer}
可选答案：${options.join(', ')}
正确答案：${correctAnswer >= 0 ? options[correctAnswer] : '未知'}

评估标准：
1. 选择正确性（60分）：是否选择了符合规则的答案
2. 规则理解（25分）：是否理解规则的核心要求
3. 应用能力（15分）：是否能将规则应用到实际场景

请按照以下JSON格式返回评估结果：
{
    "score": 数字（0-100）,
    "feedback": "详细的反馈意见",
    "strengths": ["回答的优点1", "优点2"],
    "improvements": ["需要改进的地方1", "改进地方2"],
    "keyPoints": ["相关的关键点1", "关键点2"]
}

请确保返回的是有效的JSON格式。
        `;

        try {
            const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.3,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 512,
                    }
                })
            });

            const data = await response.json();
            
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                let responseText = data.candidates[0].content.parts[0].text.trim();
                
                // 处理markdown代码块格式
                if (responseText.startsWith('```json')) {
                    responseText = responseText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
                } else if (responseText.startsWith('```')) {
                    responseText = responseText.replace(/^```\s*/, '').replace(/\s*```$/, '');
                }
                
                try {
                    return JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON Parse Error:', parseError, '原始响应:', responseText);
                    return this.getFallbackEvaluation(userAnswer);
                }
            } else {
                throw new Error('Invalid API response');
            }
        } catch (error) {
            console.error('Gemini API Error:', error);
            return this.getFallbackEvaluation(userAnswer);
        }
    }

    getFallbackQuestion(rule, category) {
        const fallbackQuestions = [
            `请详细说明"${rule}"这个规则的具体要求和操作步骤。`,
            `在实际工作中，您会如何确保"${rule}"这个规则的执行？请举例说明。`,
            `"${rule}"违反了会有什么后果？如何避免？`,
            `请解释"${rule}"对服务质量的影响，以及为什么这个规则很重要。`
        ];
        return fallbackQuestions[Math.floor(Math.random() * fallbackQuestions.length)];
    }

    getFallbackScenarioQuestion(rule, category) {
        // 创建一个备用的场景选择题
        const scenarios = [
            {
                scenario: `在工作中，你遇到了一个涉及"${rule}"的情况。你会如何处理？`,
                options: [
                    `严格按照规则要求处理`,
                    `根据情况灵活处理`,
                    `先完成工作再考虑规则`,
                    `询问同事的意见`
                ],
                correctAnswer: 0,
                explanation: `正确的做法是严格按照规则要求处理，这确保了工作的规范性和一致性。`
            },
            {
                scenario: `你的同事在处理任务时似乎忽略了"${rule}"这个规则。作为团队成员，你应该：`,
                options: [
                    `提醒同事注意相关规则`,
                    `不干涉同事的工作方式`,
                    `向主管报告这个问题`,
                    `自己也忽略这个规则`
                ],
                correctAnswer: 0,
                explanation: `适当的提醒有助于团队共同遵守规则，提高整体工作质量。`
            }
        ];
        
        return scenarios[Math.floor(Math.random() * scenarios.length)];
    }

    getFallbackEvaluation(userAnswer) {
        // 简单的评估逻辑
        const length = userAnswer.length;
        let score = 0;
        
        if (length >= 100) score += 30;
        else if (length >= 50) score += 20;
        else if (length >= 20) score += 10;
        
        // 检查关键词
        const keywords = ['规则', '必须', '应该', '需要', '注意', '确保', '避免', '禁止'];
        const foundKeywords = keywords.filter(keyword => userAnswer.includes(keyword));
        score += foundKeywords.length * 5;
        
        score = Math.min(100, Math.max(0, score));
        
        return {
            score: score,
            feedback: score >= 80 ? '回答很全面！您准确理解了相关规则。' :
                     score >= 60 ? '基本正确，但可以更详细地说明具体要求。' :
                     '需要加强对规则的理解，建议重新学习相关内容。',
            strengths: foundKeywords.length > 0 ? ['包含了重要的关键词'] : [],
            improvements: score < 80 ? ['建议更详细地说明规则的各个方面'] : [],
            keyPoints: foundKeywords
        };
    }

    async generateFollowUpQuestion(originalQuestion, userAnswer, evaluation) {
        const prompt = `
基于之前的对话，生成一个跟进问题：

原问题：${originalQuestion}
学员回答：${userAnswer}
评估结果：${evaluation.score}分 - ${evaluation.feedback}

要求：
1. 如果学员回答很好（80分以上），生成一个更深入或更复杂的问题
2. 如果学员回答一般（60-79分），生成一个巩固理解的问题
3. 如果学员回答较差（60分以下），生成一个基础理解的问题
4. 问题要帮助学员提升对规则的理解
5. 问题要具体、实用

请直接生成问题，不要包含其他说明文字。
        `;

        try {
            const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.3,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 8000,
                    }
                })
            });

            const data = await response.json();
            
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                return data.candidates[0].content.parts[0].text.trim();
            } else {
                throw new Error('Invalid API response');
            }
        } catch (error) {
            console.error('Gemini API Error:', error);
            return "请进一步解释您在实际工作中如何应用这个规则？";
        }
    }
}

// 导出Gemini API类
window.GeminiAPI = GeminiAPI;