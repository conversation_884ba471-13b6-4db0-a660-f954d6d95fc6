/*
 * 文件路径: src/styles/tokens/colors.css
 * 文件描述: 定义了应用程序的颜色系统，通过CSS自定义属性（变量）提供了一致的颜色调色板。它包含了品牌色、中性色、语义色（文本、背景、边框）、状态色以及阴影和覆盖层颜色。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 集中管理所有颜色定义，便于统一更新和维护应用的主题。
 *   - 通过预定义的颜色集，确保设计的一致性。
 *   - 定义了语义化的颜色变量（如 `--text-primary`, `--background-color`），这些变量可以在主题文件（如 `light.css`, `dark.css`）中被覆盖，从而实现灵活的主题切换。
 * 使用约定:
 *   - 其他CSS文件应通过 `var()` 函数引用这些颜色变量（例如：`color: var(--primary-500);`），而不是直接使用硬编码的颜色值。
 * 关键部分:
 *   - `--primary-*`: 定义了一系列紫色调的品牌主色。
 *   - `--neutral-*`: 定义了灰度调色板，用于中性元素。
 *   - `--text-*`: 定义了不同文本类型（主文本、次要文本、三级文本、反色文本）的颜色。
 *   - `--background-color`, `--surface-color`, `--border-color`: 定义了背景、表面和边框元素的颜色。
 *   - `--primary-color`, `--secondary-color`, `--accent-color`: 定义了主要的品牌颜色。
 *   - `--success-color`, `--warning-color`, `--danger-color`, `--info-color`: 定义了不同状态指示器（成功、警告、危险、信息）的颜色。
 *   - `--shadow-color`, `--overlay-color`: 定义了阴影和覆盖层的颜色。
 */
/* 设计令牌 - 颜色系统 */
:root {
  /* 品牌紫色调色板 */
  --primary-50: #faf5ff;
  --primary-100: #f3e8ff;
  --primary-200: #e9d5ff;
  --primary-300: #d8b4fe;
  --primary-400: #c084fc;
  --primary-500: #a855f7;
  --primary-600: #9333ea;
  --primary-700: #7c3aed;
  --primary-800: #6b21a8;
  --primary-900: #581c87;

  /* 中性色调色板 */
  --neutral-0: #ffffff;
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;

  /* 语义颜色 - 将根据主题动态变化 */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;
  --background-color: #ffffff;
  --background-secondary: #f9fafb;
  --surface-color: #ffffff;
  --surface-elevated: #ffffff;
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --border-focus: var(--primary-500);

  /* 品牌颜色 */
  --primary-color: var(--primary-500);
  --primary-dark: var(--primary-600);
  --primary-light: var(--primary-100);
  --secondary-color: #b83dba;
  --accent-color: var(--primary-600);

  /* 状态颜色 */
  --success-color: #10b981;
  --success-light: #d1fae5;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-light: #fee2e2;
  --info-color: #3b82f6;
  --info-light: #dbeafe;

  /* 覆盖层和阴影 */
  --shadow-color: rgba(0, 0, 0, 0.1);
  --overlay-color: rgba(0, 0, 0, 0.5);
}