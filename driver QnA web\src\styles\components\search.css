/*
 * 文件路径: src/styles/components/search.css
 * 文件描述: 定义了应用程序中与搜索相关的组件样式，包括搜索输入框、Gemini切换按钮、搜索建议下拉框和搜索结果头部。它融合了玻璃拟态效果、响应式设计和可访问性考虑。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于在以下文件中定义的CSS变量（设计令牌）：
 *     - `src/styles/tokens/spacing.css` (用于间距)。
 *     - `src/styles/tokens/typography.css` (用于字体大小和字重)。
 *     - `src/styles/tokens/colors.css` (用于文本和背景颜色)。
 *     - `src/styles/tokens/radius.css` (用于 `border-radius`)。
 *     - `src/styles/tokens/shadows.css` (用于 `box-shadow`)。
 *     - `src/styles/tokens/transitions.css` (用于 `transition` 属性)。
 *     - `src/styles/themes/variables.css` (用于玻璃拟态效果的颜色和模糊值，以及渐变)。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 提供一致且直观的搜索界面。
 *   - 将搜索组件与整体玻璃拟态设计视觉融合。
 *   - 为搜索输入焦点、建议和结果提供清晰的视觉反馈。
 *   - 确保在不同设备和用户偏好下的响应性和可访问性。
 * 关键部分/规则:
 *   - `.search-container`: 搜索栏的主要容器，使用flexbox布局。
 *   - `.search-input-wrapper`: 包装搜索输入框，应用玻璃拟态背景、边框和模糊效果。包含 `focus-within` 效果以提供视觉反馈。
 *   - `.search-input`: 样式化实际的文本输入字段，重置默认浏览器样式并设置字体/颜色。
 *   - `.gemini-toggle`: 样式化用于切换Gemini AI功能的按钮，具有玻璃拟态效果和激活/悬停状态。
 *   - `#searchSuggestions`: 样式化搜索建议的下拉容器，包括定位、背景、边框、阴影和滚动行为。它使用不透明度和变换来实现平滑的显示/隐藏动画。
 *   - `.suggestion-item`: 样式化单个搜索建议项，具有悬停和高亮状态，用于键盘导航。
 *   - `.suggestion-content`, `.suggestion-title`, `.suggestion-preview`, `.suggestion-icon`: 建议项内部内容的样式。
 *   - `.search-header`: 样式化搜索结果页面的头部，应用渐变、阴影和响应式文本大小。
 *   - **响应式优化 (`@media`)**: 调整小屏幕的内边距、字体大小和最小宽度。
 *   - **暗色主题适配 (`[data-theme="dark"]`)**: 覆盖暗色模式下的特定样式，确保适当的对比度和可见性。
 *   - **可访问性 (`@media (prefers-reduced-motion: reduce)`, `@media (prefers-contrast: high)`)**: 禁用变换动画并调整边框宽度以适应可访问性偏好。
 * 使用约定:
 *   - HTML中与搜索功能相关的元素应使用这些类（例如，`<div class="search-input-wrapper">`，`<input class="search-input">`）。
 *   - `#searchSuggestions` ID专门用于搜索建议下拉框。
 */
/* 组件样式 - 搜索组件 */

/* 搜索容器 */
.search-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  max-width: 600px;
}

/* 搜索输入框包装器 */
.search-input-wrapper {
  position: relative;
  background: var(--glass-background-intense);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-input);
  display: flex;
  align-items: center;
  padding: 0 var(--space-md);
  flex: 1;
  min-width: 200px;
  transition: var(--transition-all);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.search-input-wrapper:focus-within {
  background: var(--glass-background-intense);
  border-color: var(--glass-border-intense);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

/* 搜索输入框 */
.search-input {
  flex: 1;
  background: none;
  border: none;
  color: var(--text-inverse);
  font-size: var(--font-base);
  font-weight: var(--font-weight-normal);
  padding: var(--space-sm) 0;
  outline: none;
  transition: var(--transition-colors);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-weight: var(--font-weight-normal);
}

.search-input:focus {
  color: var(--text-inverse);
}

/* Gemini切换按钮 */
.gemini-toggle {
  background: var(--glass-background);
  border: 1px solid var(--glass-border-subtle);
  color: var(--text-inverse);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius);
  font-size: var(--font-xs);
  cursor: pointer;
  transition: var(--transition-button);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.gemini-toggle:hover {
  background: var(--glass-background-intense);
  border-color: var(--glass-border);
  transform: translateY(-1px);
}

.gemini-toggle.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.gemini-icon {
  font-size: var(--font-sm);
}

/* 搜索建议浮窗 */
#searchSuggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-dropdown);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 1001;
  max-height: 300px;
  overflow-y: auto;
  margin-top: var(--space-xs);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition-modal);
}

#searchSuggestions.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* 搜索建议项 */
.suggestion-item {
  padding: var(--space-md) var(--space-lg);
  cursor: pointer;
  transition: var(--transition-colors);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.highlighted {
  background-color: var(--background-secondary);
}

.suggestion-item.highlighted {
  background-color: var(--primary-50);
}

/* 搜索建议内容 */
.suggestion-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.suggestion-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-sm);
}

.suggestion-preview {
  color: var(--text-secondary);
  font-size: var(--font-xs);
  line-height: var(--leading-tight);
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.suggestion-icon {
  color: var(--primary-500);
  font-size: var(--font-lg);
  flex-shrink: 0;
}

/* 搜索结果页面头部 */
.search-header {
  background: var(--gradient-header);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl) var(--space-lg);
  margin-bottom: var(--space-2xl);
  text-align: center;
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
}

.search-header .back-btn {
  align-self: flex-start;
  margin-bottom: 0;
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
}

.search-header .back-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.search-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent
  );
}

.search-header h2 {
  font-size: var(--font-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.search-header p {
  font-size: var(--font-base);
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.15);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-lg); /* 长方圆角 */
  display: inline-block;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

/* 响应式优化 */
@media (max-width: 480px) {
  .search-input-wrapper {
    min-width: 150px;
    padding: 0 var(--space-sm);
  }
  
  .search-input {
    font-size: var(--font-sm);
    padding: var(--space-xs) 0;
  }
  
  .gemini-toggle {
    padding: var(--space-xs);
    font-size: var(--font-xs);
  }
  
  .search-header {
    padding: var(--space-lg) var(--space-md);
    margin-bottom: var(--space-lg);
  }
  
  .search-header .back-btn {
    font-size: var(--font-xs);
    padding: var(--space-xs) var(--space-sm);
    margin-bottom: var(--space-sm);
  }
  
  .search-header h2 {
    font-size: var(--font-xl);
  }
  
  .search-header p {
    font-size: var(--font-sm);
    padding: var(--space-xs) var(--space-md);
  }
}

@media (max-width: 320px) {
  .search-input-wrapper {
    padding: 0 var(--space-sm);
  }
  
  .search-input {
    font-size: var(--font-sm);
    padding: var(--space-xs) 0;
  }
  
  .search-header {
    padding: var(--space-md) var(--space-sm);
  }
  
  .search-header .back-btn {
    font-size: var(--font-xs);
    padding: var(--space-xs) var(--space-sm);
  }
}

/* 暗色主题适配 */
[data-theme="dark"] .search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] #searchSuggestions {
  background: var(--surface-elevated);
  border-color: var(--border-color);
}

[data-theme="dark"] .suggestion-item:hover,
[data-theme="dark"] .suggestion-item.highlighted {
  background-color: var(--surface-elevated);
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .search-input-wrapper:focus-within {
    transform: none;
  }
  
  .gemini-toggle:hover {
    transform: none;
  }
  
  #searchSuggestions {
    transition: opacity var(--transition-fast);
    transform: none;
  }
  
  #searchSuggestions.show {
    transform: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .search-input-wrapper {
    border-width: 2px;
  }
  
  .search-input-wrapper:focus-within {
    box-shadow: 0 0 0 3px var(--primary-500);
  }
}