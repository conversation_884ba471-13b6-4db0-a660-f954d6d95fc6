# 📸 GoMyHire FAQ 图片资源管理

## 📁 文件夹结构说明

```
images/
├── icons/                    # 图标资源
│   ├── categories/          # 分类图标
│   ├── actions/            # 操作图标
│   ├── status/             # 状态图标
│   └── social/             # 社交媒体图标
├── illustrations/          # 插画资源
│   ├── onboarding/         # 引导插画
│   ├── empty-states/       # 空状态插画
│   └── error-states/       # 错误状态插画
├── demo-screenshots/       # 演示截图
│   ├── mobile/            # 移动端截图
│   ├── tablet/            # 平板端截图
│   └── desktop/           # 桌面端截图
├── categories/            # 分类背景图
├── placeholders/          # 占位图片
├── social-media/          # 社交媒体分享图
├── logos/                 # 品牌Logo
└── animations/            # 动画资源
    ├── loading/          # 加载动画
    └── transitions/      # 过渡动画
```

## 📝 命名规范

### 图标命名格式
```
{类型}_{描述}_{尺寸}.{格式}
例如：
- icon_category_technical_32px.svg
- icon_action_download_24px.png
- icon_status_success_16px.svg
```

### 截图命名格式
```
{设备}_{功能}_{状态}_{尺寸}.{格式}
例如：
- mobile_order-status_demo_375x667.png
- tablet_registration_flow_768x1024.png
- desktop_search_results_1200x800.png
```

### 插画命名格式
```
{场景}_{描述}_{主题}_{尺寸}.{格式}
例如：
- illustration_onboarding_welcome_light_400x300.svg
- illustration_empty_search-results_purple_300x200.png
```

## 🎨 尺寸规范

### 图标尺寸
- **小图标**：16x16px, 24x24px
- **中图标**：32x32px, 48x48px
- **大图标**：64x64px, 128x128px

### 截图尺寸
- **手机**：375x667px (iPhone 6/7/8)
- **平板**：768x1024px (iPad)
- **桌面**：1200x800px (标准桌面)

### 插画尺寸
- **小插画**：200x150px
- **中插画**：400x300px
- **大插画**：800x600px

## 🔧 文件格式

### 推荐使用
- **SVG**：矢量图标、简单插画
- **PNG**：截图、复杂图片
- **WebP**：现代浏览器优化
- **GIF**：简单动画
- **MP4/WebM**：复杂动画

### 压缩要求
- **SVG**：≤ 50KB (使用SVGO优化)
- **PNG**：≤ 200KB (使用TinyPNG压缩)
- **WebP**：≤ 100KB

## 📱 响应式图片

### 图片集示例
```html
<img 
    src="images/icons/status-success-32px.svg"
    srcset="images/icons/status-success-16px.svg 1x,
            images/icons/status-success-32px.svg 2x,
            images/icons/status-success-48px.svg 3x"
    alt="成功状态图标"
>
```

### 响应式图片
```html
<picture>
    <source media="(max-width: 480px)" srcset="images/mobile-hero-375x200.webp">
    <source media="(max-width: 768px)" srcset="images/tablet-hero-768x400.webp">
    <img src="images/desktop-hero-1200x600.webp" alt="GoMyHire司机端">
</picture>
```

## 🎯 使用说明

### 替换图片步骤
1. **准备图片**：按照命名规范准备图片文件
2. **放置文件**：将图片放入对应文件夹
3. **更新引用**：在代码中更新图片路径
4. **测试显示**：检查不同设备上的显示效果

### 占位图片
使用占位图片进行开发，后续替换为实际图片：
```html
<!-- 使用占位图片 -->
<img src="images/placeholders/category-technical.svg" data-real-src="images/icons/category/technical.svg">
```

## 📊 资源清单

### 必需图片
- [ ] Logo（主Logo + 反白版本）
- [ ] 分类图标（技术、财务、服务等）
- [ ] 状态图标（成功、警告、错误）
- [ ] 引导插画（注册、使用指南）
- [ ] 空状态插画（无结果、错误页）

### 可选图片
- [ ] 社交媒体分享图
- [ ] 应用商店截图
- [ ] 功能演示GIF
- [ ] 品牌背景图

## 🔄 自动化工具

### 图片优化脚本
```bash
# 优化所有SVG文件
npm run optimize:svg

# 压缩所有PNG文件
npm run optimize:png

# 生成WebP格式
npm run generate:webp
```

### 图片验证
```bash
# 检查图片完整性
npm run validate:images

# 检查尺寸规范
npm run check:sizes
```

## 📞 技术支持

如遇到图片相关问题：
- **设计规范**：联系设计团队
- **技术问题**：联系开发团队
- **资源需求**：联系产品经理

---
**最后更新**：2024年8月31日  
**版本**：v1.0.0