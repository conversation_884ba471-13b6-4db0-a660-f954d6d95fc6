/*
 * 文件路径: src/core/local-env-loader.js
 * 文件描述: 本地开发环境专用环境变量加载器，完全避免使用服务器端点
 */
(function() {
    'use strict';
    
    class LocalEnvironmentLoader {
        constructor() {
            this.variables = new Map();
            this.isLoaded = false;
        }
        
        async loadEnvironmentVariables() {
            if (this.isLoaded) return;
            
            try {
                console.log('🔍 使用本地环境变量加载器...');
                
                // 确保DOM已加载完成
                if (document.readyState === 'loading') {
                    await new Promise(resolve => {
                        document.addEventListener('DOMContentLoaded', resolve);
                    });
                }
                
                // 方法1: 从 meta 标签获取（优先）
                this.loadFromMetaTags();
                
                // 方法2: 从 localStorage 获取（开发环境）
                this.loadFromLocalStorage();
                
                // 方法3: 从 JSON 脚本获取
                this.loadFromJsonScript();
                
                // 方法4: 从 URL 参数获取（调试用）
                this.loadFromUrlParams();
                
                this.isLoaded = true;
                
                // 触发环境变量加载完成事件
                if (typeof window !== 'undefined') {
                    window.dispatchEvent(new Event('environmentLoaded'));
                    // 注意：不再直接调用 window.initializeApiKeyWhenReady()
                    // 让 config.js 中的事件监听器来处理初始化
                }
                
                console.log('✅ 本地环境变量加载完成');
                
            } catch (error) {
                console.warn('⚠️ 本地环境变量加载失败:', error.message);
            }
        }
        
        // 从 meta 标签获取环境变量
        loadFromMetaTags() {
            console.log('🔍 开始扫描meta标签...');
            console.log('📄 当前文档状态:', document.readyState);
            console.log('📄 文档头信息:', document.head ? document.head.innerHTML.substring(0, 200) + '...' : '无head');
            
            // 确保head已加载
            if (!document.head) {
                console.warn('⚠️ 文档head未找到，等待DOM完成...');
                return;
            }
            
            // 更全面地搜索meta标签
            const selectors = [
                'meta[name^="env:"]',
                'meta[name="env:GEMINI_API_KEY"]',
                'meta[name="env:GEMINI_MODEL"]'
            ];
            
            let allMetaTags = [];
            selectors.forEach(selector => {
                const tags = document.querySelectorAll(selector);
                console.log(`🔍 ${selector}: 找到 ${tags.length} 个标签`);
                tags.forEach(tag => {
                    if (!allMetaTags.includes(tag)) {
                        allMetaTags.push(tag);
                    }
                });
            });
            
            console.log(`🔍 总共找到 ${allMetaTags.length} 个独特的meta标签`);
            
            let count = 0;
            allMetaTags.forEach(meta => {
                const key = meta.name.replace('env:', '');
                const value = meta.content;
                console.log(`🔍 发现meta标签: name="${meta.name}" content="${value?.substring(0, 20)}..."`);
                
                if (key && value && !this.isPlaceholderValue(value)) {
                    this.setVariable(key, value);
                    count++;
                    console.log(`✅ 从meta标签添加: ${key}=${value.substring(0, 8)}...`);
                } else {
                    console.log(`❌ 跳过meta标签: ${key}(${!key?'no key':!value?'no value':'invalid value'})`);
                }
            });
            console.log(`📋 meta标签加载完成，共${count}个变量`);
            
            // 额外调试：显示所有meta标签
            console.log('📋 所有meta标签:');
            document.querySelectorAll('meta').forEach((meta, index) => {
                console.log(`  ${index + 1}. name="${meta.name}" content="${meta.content}"`);
            });
        }
        
        // 从 localStorage 获取
        loadFromLocalStorage() {
            const keys = [
                'GEMINI_API_KEY',
                'GEMINI_MODEL',
                'ENABLE_API_FEATURES'
            ];
            
            keys.forEach(key => {
                const value = localStorage.getItem(`env_${key}`);
                if (value && !this.isPlaceholderValue(value)) {
                    this.setVariable(key, value);
                    console.log(`💾 从localStorage加载: ${key}`);
                }
            });
        }
        
        // 从 JSON 脚本获取
        loadFromJsonScript() {
            console.log('📄 开始扫描JSON脚本...');
            const scripts = [
                'script[type="application/json"][id="netlify-env"]',
                'script[type="application/json"][id="local-env"]'
            ];
            
            for (const selector of scripts) {
                const envScript = document.querySelector(selector);
                console.log(`📄 检查脚本: ${selector}`, envScript ? '找到' : '未找到');
                
                if (envScript) {
                    try {
                        console.log('📄 原始JSON内容:', envScript.textContent.trim());
                        const env = JSON.parse(envScript.textContent);
                        console.log('📄 解析后的JSON:', env);
                        
                        Object.entries(env).forEach(([key, value]) => {
                            console.log(`📄 处理JSON变量: ${key}=${value?.substring(0, 8)}...`);
                            if (!this.isPlaceholderValue(value)) {
                                this.setVariable(key, value);
                                console.log(`✅ 从JSON脚本添加: ${key}`);
                            } else {
                                console.log(`❌ 跳过JSON变量: ${key}(${value})`);
                            }
                        });
                        break; // 找到第一个有效的就停止
                    } catch (error) {
                        console.warn(`❌ JSON脚本解析失败 [${selector}]:`, error);
                    }
                }
            }
        }
        
        // 从 URL 参数获取（调试用）
        loadFromUrlParams() {
            if (typeof window === 'undefined') return;
            
            const urlParams = new URLSearchParams(window.location.search);
            const keys = ['api_key', 'model', 'debug'];
            
            keys.forEach(key => {
                const value = urlParams.get(key);
                if (value) {
                    const configKey = key === 'api_key' ? 'GEMINI_API_KEY' : 
                                    key === 'model' ? 'GEMINI_MODEL' : key.toUpperCase();
                    this.setVariable(configKey, value);
                    console.log(`🔗 从URL参数加载: ${configKey}`);
                }
            });
        }
        
        // 设置变量
        setVariable(key, value) {
            if (!key || typeof key !== 'string') return;

            // 清理值
            value = this.cleanValue(value);

            this.variables.set(key, value);
        }
        
        // 获取变量
        getVariable(key, defaultValue = null) {
            return this.variables.get(key) || defaultValue;
        }
        
        // 获取所有变量
        getAllVariables() {
            return Object.fromEntries(this.variables);
        }
        
        // 清理值
        cleanValue(value) {
            if (typeof value !== 'string') return value;
            return value.trim();
        }
        

        
        // 检查是否为占位符值
        isPlaceholderValue(value) {
            if (!value || typeof value !== 'string') return true;

            // 如果值看起来像一个有效的API密钥，那么它就不是占位符
            if (/^AIza[0-9A-Za-z_-]{35}$/.test(value)) {
                return false;
            }

            // 如果值看起来像一个有效的模型名称（包含字母、数字、点、横线），也不是占位符
            if (/^[a-zA-Z0-9.-]+$/.test(value) && value.length > 3 && value.length < 50) {
                return false;
            }

            const placeholders = [
                'your-gemini-api-key-here',
                'your-api-key',
                'XXXXX',
                'placeholder',
                'undefined',
                'null',
                'demo',
                ''
            ];

            return placeholders.some(p =>
                value.toLowerCase().includes(p.toLowerCase())
            );
        }
        
        // 验证 Gemini API 密钥格式
        validateGeminiApiKey(key) {
            if (!key || typeof key !== 'string') return false;
            return /^AIza[0-9A-Za-z_-]{35}$/.test(key);
        }
        
        // 应用到配置对象
        applyToConfig(config) {
            console.log('🔧 开始应用配置到CONFIG...');
            console.log('当前变量:', this.getAllVariables());
            
            const geminiApiKey = this.getVariable('GEMINI_API_KEY');
            console.log('找到的GEMINI_API_KEY:', geminiApiKey ? geminiApiKey.substring(0, 8) + '...' : '未找到');
            
            if (geminiApiKey && this.validateGeminiApiKey(geminiApiKey)) {
                config.gemini.apiKey = geminiApiKey;
                config.gemini.enabled = true;
                console.log('✅ Gemini API密钥已设置到CONFIG');
            } else {
                console.log('⚠️ 未找到有效的GEMINI_API_KEY，AI功能已禁用');
                console.log('原因:', !geminiApiKey ? '未找到密钥' : !this.validateGeminiApiKey(geminiApiKey) ? '格式无效' : '未知');
                config.gemini.enabled = false;
            }
            
            // 应用其他配置
            const model = this.getVariable('GEMINI_MODEL');
            if (model) {
                config.gemini.model = model;
                console.log('🤖 Gemini模型已设置:', model);
            }
            
            const enableApi = this.getVariable('ENABLE_API_FEATURES');
            if (enableApi === 'false') {
                config.gemini.enabled = false;
                console.log('🚫 API功能已手动禁用');
            }
        }
    }
    
    // 开发环境帮助函数
    function setLocalDevApiKey(apiKey) {
        if (!window.localEnvironmentLoader.validateGeminiApiKey(apiKey)) {
            console.error('❌ 无效的 API 密钥格式');
            return false;
        }
        
        const envVars = { GEMINI_API_KEY: apiKey };
        localStorage.setItem('env_GEMINI_API_KEY', apiKey);
        
        // 重新加载配置
        window.localEnvironmentLoader.setVariable('GEMINI_API_KEY', apiKey);
        if (window.CONFIG) {
            window.localEnvironmentLoader.applyToConfig(window.CONFIG);
            // 重新初始化API密钥验证
            if (window.initializeSecureApiKey) {
                window.initializeSecureApiKey(window.CONFIG);
            }
        }
        
        console.log('✅ 本地开发环境API密钥已设置');
        return true;
    }
    
    // 清除开发环境变量
    function clearLocalDevEnvironment() {
        localStorage.removeItem('env_GEMINI_API_KEY');
        localStorage.removeItem('env_GEMINI_MODEL');
        console.log('🧹 本地开发环境变量已清除');
        
        // 重新加载页面应用更改
        location.reload();
    }
    
    // 调试信息
    function printEnvironmentStatus() {
        if (window.localEnvironmentLoader) {
            const vars = window.localEnvironmentLoader.getAllVariables();
            console.log('🔍 当前环境变量:', vars);
            console.log('🌍 当前环境:', {
                hostname: window.location.hostname,
                protocol: window.location.protocol,
                search: window.location.search
            });
        }
    }
    
    // 导出到全局
    if (typeof window !== 'undefined') {
        window.localEnvironmentLoader = new LocalEnvironmentLoader();
        window.setLocalDevApiKey = setLocalDevApiKey;
        window.clearLocalDevEnvironment = clearLocalDevEnvironment;
        window.printEnvironmentStatus = printEnvironmentStatus;
        
        // 自动加载环境变量（无服务器依赖）
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                window.localEnvironmentLoader.loadEnvironmentVariables();
            });
        } else {
            window.localEnvironmentLoader.loadEnvironmentVariables();
        }
    }
    
})();