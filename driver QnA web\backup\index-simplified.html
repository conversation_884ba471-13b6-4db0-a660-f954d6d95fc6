<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire Driver FAQ - 简化版本</title>
    <meta name="description" content="GoMyHire司机常见问题解答系统">
    <link rel="stylesheet" href="simple-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <style>
        /* 内联关键CSS，优化首屏加载 */
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🚗 GoMyHire Driver FAQ</div>
                <div class="language-switcher">
                    <button class="lang-btn active" data-lang="zh">中文</button>
                    <button class="lang-btn" data-lang="en">English</button>
                    <button class="lang-btn" data-lang="ms">Bahasa</button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="container">
        <!-- 搜索区域 -->
        <section class="search-section">
            <input 
                type="search" 
                id="searchInput" 
                class="search-input" 
                placeholder="搜索常见问题..." 
                autocomplete="off"
                spellcheck="false">
        </section>

        <!-- 搜索结果 -->
        <section id="searchResults" class="search-results">
            <!-- 动态内容 -->
        </section>

        <!-- 默认内容 -->
        <section id="welcomeContent" class="welcome-content">
            <div class="welcome-message text-center">
                <h2>欢迎使用GoMyHire司机FAQ系统</h2>
                <p>在上方搜索框输入关键词，快速找到问题答案</p>
                <div class="mt-2">
                    <small>支持中文、英文、马来文搜索</small>
                </div>
            </div>
        </section>
    </main>

    <!-- 问题详情模态框 -->
    <div id="questionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle" class="modal-title"></h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalContent" class="modal-body"></div>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="config.js"></script>
    <script src="i18n.js"></script>
    <script src="data.js"></script>
    
    <!-- 可选的Gemini助手 -->
    <script src="gemini-assistant.js" defer></script>
    
    <!-- 简化的主应用 -->
    <script src="app.js"></script>

    <script>
        // 模态框控制
        function closeModal() {
            const modal = document.getElementById('questionModal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        // 点击模态框外部关闭
        document.addEventListener('click', (e) => {
            const modal = document.getElementById('questionModal');
            if (e.target === modal) {
                closeModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // 隐藏欢迎内容当搜索时
        const searchInput = document.getElementById('searchInput');
        const welcomeContent = document.getElementById('welcomeContent');
        
        if (searchInput && welcomeContent) {
            searchInput.addEventListener('input', (e) => {
                const hasQuery = e.target.value.trim().length > 0;
                welcomeContent.style.display = hasQuery ? 'none' : 'block';
            });
        }

        // 简单的性能监控
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`📊 页面加载时间: ${Math.round(loadTime)}ms`);
            
            if (window.app) {
                console.log('📊 应用状态:', window.app.getSystemStatus());
            }
        });
    </script>
</body>
</html>