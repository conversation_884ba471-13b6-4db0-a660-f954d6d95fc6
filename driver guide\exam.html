<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire 规则考核系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.min.js"></script>
    <script src="gemini-api.js"></script>
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --secondary: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #6366f1;
            --light: #f8fafc;
            --dark: #1e293b;
            --gray: #64748b;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .exam-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .chat-bubble {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .typing-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .typing-indicator span {
            width: 8px;
            height: 8px;
            background: var(--gray);
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
            transition: width 0.3s ease;
        }

        .score-card {
            background: linear-gradient(135deg, var(--info) 0%, var(--primary) 100%);
            color: white;
        }

        .rule-tag {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-positive {
            background: rgba(16, 185, 129, 0.1);
            color: var(--secondary);
            border-left: 4px solid var(--secondary);
        }

        .feedback-negative {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border-left: 4px solid var(--danger);
        }

        .export-btn {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .hidden {
            display: none !important;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .exam-container {
                padding: 1rem;
            }
            
            .grid md\\:grid-cols-3 {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .grid md\\:grid-cols-2 {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .chat-bubble {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .chat-bubble.justify-end {
                align-items: flex-end;
            }
            
            .flex.items-start.space-x-4 {
                flex-direction: column;
                gap: 1rem;
            }
            
            .flex.flex-col.space-y-2 {
                flex-direction: row;
                gap: 0.5rem;
            }
            
            button {
                font-size: 0.875rem;
                padding: 0.5rem 1rem;
            }
            
            h1 {
                font-size: 1.875rem;
            }
            
            h2 {
                font-size: 1.5rem;
            }
            
            h3 {
                font-size: 1.125rem;
            }
        }

        /* 改进的对话样式 */
        .chat-container {
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .chat-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .chat-area {
            max-height: 400px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        .chat-area::-webkit-scrollbar {
            width: 6px;
        }

        .chat-area::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .chat-area::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .chat-area::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 改进的输入区域 */
        .input-area {
            background: white;
            border-top: 1px solid #e2e8f0;
            border-radius: 0 0 12px 12px;
        }

        .user-input {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .user-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        /* 改进的按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:disabled {
            background: #94a3b8;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
            transform: translateY(-1px);
        }

        /* 改进的反馈样式 */
        .feedback-card {
            border-radius: 12px;
            padding: 1rem;
            margin: 0.5rem 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .feedback-success {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border-left: 4px solid #16a34a;
        }

        .feedback-warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-left: 4px solid #f59e0b;
        }

        .feedback-error {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border-left: 4px solid #ef4444;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 改进的进度条 */
        .progress-container {
            background: #f1f5f9;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress-fill {
            height: 8px;
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 0.5rem;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body class="p-4">
    <div class="max-w-6xl mx-auto">
        <!-- 考试开始界面 -->
        <div id="startScreen" class="exam-container p-8 text-center fade-in">
            <div class="mb-8">
                <i data-lucide="graduation-cap" class="w-20 h-20 mx-auto mb-4 text-blue-600"></i>
                <h1 class="text-4xl font-bold text-gray-800 mb-2" data-key="exam-title">GoMyHire 规则考核系统</h1>
                <p class="text-gray-600 text-lg" data-key="exam-subtitle">基于平台规则的对话式考核模块</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-3 text-blue-500"></i>
                    <h3 class="font-semibold text-lg mb-2" data-key="exam-feature-1-title">30轮对话</h3>
                    <p class="text-gray-600 text-sm" data-key="exam-feature-1-desc">涵盖所有重要规则要点</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <i data-lucide="brain" class="w-12 h-12 mx-auto mb-3 text-green-500"></i>
                    <h3 class="font-semibold text-lg mb-2" data-key="exam-feature-2-title">AI智能评估</h3>
                    <p class="text-gray-600 text-sm" data-key="exam-feature-2-desc">实时反馈和指导</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <i data-lucide="file-text" class="w-12 h-12 mx-auto mb-3 text-purple-500"></i>
                    <h3 class="font-semibold text-lg mb-2" data-key="exam-feature-3-title">成绩单导出</h3>
                    <p class="text-gray-600 text-sm" data-key="exam-feature-3-desc">详细考核报告</p>
                </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
                <h4 class="font-semibold text-yellow-800 mb-2" data-key="exam-notice-title">考试须知</h4>
                <ul class="text-sm text-yellow-700 text-left space-y-1">
                    <li data-key="exam-notice-1">• 考试时间：60分钟</li>
                    <li data-key="exam-notice-2">• 题目形式：对话式问答</li>
                    <li data-key="exam-notice-3">• 评分标准：规则准确性和完整性</li>
                    <li data-key="exam-notice-4">• 及格分数：80分</li>
                </ul>
            </div>

            <button id="startExamBtn" class="export-btn text-white px-8 py-4 rounded-lg text-lg font-semibold" data-key="start-exam-btn">
                开始考试
            </button>
        </div>

        <!-- 考试进行界面 -->
        <div id="examScreen" class="hidden">
            <div class="grid lg:grid-cols-4 gap-6">
                <!-- 考试信息面板 -->
                <div class="lg:col-span-1">
                    <div class="exam-container p-6 mb-6">
                        <h3 class="text-lg font-semibold mb-4 flex items-center">
                            <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                            <span data-key="exam-info-title">考试信息</span>
                        </h3>
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm text-gray-600" data-key="current-progress">当前进度</p>
                                <p class="text-2xl font-bold text-blue-600"><span id="currentRound">1</span>/30</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600" data-key="remaining-time">剩余时间</p>
                                <p class="text-xl font-semibold text-orange-600" id="timeRemaining">60:00</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600" data-key="current-score">当前得分</p>
                                <p class="text-xl font-semibold text-green-600" id="currentScore">0</p>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <div class="flex justify-between text-sm mb-2">
                                <span data-key="progress">进度</span>
                                <span id="progressPercent">3%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" id="progressBar" style="width: 3%"></div>
                            </div>
                        </div>

                        <button id="exitExamBtn" class="w-full mt-6 bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg transition-colors" data-key="exit-exam-btn">
                            退出考试
                        </button>
                    </div>

                    <!-- 实时反馈面板 -->
                    <div class="exam-container p-6">
                        <h3 class="text-lg font-semibold mb-4 flex items-center">
                            <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2"></i>
                            <span data-key="real-time-feedback">实时反馈</span>
                        </h3>
                        <div class="space-y-3">
                            <div>
                                <p class="text-sm text-gray-600" data-key="rule-coverage">规则覆盖度</p>
                                <div class="flex items-center">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-500 h-2 rounded-full" id="coverageBar" style="width: 0%"></div>
                                    </div>
                                    <span class="text-sm font-semibold" id="coveragePercent">0%</span>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600" data-key="answer-accuracy">回答准确度</p>
                                <div class="flex items-center">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-500 h-2 rounded-full" id="accuracyBar" style="width: 0%"></div>
                                    </div>
                                    <span class="text-sm font-semibold" id="accuracyPercent">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 对话考核区域 -->
                <div class="lg:col-span-3">
                    <div class="chat-container rounded-lg shadow-lg">
                        <div class="chat-header p-6">
                            <h3 class="text-xl font-semibold mb-2 flex items-center">
                                <i data-lucide="message-square" class="w-6 h-6 mr-2"></i>
                                <span data-key="dialogue-assessment">对话考核</span>
                            </h3>
                            <div class="flex items-center justify-between text-sm opacity-90">
                                <span data-key="round-info">第 <span id="currentChatRound">1</span> / 30 轮</span>
                                <span data-key="time-remaining-info">剩余时间：<span id="remainingTime">60:00</span></span>
                            </div>
                        </div>
                        
                        <div id="chatArea" class="chat-area p-4 space-y-4">
                            <!-- 对话内容将在这里动态生成 -->
                        </div>

                        <div id="inputArea" class="input-area p-4">
                            <!-- 选择题选项区域 -->
                            <div id="optionsContainer" class="space-y-3 mb-4">
                                <!-- 选择题选项将在这里动态生成 -->
                            </div>
                            
                            <!-- 错误后对话区域 (默认隐藏) -->
                            <div id="clarificationArea" class="hidden">
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                    <p class="text-sm text-yellow-800 mb-3">
                                        <i data-lucide="help-circle" class="w-4 h-4 inline mr-1"></i>
                                        <span data-key="incorrect-answer-prompt">回答不正确，您可以在这里提问以获得更多解释：</span>
                                    </p>
                                    <div class="flex space-x-2">
                                        <input type="text" id="clarificationInput" 
                                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                               placeholder="请输入您的问题..." data-placeholder-key="enter-question-placeholder">
                                        <button id="submitClarificationBtn" 
                                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                                            <i data-lucide="send" class="w-4 h-4"></i>
                                            <span data-key="ask-question-btn">提问</span>
                                        </button>
                                        <button id="skipClarificationBtn" 
                                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                                            <span data-key="skip-btn">跳过</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 主要控制按钮 -->
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-gray-600" id="inputPrompt">
                                    <span data-key="select-best-answer">请选择最合适的答案</span>
                                </div>
                                <div class="flex space-x-3">
                                    <button id="submitAnswerBtn" class="btn-primary disabled:opacity-50" disabled>
                                        <i data-lucide="check" class="w-4 h-4 mr-2"></i>
                                        <span data-key="submit-answer-btn">提交答案</span>
                                    </button>
                                    <button id="skipQuestionBtn" class="btn-secondary" disabled>
                                        <i data-lucide="skip-forward" class="w-4 h-4 mr-2"></i>
                                        <span data-key="skip-question-btn">跳过</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成绩单界面 -->
        <div id="resultScreen" class="hidden">
            <div class="exam-container p-8 text-center">
                <div class="mb-8">
                    <i data-lucide="trophy" class="w-20 h-20 mx-auto mb-4 text-yellow-500"></i>
                    <h1 class="text-4xl font-bold text-gray-800 mb-2" data-key="exam-completed">考核完成</h1>
                    <p class="text-gray-600 text-lg" data-key="congratulations-msg">恭喜您完成GoMyHire规则考核</p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="score-card p-6 rounded-lg">
                        <i data-lucide="target" class="w-8 h-8 mx-auto mb-2"></i>
                        <h3 class="text-2xl font-bold" id="finalScore">0</h3>
                        <p class="text-sm opacity-90" data-key="total-score">总分</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <i data-lucide="percent" class="w-8 h-8 mx-auto mb-2 text-blue-500"></i>
                        <h3 class="text-2xl font-bold text-blue-600" id="finalAccuracy">0%</h3>
                        <p class="text-sm text-gray-600" data-key="accuracy-rate">准确率</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <i data-lucide="clock" class="w-8 h-8 mx-auto mb-2 text-green-500"></i>
                        <h3 class="text-2xl font-bold text-green-600" id="timeUsed">0分钟</h3>
                        <p class="text-sm text-gray-600" data-key="time-used">用时</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <i data-lucide="check-circle" class="w-8 h-8 mx-auto mb-2 text-purple-500"></i>
                        <h3 class="text-2xl font-bold text-purple-600" id="passStatus">待定</h3>
                        <p class="text-sm text-gray-600" data-key="exam-result">结果</p>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md mb-8">
                    <h3 class="text-lg font-semibold mb-4" data-key="detailed-report-title">详细考核报告</h3>
                    <div id="detailedReport" class="text-left">
                        <!-- 详细报告将在这里生成 -->
                    </div>
                </div>

                <div class="flex justify-center space-x-4">
                    <button id="exportReportBtn" class="export-btn text-white px-8 py-3 rounded-lg font-semibold">
                        <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                        <span data-key="export-report-btn">导出成绩单</span>
                    </button>
                    <button id="retakeExamBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold">
                        <i data-lucide="refresh-cw" class="w-4 h-4 inline mr-2"></i>
                        <span data-key="retake-exam-btn">重新考试</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化Lucide图标
        lucide.createIcons();

        // 考试系统类
        class ExamSystem {
            constructor() {
                this.currentRound = 0;
                this.totalRounds = 30;
                this.score = 0;
                this.startTime = null;
                this.timeLimit = 60 * 60 * 1000; // 60分钟
                this.timer = null;
                this.conversation = [];
                this.rulesCoverage = new Set();
                this.geminiApiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s'; // 替换为你的Gemini API密钥
                this.geminiAPI = new GeminiAPI(this.geminiApiKey);
                this.isExamActive = false;
                
                this.initializeEventListeners();
                this.loadRulesContent();
            }

            initializeEventListeners() {
                document.getElementById('startExamBtn').addEventListener('click', () => this.startExam());
                document.getElementById('exitExamBtn').addEventListener('click', () => this.exitExam());
                document.getElementById('submitAnswerBtn').addEventListener('click', () => this.submitAnswer());
                document.getElementById('skipQuestionBtn').addEventListener('click', () => this.skipQuestion());
                document.getElementById('exportReportBtn').addEventListener('click', () => this.exportReport());
                document.getElementById('retakeExamBtn').addEventListener('click', () => this.retakeExam());
                
                // 新增：提问功能事件监听器
                document.getElementById('submitClarificationBtn').addEventListener('click', () => this.submitClarification());
                document.getElementById('skipClarificationBtn').addEventListener('click', () => this.skipClarification());
                
                // 回车键提交提问
                document.getElementById('clarificationInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.submitClarification();
                    }
                });
            }

            loadRulesContent() {
                // 从现有页面加载规则内容
                this.rulesContent = {
                    communication: [
                        "与GoMyHire团队沟通的唯一渠道是平台内文字讯息功能",
                        "禁止接受或拨打任何电话",
                        "收到消息必须及时回复",
                        "与乘客沟通只有点击Arrived后才能使用订单聊天功能",
                        "不得主动添加乘客好友或私下联系"
                    ],
                    workflow: [
                        "接单前必须点击View More查看乘客信息",
                        "必须确认航班状态",
                        "订单前1小时必须点击On The Way",
                        "订单前15分钟必须点击Arrived",
                        "上车后必须三确认目的地地址",
                        "必须让客人出示酒店凭证确认地址"
                    ],
                    autoCancel: [
                        "2025年3月10日中午12:00开始生效",
                        "前1小时未点击On The Way会自动取消+封号",
                        "前15分钟未点击Arrived会自动取消+封号",
                        "WhatsApp警告机制：自动取消前发送2次警告"
                    ],
                    overtime: [
                        "接机服务免费等待90分钟（吉隆坡，槟城，新山）",
                        "接机服务免费等待60分钟（新加坡/沙巴，斗湖）",
                        "送机服务免费等待30分钟",
                        "超时后每30分钟收费"
                    ],
                    cancellation: [
                        "超过6小时取消：扣1积分",
                        "1-6小时内取消：扣2积分",
                        "少于1小时取消：扣4积分+封号处理",
                        "航班延误必须第一时间联系客服"
                    ]
                };
            }

            async startExam() {
                document.getElementById('startScreen').classList.add('hidden');
                document.getElementById('examScreen').classList.remove('hidden');
                
                this.isExamActive = true;
                this.startTime = Date.now();
                this.startTimer();
                
                await this.nextRound();
            }

            startTimer() {
                this.timer = setInterval(() => {
                    const elapsed = Date.now() - this.startTime;
                    const remaining = Math.max(0, this.timeLimit - elapsed);
                    
                    const minutes = Math.floor(remaining / 60000);
                    const seconds = Math.floor((remaining % 60000) / 1000);
                    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                    
                    // 更新主界面的时间显示
                    const timeRemainingElement = document.getElementById('timeRemaining');
                    if (timeRemainingElement) {
                        timeRemainingElement.textContent = timeString;
                    }
                    
                    // 更新对话界面的时间显示
                    const remainingTimeElement = document.getElementById('remainingTime');
                    if (remainingTimeElement) {
                        remainingTimeElement.textContent = timeString;
                    }
                    
                    if (remaining === 0) {
                        this.endExam();
                    }
                }, 1000);
            }

            async nextRound() {
                this.currentRound++;
                
                if (this.currentRound > this.totalRounds) {
                    this.endExam();
                    return;
                }

                this.updateProgress();
                
                // 生成新问题
                const question = await this.generateQuestion();
                this.displayQuestion(question);
                
                // 启用输入
                document.getElementById('submitAnswerBtn').disabled = false;
                document.getElementById('skipQuestionBtn').disabled = false;
            }

            async generateQuestion() {
                // 智能选择规则，确保覆盖所有类别
                const categories = Object.keys(this.rulesContent);
                let selectedCategory, selectedRule;
                
                // 前10轮优先覆盖未涉及的类别
                if (this.currentRound <= 10) {
                    const uncoveredCategories = categories.filter(cat => !this.rulesCoverage.has(cat));
                    if (uncoveredCategories.length > 0) {
                        selectedCategory = uncoveredCategories[Math.floor(Math.random() * uncoveredCategories.length)];
                    } else {
                        selectedCategory = categories[Math.floor(Math.random() * categories.length)];
                    }
                } else {
                    // 10轮后，根据之前的回答表现选择规则
                    const weakCategories = this.getWeakCategories();
                    if (weakCategories.length > 0 && Math.random() < 0.6) {
                        selectedCategory = weakCategories[Math.floor(Math.random() * weakCategories.length)];
                    } else {
                        selectedCategory = categories[Math.floor(Math.random() * categories.length)];
                    }
                }
                
                const rules = this.rulesContent[selectedCategory];
                selectedRule = rules[Math.floor(Math.random() * rules.length)];
                
                this.rulesCoverage.add(selectedCategory);
                
                // 使用Gemini API生成问题
                const difficulty = this.currentRound <= 10 ? 'easy' : 
                                 this.currentRound <= 20 ? 'medium' : 'hard';
                
                try {
                    const questionText = await this.geminiAPI.generateQuestion(selectedRule, selectedCategory, difficulty);
                    
                    return {
                        text: questionText,
                        category: selectedCategory,
                        rule: selectedRule,
                        difficulty: difficulty
                    };
                } catch (error) {
                    console.error('Error generating question with Gemini:', error);
                    // 回退到预设问题
                    const fallbackQuestions = [
                        `请详细说明"${selectedRule}"这个规则的具体要求和操作步骤。`,
                        `在实际工作中，您会如何确保"${selectedRule}"这个规则的执行？请举例说明。`,
                        `"${selectedRule}"违反了会有什么后果？如何避免？`,
                        `请解释"${selectedRule}"对服务质量的影响，以及为什么这个规则很重要。`
                    ];
                    
                    return {
                        text: fallbackQuestions[Math.floor(Math.random() * fallbackQuestions.length)],
                        category: randomCategory,
                        rule: randomRule,
                        difficulty: difficulty
                    };
                }
            }

            displayQuestion(question) {
                const chatArea = document.getElementById('chatArea');
                
                // 添加AI问题
                const questionDiv = document.createElement('div');
                questionDiv.className = 'chat-bubble flex items-start space-x-3';
                questionDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                        AI
                    </div>
                    <div class="flex-1">
                        <div class="bg-blue-100 p-4 rounded-lg">
                            <p class="text-sm text-blue-600 mb-1">第${this.currentRound}题 - ${this.getCategoryName(question.category)}</p>
                            <div class="bg-white p-3 rounded border-l-4 border-blue-500 mb-2">
                                <p class="text-sm font-semibold text-gray-700 mb-1">场景案例：</p>
                                <p class="text-gray-800">${question.scenario || question.text}</p>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">规则：${question.rule}</p>
                    </div>
                `;
                
                chatArea.appendChild(questionDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
                
                // 显示选项
                this.displayOptions(question.options || []);
                
                // 存储当前问题
                this.currentQuestion = question;
            }

            displayOptions(options) {
                const optionsContainer = document.getElementById('optionsContainer');
                optionsContainer.innerHTML = '';
                
                options.forEach((option, index) => {
                    const optionDiv = document.createElement('div');
                    optionDiv.className = 'option-item';
                    optionDiv.innerHTML = `
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all">
                            <input type="radio" name="answer" value="${index}" class="mr-3 text-blue-600">
                            <span class="flex-1">
                                <span class="font-semibold text-blue-600 mr-2">${String.fromCharCode(65 + index)}.</span>
                                ${option}
                            </span>
                        </label>
                    `;
                    
                    // 添加点击事件
                    const radioInput = optionDiv.querySelector('input[type="radio"]');
                    radioInput.addEventListener('change', () => {
                        // 移除所有选项的选中状态
                        document.querySelectorAll('.option-item label').forEach(label => {
                            label.classList.remove('border-blue-500', 'bg-blue-50');
                            label.classList.add('border-gray-200');
                        });
                        
                        // 添加当前选项的选中状态
                        const selectedLabel = radioInput.closest('label');
                        selectedLabel.classList.remove('border-gray-200');
                        selectedLabel.classList.add('border-blue-500', 'bg-blue-50');
                        
                        // 启用提交按钮
                        document.getElementById('submitAnswerBtn').disabled = false;
                    });
                    
                    optionsContainer.appendChild(optionDiv);
                });
            }

            getCategoryName(category) {
                const names = {
                    communication: '沟通规则',
                    workflow: '工作流程',
                    autoCancel: '自动取消',
                    overtime: '超时政策',
                    cancellation: '取消规则'
                };
                return names[category] || category;
            }

            getWeakCategories() {
                // 根据历史表现识别薄弱的规则类别
                const categoryPerformance = {};
                
                // 初始化所有类别
                Object.keys(this.rulesContent).forEach(category => {
                    categoryPerformance[category] = { total: 0, score: 0 };
                });
                
                // 计算每个类别的平均表现
                this.conversation.forEach(conv => {
                    if (conv.evaluation && conv.category) {
                        const perf = categoryPerformance[conv.category];
                        perf.total++;
                        perf.score += conv.evaluation.score;
                    }
                });
                
                // 找出平均分低于70分的类别
                const weakCategories = [];
                Object.keys(categoryPerformance).forEach(category => {
                    const perf = categoryPerformance[category];
                    if (perf.total > 0) {
                        const avgScore = perf.score / perf.total;
                        if (avgScore < 70) {
                            weakCategories.push(category);
                        }
                    }
                });
                
                return weakCategories;
            }

            async submitAnswer() {
                const selectedOption = document.querySelector('input[name="answer"]:checked');
                if (!selectedOption) return;

                const selectedAnswer = parseInt(selectedOption.value);
                const selectedText = this.currentQuestion.options[selectedAnswer];
                
                // 禁用输入
                document.querySelectorAll('input[name="answer"]').forEach(input => {
                    input.disabled = true;
                });
                document.getElementById('submitAnswerBtn').disabled = true;
                document.getElementById('skipQuestionBtn').disabled = true;

                // 显示用户回答
                this.displayUserAnswer(selectedText, selectedAnswer);

                // 显示正在评估
                this.showTypingIndicator();

                // 评估回答
                const evaluation = await this.evaluateAnswer(selectedText, selectedAnswer);
                
                // 移除输入指示器
                this.removeTypingIndicator();
                
                // 显示评估结果
                this.displayEvaluation(evaluation);
                
                // 更新分数
                this.updateScore(evaluation.score);
                
                // 存储对话记录
                this.conversation.push({
                    round: this.currentRound,
                    question: this.currentQuestion.text,
                    answer: selectedText,
                    evaluation: evaluation,
                    category: this.currentQuestion.category,
                    rule: this.currentQuestion.rule,
                    difficulty: this.currentQuestion.difficulty,
                    timestamp: new Date()
                });
                
                // 如果回答错误（低于60分），显示提问对话框
                if (evaluation.score < 60) {
                    this.showClarificationDialog();
                } else {
                    // 3秒后下一题
                    setTimeout(() => {
                        this.nextRound();
                    }, 3000);
                }
            }

            displayUserAnswer(answer, answerIndex = -1) {
                const chatArea = document.getElementById('chatArea');
                
                const answerDiv = document.createElement('div');
                answerDiv.className = 'chat-bubble flex items-start space-x-3 justify-end';
                answerDiv.innerHTML = `
                    <div class="flex-1 max-w-md">
                        <div class="bg-green-100 p-4 rounded-lg">
                            ${answerIndex >= 0 ? `
                                <p class="text-sm font-semibold text-green-700 mb-1">
                                    选择答案：${String.fromCharCode(65 + answerIndex)}
                                </p>
                            ` : ''}
                            <p class="text-gray-800">${answer}</p>
                        </div>
                    </div>
                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold">
                        我
                    </div>
                `;
                
                chatArea.appendChild(answerDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
            }

            showTypingIndicator() {
                const chatArea = document.getElementById('chatArea');
                
                const typingDiv = document.createElement('div');
                typingDiv.id = 'typingIndicator';
                typingDiv.className = 'chat-bubble flex items-start space-x-3';
                typingDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                        AI
                    </div>
                    <div class="bg-blue-100 p-4 rounded-lg">
                        <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                `;
                
                chatArea.appendChild(typingDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
            }

            removeTypingIndicator() {
                const indicator = document.getElementById('typingIndicator');
                if (indicator) {
                    indicator.remove();
                }
            }

            async evaluateAnswer(answer, answerIndex = -1) {
                try {
                    // 使用Gemini API进行智能评估
                    const evaluation = await this.geminiAPI.evaluateAnswer(
                        this.currentQuestion,
                        this.currentQuestion.rule,
                        answer,
                        this.currentQuestion.options || [],
                        this.currentQuestion.correctAnswer !== undefined ? this.currentQuestion.correctAnswer : -1
                    );
                    
                    // 将Gemini API返回的格式转换为系统需要的格式
                    return {
                        score: evaluation.score,
                        feedback: evaluation.feedback,
                        keywords: evaluation.keyPoints || [],
                        coverage: evaluation.score / 100,
                        strengths: evaluation.strengths || [],
                        improvements: evaluation.improvements || []
                    };
                } catch (error) {
                    console.error('Gemini API评估失败，使用备用评估:', error);
                    
                    // 备用评估逻辑 - 基于选择题的简单评估
                    const isCorrect = answerIndex === this.currentQuestion.correctAnswer;
                    let score = isCorrect ? 100 : 0;
                    
                    // 如果有解释，给予部分分数
                    if (!isCorrect && this.currentQuestion.explanation) {
                        score = 30; // 选择错误但有参与分
                    }
                    
                    const feedback = isCorrect ? 
                        `回答正确！${this.currentQuestion.explanation || '您选择了符合规则的答案。'}` :
                        `回答错误。正确答案是${String.fromCharCode(65 + this.currentQuestion.correctAnswer)}。${this.currentQuestion.explanation || ''}`;

                    return {
                        score: score,
                        feedback: feedback,
                        keywords: isCorrect ? ['规则理解正确'] : ['需要加强规则学习'],
                        coverage: score / 100,
                        strengths: isCorrect ? ['选择了正确答案'] : [],
                        improvements: isCorrect ? [] : ['建议重新学习相关规则内容']
                    };
                }
            }

            extractKeywords(rule) {
                // 简单的关键词提取
                return rule.split(/[\s，。、！？；：""''（）【】]/).filter(word => word.length > 1);
            }

            displayEvaluation(evaluation) {
                const chatArea = document.getElementById('chatArea');
                
                const evaluationDiv = document.createElement('div');
                evaluationDiv.className = 'chat-bubble flex items-start space-x-3';
                
                const feedbackClass = evaluation.score >= 80 ? 'feedback-success' : 
                                   evaluation.score >= 60 ? 'feedback-warning' : 'feedback-error';
                
                evaluationDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                        AI
                    </div>
                    <div class="flex-1">
                        <div class="feedback-card ${feedbackClass}">
                            <div class="flex items-center justify-between mb-3">
                                <span class="font-semibold text-lg">得分：${evaluation.score}/100</span>
                                <div class="flex items-center">
                                    ${evaluation.score >= 80 ? 
                                        '<i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>' :
                                        evaluation.score >= 60 ?
                                        '<i data-lucide="alert-circle" class="w-6 h-6 text-yellow-600"></i>' :
                                        '<i data-lucide="x-circle" class="w-6 h-6 text-red-600"></i>'
                                    }
                                </div>
                            </div>
                            <p class="text-sm mb-3">${evaluation.feedback}</p>
                            
                            ${evaluation.strengths && evaluation.strengths.length > 0 ? `
                                <div class="mb-3">
                                    <p class="text-sm font-semibold text-green-700 mb-2">
                                        <i data-lucide="star" class="w-4 h-4 inline mr-1"></i>优点:
                                    </p>
                                    <ul class="text-sm text-green-700 ml-6">
                                        ${evaluation.strengths.map(strength => `<li class="mb-1">• ${strength}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            
                            ${evaluation.improvements && evaluation.improvements.length > 0 ? `
                                <div class="mb-3">
                                    <p class="text-sm font-semibold text-orange-700 mb-2">
                                        <i data-lucide="trending-up" class="w-4 h-4 inline mr-1"></i>改进建议:
                                    </p>
                                    <ul class="text-sm text-orange-700 ml-6">
                                        ${evaluation.improvements.map(improvement => `<li class="mb-1">• ${improvement}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            
                            <div class="flex flex-wrap gap-2 mt-3">
                                ${evaluation.keywords.map(keyword => 
                                    `<span class="rule-tag px-3 py-1 rounded-full text-xs font-medium">${keyword}</span>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                `;
                
                chatArea.appendChild(evaluationDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
                
                // 重新初始化图标
                lucide.createIcons();
            }

            skipQuestion() {
                // 跳过当前题目
                this.displayUserAnswer('（跳过此题）');
                this.displayEvaluation({
                    score: 0,
                    feedback: '题目已跳过，不得分。',
                    keywords: [],
                    coverage: 0,
                    strengths: [],
                    improvements: ['建议尝试回答问题以获得更好的学习效果']
                });
                
                setTimeout(() => {
                    this.nextRound();
                }, 2000);
            }

            updateScore(points) {
                this.score += points;
                document.getElementById('currentScore').textContent = this.score;
                
                // 更新准确率
                const accuracy = Math.round((this.score / (this.currentRound * 100)) * 100);
                document.getElementById('accuracyPercent').textContent = accuracy + '%';
                document.getElementById('accuracyBar').style.width = accuracy + '%';
                
                // 更新规则覆盖度
                const coverage = Math.round((this.rulesCoverage.size / Object.keys(this.rulesContent).length) * 100);
                document.getElementById('coveragePercent').textContent = coverage + '%';
                document.getElementById('coverageBar').style.width = coverage + '%';
            }

            updateProgress() {
                const progress = Math.round((this.currentRound / this.totalRounds) * 100);
                document.getElementById('currentRound').textContent = this.currentRound;
                document.getElementById('progressPercent').textContent = progress + '%';
                document.getElementById('progressBar').style.width = progress + '%';
                
                // 更新对话界面的轮次显示
                const chatRoundElement = document.getElementById('currentChatRound');
                if (chatRoundElement) {
                    chatRoundElement.textContent = this.currentRound;
                }
            }

            endExam() {
                this.isExamActive = false;
                clearInterval(this.timer);
                
                document.getElementById('examScreen').classList.add('hidden');
                document.getElementById('resultScreen').classList.remove('hidden');
                
                this.generateReport();
            }

            generateReport() {
                const timeUsed = Math.round((Date.now() - this.startTime) / 60000);
                const accuracy = Math.round((this.score / (this.totalRounds * 100)) * 100);
                const passed = accuracy >= 80;
                
                document.getElementById('finalScore').textContent = this.score;
                document.getElementById('finalAccuracy').textContent = accuracy + '%';
                document.getElementById('timeUsed').textContent = timeUsed + '分钟';
                document.getElementById('passStatus').textContent = passed ? '通过' : '未通过';
                document.getElementById('passStatus').className = passed ? 'text-2xl font-bold text-green-600' : 'text-2xl font-bold text-red-600';
                
                // 生成详细报告
                const detailedReport = document.getElementById('detailedReport');
                const categoryAnalysis = this.getCategoryAnalysis();
                
                detailedReport.innerHTML = `
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold mb-2">考核统计</h4>
                                <ul class="text-sm space-y-1">
                                    <li>总题目数：${this.totalRounds}</li>
                                    <li>完成题目：${this.currentRound}</li>
                                    <li>平均得分：${Math.round(this.score / this.currentRound)}</li>
                                    <li>规则覆盖：${this.rulesCoverage.size}/${Object.keys(this.rulesContent).length}</li>
                                    <li>用时：${timeUsed}分钟</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-semibold mb-2">能力评估</h4>
                                <ul class="text-sm space-y-1">
                                    <li>规则理解：${this.getSkillLevel(accuracy)}</li>
                                    <li>应用能力：${this.getApplicationLevel()}</li>
                                    <li>学习建议：${this.getLearningAdvice()}</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold mb-2">规则领域分析</h4>
                            <div class="space-y-2">
                                ${categoryAnalysis.map(category => `
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <span class="text-sm">${this.getCategoryName(category.name)}</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-20 bg-gray-200 rounded-full h-2">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: ${category.accuracy}%"></div>
                                            </div>
                                            <span class="text-xs text-gray-600">${category.accuracy}%</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold mb-2">掌握的规则领域</h4>
                            <div class="flex flex-wrap gap-2">
                                ${Array.from(this.rulesCoverage).map(category => 
                                    `<span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">${this.getCategoryName(category)}</span>`
                                ).join('')}
                            </div>
                        </div>
                        
                        ${this.getWeakAreas().length > 0 ? `
                        <div>
                            <h4 class="font-semibold mb-2">需要加强的领域</h4>
                            <div class="flex flex-wrap gap-2">
                                ${this.getWeakAreas().map(category => 
                                    `<span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">${this.getCategoryName(category)}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                `;
            }

            getLearningAdvice() {
                const accuracy = Math.round((this.score / (this.totalRounds * 100)) * 100);
                if (accuracy >= 90) return '继续保持，可以尝试更高级的考核';
                if (accuracy >= 80) return '基础掌握良好，建议深入学习细节';
                if (accuracy >= 60) return '建议重新学习未掌握的规则内容';
                return '建议从头开始系统学习所有规则';
            }

            getSkillLevel(accuracy) {
                if (accuracy >= 90) return '优秀';
                if (accuracy >= 80) return '良好';
                if (accuracy >= 70) return '中等';
                if (accuracy >= 60) return '及格';
                return '需改进';
            }

            getApplicationLevel() {
                const applicationScore = this.calculateApplicationScore();
                if (applicationScore >= 80) return '强';
                if (applicationScore >= 60) return '中等';
                return '需提升';
            }

            calculateApplicationScore() {
                // 基于回答质量和实际应用场景计算应用能力得分
                if (this.conversation.length === 0) return 0;
                
                const totalScore = this.conversation.reduce((sum, conv) => {
                    return sum + (conv.evaluation ? conv.evaluation.score : 0);
                }, 0);
                
                return Math.round(totalScore / this.conversation.length);
            }

            getCategoryAnalysis() {
                const categoryStats = {};
                
                // 初始化所有类别
                Object.keys(this.rulesContent).forEach(category => {
                    categoryStats[category] = { total: 0, score: 0 };
                });
                
                // 计算每个类别的统计
                this.conversation.forEach(conv => {
                    if (conv.evaluation && conv.category) {
                        const stats = categoryStats[conv.category];
                        stats.total++;
                        stats.score += conv.evaluation.score;
                    }
                });
                
                // 生成分析结果
                return Object.keys(categoryStats).map(category => {
                    const stats = categoryStats[category];
                    const accuracy = stats.total > 0 ? Math.round(stats.score / stats.total) : 0;
                    
                    return {
                        name: category,
                        accuracy: accuracy,
                        total: stats.total,
                        averageScore: stats.total > 0 ? Math.round(stats.score / stats.total) : 0
                    };
                }).filter(category => category.total > 0);
            }

            getWeakAreas() {
                const categoryAnalysis = this.getCategoryAnalysis();
                return categoryAnalysis
                    .filter(category => category.accuracy < 70)
                    .map(category => category.name);
            }

            exportReport() {
                // 显示导出格式选择对话框
                this.showExportDialog();
            }

            showExportDialog() {
                const dialog = document.createElement('div');
                dialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                dialog.innerHTML = `
                    <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
                        <h3 class="text-lg font-semibold mb-4">选择导出格式</h3>
                        <div class="space-y-3">
                            <button onclick="examSystem.exportAsJSON()" class="w-full text-left p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="font-medium">JSON 格式</div>
                                <div class="text-sm text-gray-600">包含完整的考核数据和详细分析</div>
                            </button>
                            <button onclick="examSystem.exportAsCSV()" class="w-full text-left p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="font-medium">CSV 格式</div>
                                <div class="text-sm text-gray-600">适合Excel分析和数据统计</div>
                            </button>
                            <button onclick="examSystem.exportAsHTML()" class="w-full text-left p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="font-medium">HTML 报告</div>
                                <div class="text-sm text-gray-600">美观的网页格式，适合打印和分享</div>
                            </button>
                        </div>
                        <div class="mt-4 flex justify-end space-x-2">
                            <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                                取消
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(dialog);
            }

            exportAsJSON() {
                const report = {
                    examInfo: {
                        examDate: new Date().toLocaleString('zh-CN'),
                        score: this.score,
                        accuracy: Math.round((this.score / (this.totalRounds * 100)) * 100),
                        timeUsed: Math.round((Date.now() - this.startTime) / 60000),
                        totalRounds: this.totalRounds,
                        completedRounds: this.currentRound,
                        passed: Math.round((this.score / (this.totalRounds * 100)) * 100) >= 80
                    },
                    categoryAnalysis: this.getCategoryAnalysis(),
                    weakAreas: this.getWeakAreas(),
                    rulesCoverage: Array.from(this.rulesCoverage),
                    conversation: this.conversation,
                    summary: {
                        skillLevel: this.getSkillLevel(Math.round((this.score / (this.totalRounds * 100)) * 100)),
                        applicationLevel: this.getApplicationLevel(),
                        learningAdvice: this.getLearningAdvice()
                    }
                };
                
                const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
                this.downloadFile(blob, `GoMyHire考核报告_${new Date().toISOString().split('T')[0]}.json`);
                this.closeExportDialog();
            }

            exportAsCSV() {
                const csvData = [
                    ['轮次', '问题', '用户回答', '得分', '反馈', '规则类别', '难度', '时间戳'],
                    ...this.conversation.map(conv => [
                        conv.round,
                        `"${conv.question.replace(/"/g, '""')}"`,
                        `"${conv.answer.replace(/"/g, '""')}"`,
                        conv.evaluation ? conv.evaluation.score : 0,
                        `"${conv.evaluation ? conv.evaluation.feedback.replace(/"/g, '""') : ''}"`,
                        this.getCategoryName(conv.category),
                        conv.difficulty,
                        new Date(conv.timestamp).toLocaleString('zh-CN')
                    ])
                ].map(row => row.join(',')).join('\n');
                
                const blob = new Blob(['\ufeff' + csvData], { type: 'text/csv;charset=utf-8;' });
                this.downloadFile(blob, `GoMyHire考核数据_${new Date().toISOString().split('T')[0]}.csv`);
                this.closeExportDialog();
            }

            exportAsHTML() {
                const accuracy = Math.round((this.score / (this.totalRounds * 100)) * 100);
                const categoryAnalysis = this.getCategoryAnalysis();
                
                const htmlContent = `
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>GoMyHire考核报告</title>
                    <style>
                        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3b82f6; padding-bottom: 20px; }
                        .score { font-size: 48px; font-weight: bold; color: #3b82f6; margin: 10px 0; }
                        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
                        .stat-box { background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #3b82f6; }
                        .category-analysis { margin: 20px 0; }
                        .category-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; background: #f1f5f9; margin: 5px 0; border-radius: 5px; }
                        .progress-bar { width: 100px; height: 8px; background: #e2e8f0; border-radius: 4px; overflow: hidden; }
                        .progress-fill { height: 100%; background: #3b82f6; transition: width 0.3s ease; }
                        .tag { display: inline-block; padding: 4px 8px; border-radius: 12px; font-size: 12px; margin: 2px; }
                        .tag.success { background: #dcfce7; color: #166534; }
                        .tag.danger { background: #fef2f2; color: #991b1b; }
                        .conversation-log { margin-top: 30px; }
                        .log-entry { border-bottom: 1px solid #e5e7eb; padding: 15px 0; }
                        .log-entry:last-child { border-bottom: none; }
                        @media print { body { background: white; } .container { box-shadow: none; } }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>GoMyHire规则考核报告</h1>
                            <div class="score">${this.score}</div>
                            <p>总分：${this.totalRounds * 100} | 准确率：${accuracy}% | ${accuracy >= 80 ? '通过' : '未通过'}</p>
                            <p>考核时间：${new Date().toLocaleString('zh-CN')}</p>
                        </div>
                        
                        <div class="grid">
                            <div class="stat-box">
                                <h4>考核统计</h4>
                                <p>总题目数：${this.totalRounds}</p>
                                <p>完成题目：${this.currentRound}</p>
                                <p>用时：${Math.round((Date.now() - this.startTime) / 60000)}分钟</p>
                                <p>规则覆盖：${this.rulesCoverage.size}/${Object.keys(this.rulesContent).length}</p>
                            </div>
                            <div class="stat-box">
                                <h4>能力评估</h4>
                                <p>规则理解：${this.getSkillLevel(accuracy)}</p>
                                <p>应用能力：${this.getApplicationLevel()}</p>
                                <p>学习建议：${this.getLearningAdvice()}</p>
                            </div>
                        </div>
                        
                        <div class="category-analysis">
                            <h3>规则领域分析</h3>
                            ${categoryAnalysis.map(category => `
                                <div class="category-item">
                                    <span>${this.getCategoryName(category.name)}</span>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: ${category.accuracy}%"></div>
                                        </div>
                                        <span>${category.accuracy}%</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        
                        <div>
                            <h3>掌握的规则领域</h3>
                            <div>
                                ${Array.from(this.rulesCoverage).map(category => 
                                    `<span class="tag success">${this.getCategoryName(category)}</span>`
                                ).join('')}
                            </div>
                        </div>
                        
                        ${this.getWeakAreas().length > 0 ? `
                        <div>
                            <h3>需要加强的领域</h3>
                            <div>
                                ${this.getWeakAreas().map(category => 
                                    `<span class="tag danger">${this.getCategoryName(category)}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}
                        
                        <div class="conversation-log">
                            <h3>对话记录</h3>
                            ${this.conversation.map(conv => `
                                <div class="log-entry">
                                    <strong>第${conv.round}题 (${this.getCategoryName(conv.category)})</strong>
                                    <p><strong>问题：</strong>${conv.question}</p>
                                    <p><strong>回答：</strong>${conv.answer}</p>
                                    <p><strong>得分：</strong>${conv.evaluation ? conv.evaluation.score : 0}/100</p>
                                    <p><strong>反馈：</strong>${conv.evaluation ? conv.evaluation.feedback : ''}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </body>
                </html>`;
                
                const blob = new Blob([htmlContent], { type: 'text/html' });
                this.downloadFile(blob, `GoMyHire考核报告_${new Date().toISOString().split('T')[0]}.html`);
                this.closeExportDialog();
            }

            downloadFile(blob, filename) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }

            closeExportDialog() {
                const dialog = document.querySelector('.fixed.inset-0');
                if (dialog) {
                    dialog.remove();
                }
            }

            exitExam() {
                if (confirm('确定要退出考试吗？已进行的进度将不会保存。')) {
                    this.endExam();
                }
            }

            showClarificationDialog() {
                // 隐藏选择题选项
                document.getElementById('optionsContainer').classList.add('hidden');
                
                // 显示提问对话框
                document.getElementById('clarificationArea').classList.remove('hidden');
                
                // 更新提示文本
                document.getElementById('inputPrompt').textContent = '您可以提问以获得更多解释';
                
                // 隐藏主要按钮
                document.getElementById('submitAnswerBtn').classList.add('hidden');
                document.getElementById('skipQuestionBtn').classList.add('hidden');
                
                // 启用提问输入
                document.getElementById('clarificationInput').disabled = false;
                document.getElementById('clarificationInput').value = '';
                document.getElementById('clarificationInput').focus();
                document.getElementById('submitClarificationBtn').disabled = false;
                document.getElementById('skipClarificationBtn').disabled = false;
            }

            async submitClarification() {
                const clarificationInput = document.getElementById('clarificationInput');
                const clarificationText = clarificationInput.value.trim();
                
                if (!clarificationText) return;
                
                // 禁用提问输入
                clarificationInput.disabled = true;
                document.getElementById('submitClarificationBtn').disabled = true;
                document.getElementById('skipClarificationBtn').disabled = true;
                
                // 显示用户的提问
                this.displayUserClarification(clarificationText);
                
                // 显示正在回答
                this.showTypingIndicator();
                
                // 使用Gemini API回答问题
                const response = await this.generateClarificationResponse(clarificationText);
                
                // 移除输入指示器
                this.removeTypingIndicator();
                
                // 显示AI的回答
                this.displayClarificationResponse(response);
                
                // 3秒后下一题
                setTimeout(() => {
                    this.hideClarificationDialog();
                    this.nextRound();
                }, 3000);
            }

            async generateClarificationResponse(question) {
                try {
                    const prompt = `
学员在GoMyHire规则考核中回答错误，现在提出了以下问题需要详细解释：

原问题：${this.currentQuestion.scenario || this.currentQuestion.text}
相关规则：${this.currentQuestion.rule}
学员选择的错误答案：${this.currentQuestion.options ? this.currentQuestion.options[this.currentQuestion.correctAnswer] : '未知'}
正确答案：${this.currentQuestion.options && this.currentQuestion.correctAnswer !== undefined ? this.currentQuestion.options[this.currentQuestion.correctAnswer] : '未知'}

学员的提问：${question}

请针对学员的提问，提供详细、易懂的解释：
1. 直接回答学员的问题
2. 解释相关规则的具体要求
3. 提供实际应用场景和例子
4. 说明为什么正确答案是正确的
5. 给出学习和应用建议

请用中文回答，语气友好专业，字数控制在200-300字。
                    `;
                    
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${this.geminiAPI.apiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contents: [{
                                parts: [{
                                    text: prompt
                                }]
                            }],
                            generationConfig: {
                                temperature: 0.3,
                                topK: 40,
                                topP: 0.95,
                                maxOutputTokens: 8000,
                            }
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                        return data.candidates[0].content.parts[0].text.trim();
                    } else {
                        throw new Error('Invalid API response');
                    }
                } catch (error) {
                    console.error('Clarification API Error:', error);
                    // 返回备用回答
                    return `很抱歉，我暂时无法回答您的问题。建议您重新学习"${this.currentQuestion.rule}"这个规则，重点关注规则的核心要求和实际应用场景。如果您有其他疑问，可以联系培训师获得更多帮助。`;
                }
            }

            displayUserClarification(clarificationText) {
                const chatArea = document.getElementById('chatArea');
                
                const clarificationDiv = document.createElement('div');
                clarificationDiv.className = 'chat-bubble flex items-start space-x-3 justify-end';
                clarificationDiv.innerHTML = `
                    <div class="flex-1 max-w-md">
                        <div class="bg-purple-100 p-4 rounded-lg">
                            <p class="text-sm font-semibold text-purple-700 mb-1">
                                <i data-lucide="help-circle" class="w-4 h-4 inline mr-1"></i>
                                我的提问：
                            </p>
                            <p class="text-gray-800">${clarificationText}</p>
                        </div>
                    </div>
                    <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                        我
                    </div>
                `;
                
                chatArea.appendChild(clarificationDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
                
                // 重新初始化图标
                lucide.createIcons();
            }

            displayClarificationResponse(response) {
                const chatArea = document.getElementById('chatArea');
                
                const responseDiv = document.createElement('div');
                responseDiv.className = 'chat-bubble flex items-start space-x-3';
                responseDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                        AI
                    </div>
                    <div class="flex-1">
                        <div class="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                            <p class="text-sm font-semibold text-blue-700 mb-2">
                                <i data-lucide="lightbulb" class="w-4 h-4 inline mr-1"></i>
                                详细解释：
                            </p>
                            <p class="text-gray-800 leading-relaxed">${response}</p>
                        </div>
                    </div>
                `;
                
                chatArea.appendChild(responseDiv);
                chatArea.scrollTop = chatArea.scrollHeight;
                
                // 重新初始化图标
                lucide.createIcons();
            }

            skipClarification() {
                this.hideClarificationDialog();
                this.nextRound();
            }

            hideClarificationDialog() {
                // 隐藏提问对话框
                document.getElementById('clarificationArea').classList.add('hidden');
                
                // 显示选择题选项
                document.getElementById('optionsContainer').classList.remove('hidden');
                
                // 恢复主要按钮
                document.getElementById('submitAnswerBtn').classList.remove('hidden');
                document.getElementById('skipQuestionBtn').classList.remove('hidden');
                
                // 恢复提示文本
                document.getElementById('inputPrompt').textContent = '请选择最合适的答案';
                
                // 清空提问输入
                document.getElementById('clarificationInput').value = '';
            }

            retakeExam() {
                // 重置所有状态
                this.currentRound = 0;
                this.score = 0;
                this.conversation = [];
                this.rulesCoverage.clear();
                this.isExamActive = false;
                
                // 清空对话区域
                document.getElementById('chatArea').innerHTML = '';
                
                // 重置界面
                document.getElementById('resultScreen').classList.add('hidden');
                document.getElementById('startScreen').classList.remove('hidden');
            }
        }

        // 初始化考试系统
        const examSystem = new ExamSystem();
    </script>
</body>
</html>