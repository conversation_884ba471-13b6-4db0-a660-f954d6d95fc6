/*
 * 文件路径: src/styles/components/glassmorphism.css
 * 文件描述: 定义了一组用于对各种UI组件应用玻璃拟态（glassmorphism）效果的CSS类。它利用CSS自定义属性（设计令牌）来保持背景、边框、模糊和阴影值的一致性，从而实现不同强度的玻璃拟态效果。此外，它还包含了卡片、按钮、导航、模态框、搜索输入框和头部等常见组件的特定样式。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于在以下文件中定义的CSS变量（设计令牌）：
 *     - `src/styles/themes/variables.css` (用于 `--glass-background-*`, `--glass-border-*`, `--glass-blur-*`, `--gradient-header`)
 *     - `src/styles/tokens/radius.css` (用于 `--radius-card`, `--radius-button`, `--radius-2xl`, `--radius-modal`, `--radius-full`)
 *     - `src/styles/tokens/shadows.css` (用于 `--shadow-card`, `--shadow-lg`, `--shadow-2xl`, `--shadow-floating`, `--shadow-primary`)
 *     - `src/styles/tokens/transitions.css` (用于 `--transition-card`, `--transition-button`, `--transition-all`)
 *     - `src/styles/tokens/colors.css` (用于 `--text-primary`, `--primary-500`)
 *     - `src/styles/tokens/typography.css` (用于 `--font-weight-medium`)
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 在应用程序中提供一致且可重用的玻璃拟态UI趋势实现。
 *   - 为不同用例定义不同强度的玻璃拟态效果（基础、强烈、微妙）。
 *   - 将玻璃拟态效果应用于特定组件，并为其提供适当的样式。
 *   - 确保对不支持 `backdrop-filter` 的浏览器进行优雅降级。
 *   - 通过降低模糊强度来优化移动设备上的性能。
 * 关键部分/规则:
 *   - `.glass-surface`: 玻璃拟态的基础类，使用 `var(--glass-*)` 令牌应用背景、`backdrop-filter`（模糊）和边框。
 *   - `.glass-surface--intense`, `.glass-surface--subtle`: 用于更强和更微妙玻璃拟态效果的变体。
 *   - `.glass-card`: 扩展 `.glass-surface` 并为卡片添加特定样式，包括边框圆角、盒阴影和悬停效果。
 *   - `.glass-button`: 扩展 `.glass-surface` 并为按钮添加样式，包括内边距、文本颜色、字重以及悬停/激活效果。
 *   - `.glass-nav`, `.glass-modal`, `.glass-search`, `.glass-header`: 针对导航、模态框、搜索输入和头部的特定玻璃拟态样式，利用相关的设计令牌。
 *   - `.glass-border`: 一个仅应用玻璃拟态边框效果，不带背景或模糊的类。
 *   - `.glass-glow`: 一个用于添加微妙线性渐变发光效果的类。
 *   - `@media (max-width: 767px)`: 针对移动设备降低模糊强度，以提高潜在性能。
 *   - `@supports not (backdrop-filter: blur(1px))`: 为不支持 `backdrop-filter` 的浏览器提供回退方案，使用纯色背景和边框颜色。
 * 使用约定:
 *   - 这些类直接应用于HTML元素以实现所需的玻璃拟态效果（例如，`<div class="glass-card">`）。
 *   - 注意：文件中使用了 `@extend` 语法，这通常是CSS预处理器（如Sass或Less）的特性。在此文档中，我们将其视为概念上的扩展，实际项目中可能需要预处理器编译或手动管理这些样式。
 */
/* 组件样式 - 玻璃拟态效果 */

/* 基础玻璃拟态效果类 */
.glass-surface {
  background: var(--glass-background);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
}

/* 强烈玻璃拟态效果 */
.glass-surface--intense {
  background: var(--glass-background-intense);
  backdrop-filter: blur(var(--glass-blur-intense));
  -webkit-backdrop-filter: blur(var(--glass-blur-intense));
  border: 1px solid var(--glass-border-intense);
}

/* 微妙玻璃拟态效果 */
.glass-surface--subtle {
  background: var(--glass-background-subtle);
  backdrop-filter: blur(var(--glass-blur-subtle));
  -webkit-backdrop-filter: blur(var(--glass-blur-subtle));
  border: 1px solid var(--glass-border-subtle);
}

/* 玻璃卡片组合效果 */
.glass-card {
  @extend .glass-surface;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-card);
  transition: var(--transition-card);
}

.glass-card:hover {
  background: var(--glass-background-intense);
  border-color: var(--glass-border-intense);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 玻璃按钮效果 */
.glass-button {
  @extend .glass-surface;
  border-radius: var(--radius-button);
  padding: var(--space-sm) var(--space-lg);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-button);
  cursor: pointer;
}

.glass-button:hover {
  background: var(--glass-background-intense);
  border-color: var(--glass-border-intense);
  transform: translateY(-1px);
}

.glass-button:active {
  transform: translateY(0);
}

/* 玻璃导航效果 */
.glass-nav {
  @extend .glass-surface;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-floating);
}

/* 玻璃模态框效果 */
.glass-modal {
  @extend .glass-surface--intense;
  border-radius: var(--radius-modal);
  box-shadow: var(--shadow-2xl);
}

/* 玻璃搜索框效果 */
.glass-search {
  @extend .glass-surface;
  border-radius: var(--radius-full);
  transition: var(--transition-all);
}

.glass-search:focus-within {
  background: var(--glass-background-intense);
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
}

/* 玻璃头部效果 */
.glass-header {
  @extend .glass-surface;
  background: var(--gradient-header);
  box-shadow: var(--shadow-primary);
}

/* 玻璃边框效果（仅边框，无背景） */
.glass-border {
  background: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
}

/* 浮动光线效果 */
.glass-glow {
  position: relative;
}

.glass-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    var(--glass-border-intense), 
    transparent
  );
  border-radius: var(--radius-full);
}

/* 移动端优化 */
@media (max-width: 767px) {
  /* 在低性能设备上减少模糊强度 */
  .glass-surface {
    backdrop-filter: blur(calc(var(--glass-blur) * 0.7));
    -webkit-backdrop-filter: blur(calc(var(--glass-blur) * 0.7));
  }
  
  .glass-surface--intense {
    backdrop-filter: blur(calc(var(--glass-blur-intense) * 0.7));
    -webkit-backdrop-filter: blur(calc(var(--glass-blur-intense) * 0.7));
  }
}

/* 降级支持 - 不支持backdrop-filter的浏览器 */
@supports not (backdrop-filter: blur(1px)) {
  .glass-surface,
  .glass-surface--intense,
  .glass-surface--subtle {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
  }
}