/*
 * 文件路径: src/styles/index.css
 * 文件描述: GoMyHire FAQ 系统的主CSS入口文件。
 * 依赖关系:
 *   - 由 index.html 通过 <link> 标签引入。
 *   - 内部通过 @import 引入了以下CSS模块，按顺序加载和应用：
 *     - 设计令牌系统:
 *       - ./tokens/colors.css
 *       - ./tokens/spacing.css
 *       - ./tokens/typography.css
 *       - ./tokens/radius.css
 *       - ./tokens/shadows.css
 *       - ./tokens/transitions.css
 *     - 主题系统:
 *       - ./themes/variables.css
 *       - ./themes/light.css
 *       - ./themes/dark.css
 *     - 基础样式:
 *       - ./base/reset.css
 *       - ./base/globals.css
 *       - ./base/utilities.css
 *     - 布局组件:
 *       - ./layout/header.css
 *     - UI组件:
 *       - ./components/glassmorphism.css
 *       - ./components/buttons.css
 *       - ./components/cards.css
 *       - ./components/search.css
 *       - ./components/navigation.css
 * 初始化时机: 浏览器解析 index.html 时加载并应用。
 * 功能: 定义了全局样式、主题变量、基础重置、通用工具类、布局结构以及核心UI组件的样式。
 *       负责整个应用的外观和响应式布局。
 */

/* ===== 设计令牌系统 ===== */
@import './tokens/colors.css';
@import './tokens/spacing.css';
@import './tokens/typography.css';
@import './tokens/radius.css';
@import './tokens/shadows.css';
@import './tokens/transitions.css';

/* ===== 主题系统 ===== */
@import './themes/variables.css';
@import './themes/light.css';
@import './themes/dark.css';

/* ===== 基础样式 ===== */
@import './base/reset.css';
@import './base/globals.css';
@import './base/utilities.css';

/* ===== 布局组件 ===== */
@import './layout/header.css';

/* ===== UI组件 ===== */
@import './components/glassmorphism.css';
@import './components/buttons.css';
@import './components/cards.css';
@import './components/search.css';
@import './components/navigation.css';
@import './components/floating-chat.css';
@import './components/enhanced-content.css';

/* ===== 页面特定样式 ===== */
/* 这些将在下一步创建 */
/* @import './pages/welcome.css'; */
/* @import './pages/categories.css'; */
/* @import './pages/faq-detail.css'; */
@import './pages/search-results.css';

/* ===== 补充样式 - 临时兼容性样式 ===== */

/* 主内容区域 */
.main-content {
  min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
  padding: var(--container-padding);
  padding-top: var(--space-lg);
  position: relative;
  z-index: 1;
}

/* 加载提示样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-modal);
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.purple-loader {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(168, 85, 247, 0.2);
  border-top: 3px solid var(--primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.purple-text-gradient {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-weight-medium);
  margin-top: var(--space-md);
}

/* FAQ内容区域 */
.faq-content {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-2xl);
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  position: relative;
  overflow-wrap: anywhere; /* 允许极长单词/URL换行，防止横向溢出 */
  word-break: break-word; /* 兼容性处理 */
}

.faq-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

/* 欢迎页面基础样式 */
.welcome-page {
  padding: var(--space-lg) 0;
}

.faq-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-lg);
  padding: var(--space-lg) 0;
}

/* 分类页面基础样式 */
.categories-page {
  padding: var(--space-lg) 0;
}

.page-title {
  text-align: center;
  margin-bottom: var(--space-2xl);
}

.page-title h2 {
  font-size: var(--font-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 
 * 分类页面网格布局 - 强制三列显示
 * 
 * ⚠️ 重要说明：
 * - HTML中实际使用的是 .category-grid 类（由JavaScript动态生成）
 * - #categories-container 是HTML中定义的容器ID
 * - .categories-container 是预留的类名（目前未使用）
 * 
 * 🎯 设计要求：在所有设备上都显示为三列布局
 * - 桌面端：三列等宽，最大宽度1000px
 * - 平板端：三列等宽，间距适中
 * - 手机端：三列等宽，间距较小
 * 
 * 🛠️ 使用 !important 的原因：
 * - 防止 card-grid 等其他CSS规则意外覆盖
 * - 确保在所有响应式断点下都保持三列布局
 */
#categories-container,
.categories-container,
.category-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: var(--space-lg);
  padding: var(--space-lg) var(--space-lg);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 搜索结果页面基础样式 */
.search-page {
  padding: var(--space-lg) 0;
}

.search-results {
  max-width: 800px;
  margin: 0 auto;
}

/* 无结果页面 */
.no-results {
  text-align: center;
  padding: var(--space-4xl) var(--space-lg);
  color: var(--text-secondary);
}

.no-results .icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
  opacity: 0.5;
}

.no-results h3 {
  font-size: var(--font-2xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-md);
  color: var(--text-primary);
}

.no-results p {
  font-size: var(--font-base);
  line-height: var(--leading-relaxed);
  max-width: 400px;
  margin: 0 auto;
}

/* 相关问题区域 */
.related-questions {
  margin-top: var(--space-3xl);
  padding: var(--space-2xl);
  background: var(--glass-background-subtle);
  border: 1px solid var(--glass-border-subtle);
  border-radius: var(--radius-2xl);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1; /* 降低局部层级，避免覆盖下方交互元素 */
}

.related-questions h3 {
  font-size: var(--font-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-700);
  margin-bottom: var(--space-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

/* 动画关键帧 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式断点 */
@media (max-width: 768px) {
  .main-content {
    padding: var(--space-md);
  }
  
  .faq-cards {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  /* 平板端分类网格 - 保持三列布局，调整间距 */
  #categories-container,
  .categories-container,
  .category-grid {
    grid-template-columns: repeat(3, 1fr) !important; /* 强制三列 */
    gap: var(--space-sm); /* 较小间距适应平板 */
    padding: var(--space-md) var(--space-sm);
    max-width: 100%;
  }
  
  .faq-content {
    padding: var(--space-lg);
    margin: var(--space-md);
  max-width: 100%;
  }
  
  .related-questions {
    padding: var(--space-lg);
    margin-top: var(--space-2xl);
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--space-sm);
  }
  
  .page-title h2 {
    font-size: var(--font-2xl);
  }
  
  /* 手机端分类网格 - 保持三列布局，自适应间距 */
  #categories-container,
  .categories-container,
  .category-grid {
    grid-template-columns: repeat(3, 1fr) !important; /* 强制三列 */
    gap: var(--space-xs); /* 最小间距 */
    padding: var(--space-sm) var(--space-xs);
    max-width: 100%;
  }
  
  .faq-content {
    padding: var(--space-md);
    margin: var(--space-sm);
  max-width: 100%;
  }
}

/* ===== Desktop layout adjustments (PC) ===== */
@media (min-width: 1024px) {
  #categories-container,
  .categories-container,
  .category-grid {
    grid-template-columns: repeat(3, minmax(300px, 1fr)) !important;
    gap: var(--space-xl);
    padding: var(--space-2xl) var(--space-xl);
    max-width: 1200px;
  }
  
  /* Only when main follows our fixed header, add top padding to avoid overlap */
  body > header.header + main.main-content {
    padding-top: calc(var(--header-height) + var(--space-lg));
  }

  /* Scope vertical rhythm to pages that are direct children of main */
  main.main-content > .welcome-page,
  main.main-content > .categories-page,
  main.main-content > .search-page {
    padding-top: var(--space-2xl);
    padding-bottom: var(--space-2xl);
  }
}

/* 无障碍和可用性改进 */
@media (prefers-reduced-motion: reduce) {
  .purple-loader {
    animation: none;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {
  .faq-content,
  .related-questions {
    border-width: 2px;
  }
}

/* 打印样式 */
@media print {
  .header,
  .bottom-nav,
  .loading-overlay {
    display: none !important;
  }
  
  .main-content {
    padding-top: 0;
    padding-bottom: 0;
  }
  
  .faq-content {
    background: none;
    border: 1px solid #000;
    box-shadow: none;
  }
}

/* ===== FAQ兼容层样式 ===== */
/* @SHARED_STYLE 兼容层：补齐FAQ详情页关键选择器，对齐旧版视觉 */
/* @REFERENCE 参考 backup/styles.css 的等价语义，使用设计令牌表达 */
/* @LIFECYCLE 临时兼容，后续计划迁移到组件/页面模块 */

/* FAQ头部区域 - 分隔线与间距 */
.faq-header {
  margin-bottom: var(--space-lg); /* @REFERENCE 底部间距 */
  padding-bottom: var(--space-sm); /* @REFERENCE 内边距 */
  border-bottom: 1px solid var(--border-color); /* @REFERENCE 分隔线 */
}

/* FAQ编号徽标 */
.faq-id {
  display: inline-block; /* @REFERENCE 行内块元素 */
  background: var(--primary-500); /* @REFERENCE 主色背景 */
  color: white; /* @REFERENCE 白色文字 */
  padding: var(--space-xs) var(--space-md); /* @REFERENCE 内边距 */
  border-radius: var(--radius-full); /* @REFERENCE 圆角 */
  font-size: var(--font-sm); /* @REFERENCE 字体大小 */
  font-weight: var(--font-weight-bold); /* @REFERENCE 字重 */
  margin-bottom: var(--space-sm); /* @REFERENCE 底部间距 */
}

/* FAQ标题排版 */
.faq-title {
  font-size: var(--font-2xl); /* @REFERENCE 大标题字号 */
  font-weight: var(--font-weight-bold); /* @REFERENCE 粗体 */
  color: var(--text-primary); /* @REFERENCE 主文本色 */
  line-height: var(--leading-tight); /* @REFERENCE 紧凑行高 */
}

/* FAQ正文基础排版 */
.faq-body {
  font-size: var(--font-base); /* @REFERENCE 基础字号 */
  line-height: var(--leading-relaxed); /* @REFERENCE 宽松行高 */
  color: var(--text-primary); /* @REFERENCE 主文本色 */
  overflow-wrap: anywhere;
}

.faq-body h3 {
  font-size: var(--font-xl); /* @REFERENCE 子标题字号 */
  font-weight: var(--font-weight-bold); /* @REFERENCE 粗体 */
  color: var(--text-primary); /* @REFERENCE 主文本色 */
  margin: var(--space-xl) 0 var(--space-md) 0; /* @REFERENCE 上下间距 */
}

.faq-body h4 {
  font-size: var(--font-lg); /* @REFERENCE 小标题字号 */
  font-weight: var(--font-weight-semibold); /* @REFERENCE 半粗体 */
  color: var(--text-primary); /* @REFERENCE 主文本色 */
  margin: var(--space-lg) 0 var(--space-sm) 0; /* @REFERENCE 上下间距 */
}

/* 列表样式已迁移到 enhanced-content.css 统一管理 */
/* 这里保留表格和其他内容样式 */

.faq-body table {
  width: 100%; /* @REFERENCE 表格宽度 */
  border-collapse: collapse; /* @REFERENCE 边框合并 */
  margin: var(--space-lg) 0; /* @REFERENCE 表格间距 */
  border: 1px solid var(--border-color); /* @REFERENCE 表格边框 */
  border-radius: var(--radius-md); /* @REFERENCE 表格圆角 */
  overflow: hidden; /* @REFERENCE 圆角裁剪 */
}

/* 代码块与预格式文本防止溢出 */
.faq-body pre,
.faq-body code {
  max-width: 100%;
  white-space: pre-wrap;
  word-break: break-word;
}

.faq-body th,
.faq-body td {
  padding: var(--space-md); /* @REFERENCE 单元格内边距 */
  text-align: left; /* @REFERENCE 左对齐 */
  border-bottom: 1px solid var(--border-color); /* @REFERENCE 单元格边框 */
}

.faq-body th {
  background: var(--background-secondary); /* @REFERENCE 表头背景 */
  font-weight: var(--font-weight-semibold); /* @REFERENCE 表头字重 */
}

/* 模块化重构完成标识 */
body::after {
  content: "CSS Modularized v2.0.0";
  position: fixed;
  bottom: 0;
  right: 0;
  font-size: var(--font-xs);
  color: var(--text-tertiary);
  background: var(--surface-color);
  padding: var(--space-xs);
  opacity: 0;
  pointer-events: none;
  z-index: -1;
}