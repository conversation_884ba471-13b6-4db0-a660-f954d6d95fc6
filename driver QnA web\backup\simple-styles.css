/**
 * GoMyHire Driver FAQ - 简化样式
 * 目标：简单、清晰、响应式
 * 移除过度复杂的动画和效果
 */

/* ========== 全局样式 ========== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f5f5f5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* ========== 头部样式 ========== */
.header {
    background: #6f42c1;
    color: white;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.language-switcher {
    display: flex;
    gap: 0.5rem;
}

.lang-btn {
    padding: 0.25rem 0.75rem;
    border: 1px solid rgba(255,255,255,0.3);
    background: transparent;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.lang-btn:hover,
.lang-btn.active {
    background: rgba(255,255,255,0.2);
}

/* ========== 搜索样式 ========== */
.search-section {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #6f42c1;
}

/* ========== 搜索结果样式 ========== */
.search-summary {
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.result-item {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid #6f42c1;
}

.result-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    gap: 1rem;
}

.result-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    flex: 1;
}

.result-score {
    background: #6f42c1;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
}

.result-content {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.result-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #999;
}

.result-category {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
}

/* ========== 搜索高亮 ========== */
mark {
    background: #fff3cd;
    color: #856404;
    padding: 0.1rem 0.2rem;
    border-radius: 2px;
    font-weight: 500;
}

/* ========== 状态样式 ========== */
.search-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #666;
    gap: 0.5rem;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #6f42c1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.search-error {
    text-align: center;
    padding: 2rem;
    color: #d32f2f;
    background: #ffeaea;
    border-radius: 8px;
}

.no-results {
    text-align: center;
    padding: 3rem 2rem;
    color: #666;
}

.no-results h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

/* ========== 模态框样式 ========== */
#questionModal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    width: 100%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.modal-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    color: #666;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    line-height: 1.6;
    color: #333;
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .search-section {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .result-item {
        padding: 1rem;
    }
    
    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .result-score {
        align-self: flex-end;
    }
    
    .modal-content {
        margin: 10px;
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .search-input {
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .result-title {
        font-size: 1rem;
    }
    
    .result-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* ========== 工具类 ========== */
.text-center { text-align: center; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }

/* ========== 打印样式 ========== */
@media print {
    .header, .search-section {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
    
    .result-item {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd;
    }
}