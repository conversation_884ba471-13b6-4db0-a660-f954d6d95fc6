# 部署指南

**报告日期:** 2025年9月1日

## 1. Netlify部署

### 1.1. 快速部署

*   **拖放部署 (推荐)**:
    1.  访问 [Netlify](https://netlify.com)。
    2.  将项目文件夹拖放到Netlify主页。
    3.  等待自动部署完成。
*   **Netlify CLI部署**:
    ```bash
    npm install -g netlify-cli
    netlify deploy --prod --dir .
    ```

### 1.2. 环境变量配置

*   **方式1: HTML Meta标签 (已配置)**:
    ```html
    <meta name="env:GEMINI_API_KEY" content="AIzaSy...">
    <meta name="env:GEMINI_MODEL" content="gemini-2.5-flash">
    ```
*   **方式2: Netlify环境变量 (推荐)**:
    *   在Netlify项目设置中添加 `GEMINI_API_KEY` 和 `GEMINI_MODEL`。

---
*本部分整合了 `docs/NETLIFY_DEPLOY.md` 的核心内容。*

## 2. 部署前检查清单

### 2.1. 数据完整性

*   [ ] **FAQ数量**: 64个条目。
*   [ ] **分类系统**: 使用6个标准分类。
*   [ ] **多语言支持**: 中、英、马三语内容完整。
*   [ ] **搜索标签**: 94%覆盖率。

### 2.2. 技术质量

*   [ ] **JSON语法**: 无错误。
*   [ ] **编码格式**: UTF-8。
*   [ ] **代码规范**: 遵循JavaScript规范。

### 2.3. 功能完整性

*   [ ] **分类浏览**: 正常。
*   [ ] **搜索功能**: 正常。
*   [ ] **多语言切换**: 正常。
*   [ ] **响应式设计**: 正常。

### 2.4. 内容准确性

*   [ ] **关键信息**: 管理员电话、等待时间、补贴金额等已验证。

---
*本部分整合了 `docs/guides/DEPLOYMENT_CHECKLIST.md` 的核心内容。*
