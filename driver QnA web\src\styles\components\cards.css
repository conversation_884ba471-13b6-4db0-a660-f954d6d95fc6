/*
 * 文件路径: src/styles/components/cards.css
 * 文件描述: 定义了应用程序中各种卡片组件的全面样式集。它建立了一个基础卡片样式，并提供了视觉外观（扁平、浮起、玻璃拟态）和特定用例（FAQ卡片、分类卡片、搜索结果卡片、相关问题卡片）的多种变体。此外，它还包含了响应式调整、悬停效果和动画。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于在以下文件中定义的CSS变量（设计令牌）：
 *     - `src/styles/tokens/spacing.css` (用于内边距和间距)。
 *     - `src/styles/tokens/radius.css` (用于 `border-radius`)。
 *     - `src/styles/tokens/shadows.css` (用于 `box-shadow`)。
 *     - `src/styles/tokens/transitions.css` (用于 `transition` 属性)。
 *     - `src/styles/tokens/colors.css` (用于文本和背景颜色，包括品牌色和状态色)。
 *     - `src/styles/tokens/typography.css` (用于字体大小和字重)。
 *     - `src/styles/themes/variables.css` (用于卡片背景、边框、悬停背景以及玻璃拟态效果的颜色和模糊值)。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 提供一套一致且视觉吸引力的卡片组件，用于内容展示。
 *   - 提供各种卡片样式以适应不同的UI上下文并强调内容层次结构。
 *   - 实现交互式悬停效果和动画，以增强用户体验。
 *   - 确保在不同屏幕尺寸上的响应性。
 *   - 支持可访问性功能，如减少动画和高对比度模式。
 * 关键部分/规则:
 *   - `.card`: 基础卡片样式，定义了背景、边框、圆角、内边距、盒阴影和过渡等通用属性。包含一个用于微妙提升和背景变化的悬停效果。
 *   - **卡片变体**:
 *     - `.card--flat`, `.card--elevated`: 用于不同阴影/边框强度的变体。
 *     - `.card--glass`: 应用玻璃拟态效果，使用 `var(--glass-*)` 令牌。
 *   - **特定卡片类型**:
 *     - `.faq-card`: 注入 `.card--glass` 核心属性，具有特定的内边距、圆角和独特的线性渐变背景。包含一个在悬停时顶部边框发光的微妙效果。
 *     - `.category-card`: 注入 `.card--glass` 核心属性，文本居中，具有特定的内边距和独特的线性渐变背景。特点是 `::before` 伪元素用于悬停动画。
 *     - `.search-result-card`: 注入 `.card` 核心属性，左侧边框在悬停时改变颜色，表示选中或焦点。
 *     - `.related-item`: 注入 `.card--glass` 核心属性，用于相关问题，采用flex布局，并带有一个在悬停时动画的箭头图标。
 *   - **卡片内部元素**: `.category-icon`, `.category-name`, `.category-count`, `.related-title`, `.related-arrow` 的样式。
 *   - **网格布局**: `.card-grid`, `.card-grid--2`, `.card-grid--3` 定义了用于显示卡片的响应式网格布局。
 *   - **动画**: `.card--animate-in` 使用 `slideInUp` 关键帧动画，并带有交错延迟，用于入场效果。
 *   - **响应式调整 (`@media`)**: 调整卡片内边距、字体大小和网格布局以适应小屏幕。值得注意的是，`card-grid:not(.category-grid)` 确保只有非分类网格在移动设备上切换到单列，而分类网格保持其3列布局。
 *   - **可访问性 (`@media (prefers-reduced-motion: reduce)`, `@media (prefers-contrast: high)`)**: 禁用变换动画并调整边框宽度以适应可访问性偏好。
 * 使用约定:
 *   - 卡片通常使用 `.card` 类以及特定的类型或用例类创建（例如，`<div class="card faq-card">`）。
 *   - 文件已移除 `@extend` 语法，改为纯CSS实现，确保浏览器直接解析兼容性。所有卡片类型都显式注入了基础样式属性，无需预处理器编译。
 */
/* 组件样式 - 卡片组件 */

/* 基础卡片样式 */
.card {
  background: var(--card-background);
  border: 1px solid var(--card-border);
  border-radius: var(--radius-card);
  padding: var(--space-lg);
  box-shadow: var(--shadow-card);
  transition: var(--transition-card);
  position: relative;
  overflow: hidden;
}

.card:hover {
  background: var(--card-hover-background);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* 卡片变体 */
.card--flat {
  box-shadow: none;
  border: 1px solid var(--border-light);
}

.card--elevated {
  box-shadow: var(--shadow-xl);
}

.card--glass {
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

.card--glass:hover {
  background: var(--glass-background-intense);
  border-color: var(--glass-border-intense);
}

/* FAQ卡片特定样式 */
.faq-card {
  /* @SHARED_STYLE 注入 .card--glass 核心属性，替代 @extend */
  background: var(--glass-background); /* @REFERENCE 来自 .card--glass */
  border: 1px solid var(--glass-border); /* @REFERENCE 来自 .card--glass */
  -webkit-backdrop-filter: blur(20px); /* @REFERENCE 来自 .card--glass */
  backdrop-filter: blur(20px); /* @REFERENCE 来自 .card--glass */
  /* FAQ卡片特有样式覆盖 */
  margin-bottom: var(--space-lg);
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.faq-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  border-radius: var(--radius-full);
}

.faq-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(168, 85, 247, 0.15);
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.15), 
    rgba(255, 255, 255, 0.08)
  );
  border-color: rgba(255, 255, 255, 0.25);
}

/* 分类卡片样式 */
.category-card {
  /* @SHARED_STYLE 注入 .card--glass 核心属性，替代 @extend */
  background: var(--glass-background); /* @REFERENCE 来自 .card--glass */
  border: 1px solid var(--glass-border); /* @REFERENCE 来自 .card--glass */
  -webkit-backdrop-filter: blur(20px); /* @REFERENCE 来自 .card--glass */
  backdrop-filter: blur(20px); /* @REFERENCE 来自 .card--glass */
  /* 分类卡片特有样式覆盖 */
  text-align: center;
  padding: var(--space-2xl) var(--space-lg);
  border-radius: var(--radius-2xl);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.category-card:hover::before {
  transform: translateX(100%);
}

.category-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 25px 80px rgba(168, 85, 247, 0.2);
  background: linear-gradient(135deg, 
    rgba(168, 85, 247, 0.15), 
    rgba(184, 61, 186, 0.1)
  );
  border-color: rgba(168, 85, 247, 0.3);
}

.category-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-sm);
  transition: var(--transition-transform);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

@media (max-width: 480px) {
  .category-card {
    padding: var(--space-md);
    min-height: 120px;
  }
  
  .category-icon {
    font-size: 1.8rem;
    margin-bottom: var(--space-xs);
  }
}

@media (max-width: 320px) {
  .category-card {
    padding: var(--space-sm);
    min-height: 100px;
  }
  
  .category-icon {
    font-size: 1.5rem;
    margin-bottom: var(--space-xs);
  }
}

.category-card:hover .category-icon {
  transform: scale(1.1) translateY(-2px);
  filter: drop-shadow(0 8px 16px rgba(168, 85, 247, 0.3));
}

.category-name {
  font-size: var(--font-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  word-break: break-word;
  line-height: 1.2;
}

@media (max-width: 480px) {
  .category-name {
    font-size: var(--font-base);
    font-weight: var(--font-weight-medium);
  }
}

@media (max-width: 320px) {
  .category-name {
    font-size: var(--font-sm);
  }
}

.category-count {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 480px) {
  .category-count {
    font-size: var(--font-xs);
  }
}

/* 搜索结果卡片 */
.search-result-card {
  /* @SHARED_STYLE 注入 .card 核心属性，替代 @extend */
  background: var(--card-background); /* @REFERENCE 来自 .card */
  border: 1px solid var(--card-border); /* @REFERENCE 来自 .card */
  border-radius: var(--radius-card); /* @REFERENCE 来自 .card */
  padding: var(--space-lg); /* @REFERENCE 来自 .card */
  box-shadow: var(--shadow-card); /* @REFERENCE 来自 .card */
  transition: var(--transition-card); /* @REFERENCE 来自 .card */
  position: relative; /* @REFERENCE 来自 .card */
  overflow: hidden; /* @REFERENCE 来自 .card */
  /* 搜索结果卡片特有样式覆盖 */
  margin-bottom: var(--space-lg);
  cursor: pointer;
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
}

.search-result-card:hover {
  border-left-color: var(--primary-500);
  background: var(--background-secondary);
  transform: translateX(4px);
}

/* 相关问题卡片 */
.related-item {
  /* @SHARED_STYLE 注入 .card--glass 核心属性，替代 @extend */
  background: var(--glass-background); /* @REFERENCE 来自 .card--glass */
  border: 1px solid var(--glass-border); /* @REFERENCE 来自 .card--glass */
  -webkit-backdrop-filter: blur(20px); /* @REFERENCE 来自 .card--glass */
  backdrop-filter: blur(20px); /* @REFERENCE 来自 .card--glass */
  /* 相关问题卡片特有样式 */
  padding: var(--space-lg);
  margin-bottom: var(--space-sm);
  cursor: pointer;
  text-decoration: none;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  border-radius: var(--radius-lg); /* @SHARED_STYLE 确保圆角显示，修复视觉问题 */
}

.related-item:hover {
  background: rgba(168, 85, 247, 0.15);
  border-color: rgba(168, 85, 247, 0.3);
  transform: translateY(-2px);
  color: var(--text-primary);
  text-decoration: none;
}

.related-title {
  flex: 1 1 auto;
  min-width: 0; /* 允许在flex容器中正确截断 */
  font-weight: var(--font-weight-medium);
  line-height: var(--leading-normal);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 标题过长时省略显示，避免把箭头挤出容器 */
}

.related-arrow {
  color: var(--primary-600);
  font-size: var(--font-lg);
  font-weight: var(--font-weight-semibold);
  transition: var(--transition-transform);
}

.related-item:hover .related-arrow {
  transform: translateX(4px);
  color: var(--primary-700);
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  gap: var(--space-lg);
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* 统一处理卡片中的媒体元素，避免超出容器 */
.card img,
.card video,
.card iframe {
  max-width: 100%;
  height: auto;
  border-radius: inherit;
  display: block;
}

.card-grid--2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.card-grid--3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* 卡片动画类 */
.card--animate-in {
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.6s ease forwards;
}

.card--animate-in:nth-child(2) { animation-delay: 0.1s; }
.card--animate-in:nth-child(3) { animation-delay: 0.2s; }
.card--animate-in:nth-child(4) { animation-delay: 0.3s; }
.card--animate-in:nth-child(5) { animation-delay: 0.4s; }
.card--animate-in:nth-child(6) { animation-delay: 0.5s; }

/* ===== 问题列表样式 ===== */
/* 从backup/styles.css迁移，用于问题列表显示 */

.question-item {
  padding: var(--space-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: var(--transition-card);
  cursor: pointer;
  background: var(--card-background);
  margin-bottom: var(--space-sm);
}

.question-item:hover {
  border-color: var(--primary-500);
  box-shadow: var(--shadow-md);
  background: var(--card-hover-background);
}

.question-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.question-id {
  background: var(--primary-500);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-sm);
  font-weight: var(--font-weight-bold);
  margin-right: var(--space-sm);
}

.question-title {
  font-size: var(--font-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--leading-snug);
}

.question-preview {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  margin-top: var(--space-xs);
}

/* 响应式适配 */
@media (max-width: 768px) {
  /* 
   * ⚠️ 重要：只对非分类网格应用单列布局
   * 
   * 说明：
   * - :not(.category-grid) 确保分类页面不受此规则影响
   * - 分类页面需要在所有设备上保持三列布局
   * - 其他卡片网格在小屏幕上改为单列显示
   */
  .card-grid:not(.category-grid) {
    grid-template-columns: 1fr; /* 仅非分类网格变为单列 */
    gap: var(--space-md);
  }
  
  .category-card {
    padding: var(--space-lg) var(--space-md);
    min-height: 120px;
  }
  
  .category-icon {
    font-size: 2rem;
  }
  
  .category-name {
    font-size: var(--font-base);
  }
  
  .faq-card {
    padding: var(--space-lg);
  }
}

@media (max-width: 480px) {
  .card {
    padding: var(--space-md);
    border-radius: var(--radius-lg);
  }
  
  .category-card {
    padding: var(--space-md);
    min-height: 100px;
  }
  
  .category-icon {
    font-size: 1.8rem;
  }
  
  .category-name {
    font-size: var(--font-sm);
  }
}

@media (max-width: 320px) {
  .category-card {
    padding: var(--space-md);
    min-height: 100px;
  }
  
  .category-icon {
    font-size: 2rem;
  }
  
  .category-name {
    font-size: var(--font-sm);
  }
}

/* 动画关键帧 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .card,
  .faq-card,
  .category-card,
  .search-result-card,
  .related-item {
    transition: var(--transition-colors);
  }
  
  .card:hover,
  .faq-card:hover,
  .category-card:hover,
  .search-result-card:hover {
    transform: none;
  }
  
  .card--animate-in {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .category-card::before {
    display: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  .search-result-card:hover {
    border-left-width: 6px;
  }
  
  .category-card,
  .faq-card {
    border-width: 2px;
  }
}