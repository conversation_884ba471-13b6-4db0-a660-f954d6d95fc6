/*
 * 文件路径: src/core/netlify-env-loader.js
 * 文件描述: 专门为Netlify部署设计的环境变量加载器，使用meta标签和localStorage
 */
(function() {
    'use strict';
    
    class NetlifyEnvironmentLoader {
        constructor() {
            this.variables = new Map();
            this.isLoaded = false;
        }
        
        async loadEnvironmentVariables() {
            if (this.isLoaded) return;
            
            try {
                // 方法1: 从 meta 标签获取（Netlify推荐方式）
                this.loadFromMetaTags();
                
                // 方法2: 从 localStorage 获取（开发环境）
                this.loadFromLocalStorage();
                
                // 方法3: 从 Netlify 环境变量注入的脚本获取
                this.loadFromNetlifyInject();
                
                this.isLoaded = true;
                
                // 应用到配置对象
                this.applyToConfig();
                
                // 触发环境变量加载完成事件
                if (typeof window !== 'undefined') {
                    window.dispatchEvent(new Event('environmentLoaded'));
                    if (window.initializeApiKeyWhenReady) {
                        window.initializeApiKeyWhenReady();
                    }
                }
                
            } catch (error) {
                console.warn('⚠️ Netlify环境变量加载失败:', error.message);
            }
        }
        
        // 从 meta 标签获取环境变量
        loadFromMetaTags() {
            console.log('🔍 Netlify加载器开始扫描meta标签...');
            console.log('📄 当前文档状态:', document.readyState);
            console.log('📄 文档头信息:', document.head ? document.head.innerHTML.substring(0, 200) + '...' : '无head');
            
            // 确保head已加载
            if (!document.head) {
                console.warn('⚠️ 文档head未找到，等待DOM完成...');
                return;
            }
            
            // 更全面地搜索meta标签
            const selectors = [
                'meta[name^="env:"]',
                'meta[name="env:GEMINI_API_KEY"]',
                'meta[name="env:GEMINI_MODEL"]'
            ];
            
            let allMetaTags = [];
            selectors.forEach(selector => {
                const tags = document.querySelectorAll(selector);
                console.log(`🔍 Netlify ${selector}: 找到 ${tags.length} 个标签`);
                tags.forEach(tag => {
                    if (!allMetaTags.includes(tag)) {
                        allMetaTags.push(tag);
                    }
                });
            });
            
            console.log(`🔍 Netlify总共找到 ${allMetaTags.length} 个独特的meta标签`);
            
            let count = 0;
            allMetaTags.forEach(meta => {
                const key = meta.name.replace('env:', '');
                const value = meta.content;
                console.log(`🔍 Netlify发现meta标签: name="${meta.name}" content="${value?.substring(0, 20)}..."`);
                
                if (key && value && !this.isPlaceholderValue(value)) {
                    this.setVariable(key, value);
                    count++;
                    console.log(`✅ Netlify从meta标签添加: ${key}=${value.substring(0, 8)}...`);
                } else {
                    console.log(`❌ Netlify跳过meta标签: ${key}(${!key?'no key':!value?'no value':'invalid value'})`);
                }
            });
            console.log(`📋 Netlify meta标签加载完成，共${count}个变量`);
            
            // 额外调试：显示所有meta标签
            console.log('📋 Netlify所有meta标签:');
            document.querySelectorAll('meta').forEach((meta, index) => {
                console.log(`  ${index + 1}. name="${meta.name}" content="${meta.content}"`);
            });
        }
        
        // 从 localStorage 获取（开发环境）
        loadFromLocalStorage() {
            if (!this.isLocalhost()) return;
            
            const envData = localStorage.getItem('netlify-dev-env');
            if (envData) {
                try {
                    const env = JSON.parse(envData);
                    Object.entries(env).forEach(([key, value]) => {
                        if (!this.isPlaceholderValue(value)) {
                            this.setVariable(key, value);
                        }
                    });
                } catch (error) {
                    console.warn('localStorage环境变量解析失败:', error);
                }
            }
        }
        
        // 从 Netlify 环境变量注入的脚本获取
        loadFromNetlifyInject() {
            const envScript = document.querySelector('script[type="application/json"][id="netlify-env"]');
            if (envScript) {
                try {
                    const env = JSON.parse(envScript.textContent);
                    Object.entries(env).forEach(([key, value]) => {
                        if (!this.isPlaceholderValue(value)) {
                            this.setVariable(key, value);
                        }
                    });
                } catch (error) {
                    console.warn('Netlify环境变量脚本解析失败:', error);
                }
            }
        }
        
        // 设置变量
        setVariable(key, value) {
            if (!key || typeof key !== 'string') return;

            this.variables.set(key, value);
        }
        
        // 获取变量
        getVariable(key, defaultValue = null) {
            return this.variables.get(key) || defaultValue;
        }
        

        
        // 检查是否为占位符值
        isPlaceholderValue(value) {
            if (!value || typeof value !== 'string') return true;
            
            const placeholders = [
                'your-gemini-api-key-here',
                'your-api-key',
                'XXXXX',
                'placeholder',
                'undefined',
                'null',
                ''
            ];
            
            return placeholders.some(p => value.toLowerCase().includes(p.toLowerCase()));
        }
        
        // 检查是否为本地环境
        isLocalhost() {
            const hostname = window.location.hostname;
            return hostname === 'localhost' || 
                   hostname === '127.0.0.1' || 
                   hostname.startsWith('192.168.') ||
                   hostname.startsWith('10.') ||
                   hostname.endsWith('.local');
        }
        
        // 验证 Gemini API 密钥格式
        validateGeminiApiKey(key) {
            if (!key || typeof key !== 'string') return false;
            return /^AIza[0-9A-Za-z_-]{35}$/.test(key);
        }
        
        // 应用到配置对象
        applyToConfig(config) {
            const geminiApiKey = this.getVariable('GEMINI_API_KEY');
            if (geminiApiKey && this.validateGeminiApiKey(geminiApiKey)) {
                config.gemini.apiKey = geminiApiKey;
                config.gemini.enabled = true;
                console.log('🔑 Gemini API密钥已设置到CONFIG');
            } else {
                console.log('⚠️ 未找到有效的GEMINI_API_KEY，AI功能已禁用');
            }
            
            // 应用其他配置
            const model = this.getVariable('GEMINI_MODEL');
            if (model) {
                config.gemini.model = model;
                console.log('🤖 Gemini模型已设置:', model);
            }
        }
    }
    
    // 开发环境帮助函数
    function setNetlifyDevApiKey(apiKey) {
        if (!window.netlifyEnvironmentLoader?.isLocalhost()) {
            console.error('❌ 此功能仅在开发环境可用');
            return false;
        }
        
        if (!window.netlifyEnvironmentLoader.validateGeminiApiKey(apiKey)) {
            console.error('❌ 无效的 API 密钥格式');
            return false;
        }
        
        const envVars = { GEMINI_API_KEY: apiKey };
        localStorage.setItem('netlify-dev-env', JSON.stringify(envVars));
        
        // 重新加载配置
        window.netlifyEnvironmentLoader.setVariable('GEMINI_API_KEY', apiKey);
        if (window.CONFIG) {
            window.netlifyEnvironmentLoader.applyToConfig(window.CONFIG);
            if (window.initializeApiKeyWhenReady) {
                window.initializeApiKeyWhenReady();
            }
        }
        
        console.log('✅ Netlify开发环境API密钥已设置');
        return true;
    }
    
    // 清除开发环境变量
    function clearNetlifyDevEnvironment() {
        if (!window.netlifyEnvironmentLoader?.isLocalhost()) {
            console.error('❌ 此功能仅在开发环境可用');
            return;
        }
        
        localStorage.removeItem('netlify-dev-env');
        console.log('🧹 Netlify开发环境变量已清除');
        
        // 重新加载页面应用更改
        location.reload();
    }
    
    // 导出到全局
    if (typeof window !== 'undefined') {
        window.netlifyEnvironmentLoader = new NetlifyEnvironmentLoader();
        window.setNetlifyDevApiKey = setNetlifyDevApiKey;
        window.clearNetlifyDevEnvironment = clearNetlifyDevEnvironment;
        
        // 自动加载环境变量
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                window.netlifyEnvironmentLoader.loadEnvironmentVariables();
            });
        } else {
            window.netlifyEnvironmentLoader.loadEnvironmentVariables();
        }
    }
    
})();