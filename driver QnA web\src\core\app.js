/*
 * 文件路径: src/core/app.js
 * 文件描述: GoMyHire 司机FAQ系统的主要入口和核心控制器。它负责初始化所有关键组件，管理页面导航、搜索功能和主题切换，并协调各模块间的交互。
 *
 * 深度追踪记录:
 *
 * 1.  **初始化流程 (`constructor` -> `initializeApp`)**:
 *     - `FAQApp` 实例在 `DOMContentLoaded` 事件触发后由 `initializeApp` 函数创建。
 *     - `initializeApp` 会检查 `I18nManager` 和 `DataManager` 是否已加载，确保核心依赖就绪。
 *     - `constructor` 内部按顺序初始化以下核心组件：
 *         - `this.i18n = new I18nManager()`: 创建多语言管理器实例。
 *         - `this.dataManager = new DataManager()`: 创建数据管理器实例，负责FAQ数据的加载、分类和检索。
 *         - `this.initializeGeminiAssistant()`: 调用方法初始化Gemini AI助手（如果配置了API密钥）。
 *         - `this.initializeUI()`: 设置UI元素和事件监听器。
 *         - `this.initializePages()`: 获取页面DOM引用并设置底部导航事件。
 *         - `this.initializeTheme()`: 根据localStorage加载或设置主题。
 *         - `this.initializeFloatingChat()`: 初始化浮动聊天窗口。
 *     - 最后调用 `this.showWelcomePage()` 显示初始页面，并 `app.hideLoading()` 隐藏加载提示。
 *
 * 2.  **Gemini AI助手集成 (`initializeGeminiAssistant`)**:
 *     - **依赖**: `window.CONFIG` (来自 `src/core/config.js`) 和 `GeminiSearchAssistant` (来自 `src/search/gemini-assistant.js`)。
 *     - **逻辑**: 检查 `window.CONFIG.gemini.apiKey` 是否存在且有效（非默认占位符）。
 *     - **数据流**: 如果条件满足，创建 `GeminiSearchAssistant` 实例并将其赋值给 `this.geminiAssistant`。
 *     - **控制流**: 调用 `this.geminiAssistant.setDataManager(this.dataManager)` 将 `DataManager` 实例注入到 `GeminiSearchAssistant` 中，使其能够访问FAQ数据进行AI增强搜索和回答。
 *
 * 3.  **UI交互与事件处理 (`initializeUI`, `setupChatToggle`)**:
 *     - **搜索输入 (`searchInput`)**:
 *         - `input` 事件: 监听用户输入，通过防抖 (`searchTimeout`) 机制，在用户停止输入300ms后触发 `this.performSearch(query)`。
 *         - `keydown` 事件:
 *             - `Enter` 键: 立即触发 `this.performSearch(query)`，并隐藏搜索建议。
 *             - `Escape` 键: 清空搜索框，隐藏搜索建议，并显示欢迎页面。
 *             - `ArrowDown`/`ArrowUp` 键: 调用 `this.navigateSuggestions` 进行搜索建议的键盘导航。
 *         - `click` 事件 (document): 监听点击外部区域，隐藏搜索建议。
 *     - **语言切换按钮 (`.lang-btn`)**:
 *         - `click` 事件: 获取 `data-lang` 属性，调用 `this.switchLanguage(lang)`。
 *     - **主题切换按钮 (`themeToggle`)**:
 *         - `click` 事件: 调用 `this.toggleTheme()`。
 *     - **聊天切换按钮 (`chatToggle`)**:
 *         - `click` 事件: 调用 `this.floatingChat.toggle()` 控制浮动聊天窗口的显示/隐藏，并更新按钮的 `active` 状态和 `title` 属性。
 *
 * 4.  **页面导航与渲染 (`showWelcomePage`, `renderFAQCards`, `showCategoryQuestions`, `showQuestionDetail`)**:
 *     - **数据流**: 这些方法都通过 `this.dataManager` 获取FAQ数据（如 `getCategories()`, `getAllQuestions()`, `getQuestionById()`）。
 *     - **控制流**:
 *         - `showWelcomePage()`: 隐藏所有页面，渲染FAQ分类卡片 (`renderFAQCards`)。
 *         - `renderFAQCards()`: 动态生成分类卡片HTML，并为每个卡片添加 `click` 事件监听器，点击后调用 `this.showCategoryQuestions(categoryId)`。
 *         - `showCategoryQuestions(categoryId)`: 隐藏所有页面，根据 `categoryId` 从 `dataManager` 获取问题列表，动态生成问题HTML，并为每个问题添加 `click` 事件监听器，点击后调用 `this.showQuestionDetail(questionId)`。
 *         - `showQuestionDetail(questionId)`: 隐藏所有页面，根据 `questionId` 从 `dataManager` 获取问题详情，动态渲染FAQ内容，并处理图片加载错误。
 *
 * 5.  **核心搜索逻辑 (`performSearch`, `basicSearch`, `mergeResults`)**:
 *     - **数据流**:
 *         - `performSearch(query)`:
 *             - 首先检查 `this.searchCache`，如果命中则直接使用缓存结果。
 *             - 调用 `this.basicSearch(query)` 执行基础文本搜索，获取初步结果。
 *             - 如果 `this.geminiAssistant` 启用且基础结果较少 (<3个)，则调用 `this.geminiAssistant.searchFAQData(query, this.currentLanguage)` 进行AI增强搜索。
 *             - **数据合并**: `this.mergeResults(basicResults, aiResults.relatedQuestions)` 将基础搜索结果和AI增强结果进行合并，优先保留AI结果。
 *             - 结果存储在 `this.searchCache` 中（最多50个查询），并更新 `this.lastQuery` 和 `this.currentResults`。
 *         - `basicSearch(query)`:
 *             - **数据源**: `this.dataManager.getAllQuestions()`。
 *             - **逻辑**: 对问题标题、内容和标签进行关键词匹配（精确匹配和模糊匹配），并根据优先级进行分数加权。
 *             - **输出**: 返回排序后的前15个匹配问题。
 *         - `mergeResults(basicResults, aiResults)`:
 *             - **逻辑**: 使用 `Map` 结构去重，将AI结果合并到基础结果中，并根据分数重新排序。
 *             - **输出**: 返回合并后的前10个结果。
 *     - **控制流**: `performSearch` 会触发 `this.showSearchPage()` 和 `this.showSearchLoading()` 来更新UI状态，并在搜索完成后调用 `this.displayResults()`。
 *
 * 6.  **语言切换 (`switchLanguage`, `updateUILanguage`)**:
 *     - **数据流**: `switchLanguage(lang)` 调用 `this.i18n.setLanguage(lang)` 来更新 `I18nManager` 的语言设置。
 *     - **控制流**:
 *         - `this.searchCache.clear()`: 语言切换后清空搜索缓存，确保下次搜索使用新语言。
 *         - 根据 `this.currentPage` 和 `currentCategoryId`/`currentQuestionId` 重新渲染当前页面，确保所有文本都更新为新语言。
 *         - `updateUILanguage()`: 更新语言按钮的 `active` 状态和搜索输入框的 `placeholder` 文本。
 *
 * 7.  **浮动聊天窗口交互 (`initializeFloatingChat`, `setupChatToggle`)**:
 *     - **数据流**: `FloatingChatWindow` 实例 (`this.floatingChat`) 在创建时接收 `this` (FAQApp实例) 作为参数，从而能够调用 `this.app.dataManager.searchQuestions` 进行静默搜索和访问 `this.app.geminiAssistant`。
 *     - **控制流**: `FloatingChatWindow` 内部的 `sendMessage` 方法会调用 `this.app.dataManager.searchQuestions` 进行静默搜索，然后 `processAIResponse` 会进一步调用 `this.app.geminiAssistant.generateFAQBasedResponse` 来生成AI回复。
 *
 * 依赖关系 (更详细):
 *   - `FAQApp` 依赖 `I18nManager`、`DataManager`、`GeminiSearchAssistant` 和 `FloatingChatWindow` 的类定义。
 *   - `FAQApp` 通过 `window.CONFIG` 间接依赖 `src/core/config.js`。
 *   - `GeminiSearchAssistant` 依赖 `DataManager` 实例来获取FAQ数据。
 *   - `FloatingChatWindow` 依赖 `FAQApp` 实例来触发搜索和AI交互。
 *
 * 模块间数据与控制流:
 *   - **`app.js` -> `i18n.js`**: `app` 调用 `i18n.setLanguage` 改变语言，`i18n` 更新UI文本。
 *   - **`app.js` -> `data.js`**: `app` 调用 `dataManager` 的方法获取FAQ数据，`dataManager` 返回结构化数据。
 *   - **`app.js` -> `gemini-assistant.js`**: `app` 将 `dataManager` 注入 `geminiAssistant`。`app` 调用 `geminiAssistant.searchFAQData` 或 `generateFAQBasedResponse` 获取AI增强结果。
 *   - **`app.js` -> `floating-chat.js`**: `app` 创建 `floatingChat` 实例并传入自身引用。`floatingChat` 通过此引用调用 `app.dataManager.searchQuestions` 进行静默搜索和访问 `app.geminiAssistant`。
 *   - **`floating-chat.js` -> `app.js`**: `floatingChat` 的 `sendMessage` 方法会调用 `app.dataManager.searchQuestions` 进行静默搜索，形成一个反向调用，将聊天输入传递给主应用进行处理。
 *
 * 这个更深度的追踪记录旨在揭示组件之间的协作方式，以及数据如何在应用程序的不同层级之间流动和转换。
 */
/**
 * GoMyHire Driver FAQ System - 简化版本
 * 目标：从68k行代码简化到5k行以下  
 * 原则：简单胜过复杂，可维护性优先
 * 保持原有FAQApp类名以确保兼容性
 */

class FAQApp {
    constructor() {
        console.log('🚀 初始化GoMyHire FAQ应用...');
        console.log('📱 设备信息:', {
            userAgent: navigator.userAgent,
            viewport: `${window.innerWidth}x${window.innerHeight}`,
            isMobile: this.isMobileDevice(),
            isIOS: this.isIOSDevice()
        });
        console.log('📱 设备信息:', {
            userAgent: navigator.userAgent,
            screenSize: `${screen.width}x${screen.height}`,
            viewport: `${window.innerWidth}x${window.innerHeight}`,
            touchSupport: 'ontouchstart' in window
        });
        
        // 核心组件初始化
        this.i18n = new I18nManager();
        this.dataManager = new DataManager();
        this.dataManager.initialize(); // 初始化数据管理器
    this.currentLanguage = this.i18n?.getCurrentLanguage?.() || 'zh';
        this.currentPage = 'welcome';
        
        // 简单的内存缓存
        this.searchCache = new Map();
        this.lastQuery = '';
        this.currentResults = [];
        
        // Gemini助手（可选）
        this.initializeGeminiAssistant();
        
        // 初始化UI
        this.initializeUI();
        
        // 初始化页面
        this.initializePages();
        
        // 初始化主题
        this.initializeTheme();
        
        // 初始化浮动聊天窗口
        this.initializeFloatingChat();
        
        // 显示欢迎页面和分类
        this.showWelcomePage();
        
        // 应用初始化完成
        console.log('✅ GoMyHire FAQ应用初始化完成');
        
        // 在开发环境中运行自动测试
        if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
            setTimeout(() => {
                this.runFloatingUITests();
            }, 2000); // 延迟2秒等待所有组件加载完成
        }
    }

    // 简化的Gemini初始化
    initializeGeminiAssistant() {
        try {
            console.log('🔧 开始初始化Gemini助手...');
            console.log('📋 当前CONFIG状态:', {
                hasConfig: !!window.CONFIG,
                hasGeminiConfig: !!(window.CONFIG?.gemini),
                apiKey: window.CONFIG?.gemini?.apiKey ? 
                    `${window.CONFIG.gemini.apiKey.substring(0, 10)}...` : 
                    '未设置',
                enabled: window.CONFIG?.gemini?.enabled,
                hasGeminiClass: typeof GeminiSearchAssistant !== 'undefined'
            });
            
            if (window.CONFIG?.gemini?.apiKey && window.CONFIG.gemini.apiKey !== 'your-gemini-api-key-here') {
                console.log('✅ 发现有效的API密钥，创建Gemini助手实例...');
                this.geminiAssistant = new GeminiSearchAssistant(window.CONFIG);
                this.geminiAssistant.setDataManager(this.dataManager);
                console.log('🤖 Gemini助手初始化成功:', {
                    isAvailable: this.geminiAssistant.isAvailable(),
                    hasDataManager: !!this.geminiAssistant.dataManager
                });
            } else {
                console.log('⚠️ 未找到有效的API密钥，Gemini助手未初始化');
                this.geminiAssistant = null;
            }
        } catch (error) {
            console.warn('❌ Gemini初始化失败，使用基础搜索:', error.message);
            console.error('详细错误:', error);
            this.geminiAssistant = null;
        }
    }

    // 简化的UI初始化
    initializeUI() {
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');
        
        if (!searchInput || !searchResults) {
            console.error('❌ 必需的UI元素未找到');
            return;
        }

        // 搜索建议容器
    this.createSearchSuggestions();
        this.currentSuggestionIndex = -1;
        
        // 搜索输入事件 - 增加防抖
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            // 清除之前的定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            if (query.length >= 1) {
                // 显示搜索建议
                this.showSearchSuggestions(query);
                
                if (query.length >= 2) {
                    // 防抖 - 300ms后执行搜索
                    searchTimeout = setTimeout(() => {
                        this.performSearch(query);
                    }, 300);
                }
            } else if (query.length === 0) {
                this.hideSearchSuggestions();
                this.clearResults();
                this.showWelcomePage();
            }
        });

        // 回车搜索和键盘导航
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const query = e.target.value.trim();
                if (query) {
                    // 清除防抖定时器，立即搜索
                    if (searchTimeout) {
                        clearTimeout(searchTimeout);
                    }
                    this.performSearch(query);
                    this.hideSearchSuggestions();
                }
            } else if (e.key === 'Escape') {
                // ESC键清空搜索
                searchInput.value = '';
                this.hideSearchSuggestions();
                this.clearResults();
            } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                // 键盘导航搜索建议
                e.preventDefault();
                this.navigateSuggestions(e.key);
            }
        });

        // 点击外部隐藏建议
        document.addEventListener('click', (e) => {
            if (!searchInput.contains(e.target) && !this.suggestionsContainer?.contains(e.target)) {
                this.hideSearchSuggestions();
            }
        });

        // 语言切换
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = btn.getAttribute('data-lang');
                this.switchLanguage(lang);
            });
        });

        console.log('✅ UI事件监听器已设置');
    }

    // ============== 搜索建议（简化版） ==============
    createSearchSuggestions() {
        // 若已存在则跳过
        if (this.suggestionsContainer) {
            console.log('🔍 搜索建议容器已存在，跳过创建');
            return;
        }
        
        console.log('🔍 正在创建搜索建议容器...');

        const container = document.createElement('div');
        container.id = 'searchSuggestions';
        container.style.position = 'absolute';
        container.style.top = '100%';
        container.style.left = '0';
        container.style.right = '0';
        container.style.background = 'var(--surface-color, #fff)';
        container.style.border = '1px solid var(--border-color, rgba(0,0,0,0.08))';
        container.style.boxShadow = 'var(--shadow-lg, 0 8px 24px rgba(0,0,0,0.12))';
        container.style.borderRadius = '12px';
        container.style.marginTop = '8px';
        container.style.zIndex = '1000';
        container.style.display = 'none';
        container.style.maxHeight = '320px';
        container.style.overflowY = 'auto';
        container.style.padding = '6px';

        const wrapper = document.querySelector('.search-input-wrapper');
        if (wrapper) {
            wrapper.style.position = 'relative';
            wrapper.appendChild(container);
            this.suggestionsContainer = container;
            console.log('✅ 搜索建议容器创建成功');
        } else {
            console.error('❌ 搜索输入包装器未找到，无法创建建议容器');
        }
    }

    showSearchSuggestions(query) {
        if (!this.suggestionsContainer) {
            console.warn('⚠️ 搜索建议容器不存在');
            return;
        }
        
        console.log('🔍 显示搜索建议:', query);

        const allQuestions = this.dataManager.getAllQuestions();
        const lower = query.toLowerCase();
        const suggestions = [];

        for (const q of allQuestions) {
            const title = (q.title[this.currentLanguage] || q.title.zh || '').toLowerCase();
            if (title.includes(lower)) {
                suggestions.push({ id: q.id, title: q.title[this.currentLanguage] || q.title.zh });
            }
            if (suggestions.length >= 8) break;
        }

        if (suggestions.length === 0) {
            this.suggestionsContainer.style.display = 'none';
            this.suggestionsContainer.innerHTML = '';
            console.log('🔍 没有找到匹配的搜索建议');
            return;
        }
        
        console.log(`🔍 找到 ${suggestions.length} 个搜索建议`);

        this.suggestionsContainer.innerHTML = suggestions.map((s, i) => `
            <div class="suggestion-item" data-id="${s.id}" data-index="${i}">
                ${this.highlightQuery(s.title, query)}
            </div>
        `).join('');

        this.suggestionsContainer.querySelectorAll('.suggestion-item').forEach(el => {
            el.addEventListener('click', () => {
                const id = el.getAttribute('data-id');
                this.hideSearchSuggestions();
                this.showQuestionDetail(id);
            });
        });

        this.suggestionsContainer.style.display = 'block';
        this.currentSuggestionIndex = -1;
        console.log('✅ 搜索建议已显示');
    }

    hideSearchSuggestions() {
        if (!this.suggestionsContainer) return;
        this.suggestionsContainer.style.display = 'none';
        this.suggestionsContainer.innerHTML = '';
        this.currentSuggestionIndex = -1;
        
        // 清除所有active类
        this.suggestionsContainer.querySelectorAll('.suggestion-item.active').forEach(el => {
            el.classList.remove('active');
        });
        
        console.log('🔍 搜索建议已隐藏');
    }

    navigateSuggestions(key) {
        if (!this.suggestionsContainer || this.suggestionsContainer.style.display === 'none') return;
        const items = Array.from(this.suggestionsContainer.querySelectorAll('.suggestion-item'));
        if (items.length === 0) return;

        // 计算新的索引
        if (key === 'ArrowDown') this.currentSuggestionIndex = (this.currentSuggestionIndex + 1) % items.length;
        else if (key === 'ArrowUp') this.currentSuggestionIndex = (this.currentSuggestionIndex - 1 + items.length) % items.length;

        // 视觉高亮
        items.forEach((el, idx) => {
            if (idx === this.currentSuggestionIndex) {
                el.classList.add('active');
            } else {
                el.classList.remove('active');
            }
        });

        // 回车选中
        // 该函数由键盘事件调用，按下回车时会触发 performSearch，所以此处不添加回车处理避免冲突
    }

    // 初始化页面
    initializePages() {
        // 获取所有页面元素
        this.pages = {
            welcome: document.getElementById('welcomePage'),
            categories: document.getElementById('categoriesPage'), 
            categoryQuestions: document.getElementById('categoryQuestionsPage'),
            faq: document.getElementById('faqPage'),
            search: document.getElementById('searchPage')
        };

        // 底部导航事件
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = item.getAttribute('data-tab');
                if (tab === 'faq') {
                    this.showWelcomePage();
                }
                // 更新导航状态
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
            });
        });

        // 主题切换事件
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        console.log('✅ 页面初始化完成');
    }

    // 显示欢迎页面
    showWelcomePage() {
        this.hideAllPages();
        if (this.pages.welcome) {
            this.pages.welcome.classList.remove('page-hidden');
            this.renderFAQCards();
        }
        this.currentPage = 'welcome';
    }

    // 隐藏所有页面
    hideAllPages() {
        Object.values(this.pages).forEach(page => {
            if (page) page.classList.add('page-hidden');
        });
    }

    // 渲染FAQ分类卡片
    renderFAQCards() {
        const faqCards = document.getElementById('faqCards');
        if (!faqCards) return;

        const categories = this.dataManager.getCategories();
        const allQuestions = this.dataManager.getAllQuestions();

        // 计算每个分类的问题数量
        const categoryCounts = {};
        Object.keys(categories).forEach(catId => {
            categoryCounts[catId] = allQuestions.filter(q => q.category === catId).length;
        });

        // 多语言文本
        const texts = {
            'zh': {
                title: '常见问题分类',
                subtitle: '选择分类查看相关问题',
                questionsCount: '个问题'
            },
            'en': {
                title: 'FAQ Categories',
                subtitle: 'Select a category to view related questions',
                questionsCount: ' questions'
            },
            'ms': {
                title: 'Kategori FAQ',
                subtitle: 'Pilih kategori untuk melihat soalan berkaitan',
                questionsCount: ' soalan'
            }
        };
        
        const currentTexts = texts[this.currentLanguage] || texts['zh'];

        // 生成分类卡片HTML
        const cardsHTML = Object.entries(categories).map(([catId, category]) => {
            const name = category.name[this.currentLanguage] || category.name.zh;
            const count = categoryCounts[catId] || 0;
            const icon = category.icon || '📋';

            return `
                <div class="category-card" data-category="${catId}">
                    <div class="category-icon">${icon}</div>
                    <div class="category-name">${name}</div>
                    <div class="category-count">${count}${currentTexts.questionsCount}</div>
                </div>
            `;
        }).join('');

        faqCards.innerHTML = `
            <div class="welcome-header">
                <h2>${currentTexts.title}</h2>
                <p>${currentTexts.subtitle}</p>
            </div>
            <div class="category-grid">
                ${cardsHTML}
            </div>
        `;

        // 添加分类卡片点击事件
        faqCards.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const categoryId = card.getAttribute('data-category');
                this.showCategoryQuestions(categoryId);
            });
        });
    }

    // 显示分类问题页面
    showCategoryQuestions(categoryId) {
        this.hideAllPages();
        if (!this.pages.categoryQuestions) return;
        
        // 保存当前分类ID用于语言切换
        this.currentCategoryId = categoryId;

        const category = this.dataManager.getCategories()[categoryId];
        const categoryName = category.name[this.currentLanguage] || category.name.zh;
        
        const allQuestions = this.dataManager.getAllQuestions();
        const categoryQuestions = allQuestions.filter(q => q.category === categoryId);

        const questionsHTML = categoryQuestions.map(question => {
            const title = question.title[this.currentLanguage] || question.title.zh;
            return `
                <div class="related-item" data-id="${question.id}">
                    <div class="related-title">${title}</div>
                    <div class="related-arrow">→</div>
                </div>
            `;
        }).join('');

        this.pages.categoryQuestions.innerHTML = `
            <div class="page-header">
                <button class="back-btn">← 返回</button>
                <h2>${categoryName}</h2>
            </div>
            <div class="card-grid">
                ${questionsHTML}
            </div>
        `;

        // 添加返回按钮点击事件
        const backBtn = this.pages.categoryQuestions.querySelector('.back-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                this.showWelcomePage();
            });
        }

        // 添加问题点击事件 @SHARED_STYLE 事件绑定收敛到 .related-item
        this.pages.categoryQuestions.querySelectorAll('.related-item').forEach(item => {
            item.addEventListener('click', () => {
                const questionId = item.getAttribute('data-id');
                this.showQuestionDetail(questionId);
            });
        });

        this.pages.categoryQuestions.classList.remove('page-hidden');
        this.currentPage = 'categoryQuestions';
    }

    // 显示问题详情（重写版本）
    showQuestionDetail(questionId) {
        this.hideAllPages();
        if (!this.pages.faq) return;
        
        // 保存当前问题ID用于语言切换
        this.currentQuestionId = questionId;

        const allQuestions = this.dataManager.getAllQuestions();
        const question = allQuestions.find(q => q.id === questionId);
        if (!question) return;

        const title = question.title[this.currentLanguage] || question.title.zh;
        const content = question.content[this.currentLanguage] || question.content.zh;
        const category = this.dataManager.getCategories()[question.category];
        const categoryName = category?.name[this.currentLanguage] || category?.name.zh || '未分类';

        this.pages.faq.innerHTML = `
            <div id="faqContent" class="faq-content">
                <div class="breadcrumb">
                    <button class="breadcrumb-item home-btn">首页</button>
                    <span class="breadcrumb-separator">></span>
                    <button class="breadcrumb-item category-btn">${categoryName}</button>
                    <span class="breadcrumb-separator">></span>
                    <span class="breadcrumb-current">${title}</span>
                </div>
                
                <div class="faq-header">
                    <div class="faq-id">#${question.id}</div>
                    <h1 class="faq-title">${title}</h1>
                </div>
                
                <div class="faq-body">
                    ${content}
                </div>
            </div>
        `;

        // 处理图片加载失败，避免破图影响阅读
        try {
            const imgs = this.pages.faq.querySelectorAll('.faq-body img');
            imgs.forEach(img => {
                img.addEventListener('error', () => {
                    // 使用占位图替换失败的图片
                    const originalSrc = img.getAttribute('data-original-src') || img.src;
                    img.src = './images/placeholder.svg';
                    img.alt = `图片加载失败: ${originalSrc}`;
                    img.style.opacity = '0.7';
                }, { once: true });
            });
        } catch (_) { /* noop */ }

        // 添加面包屑导航事件监听器
        const homeBtn = this.pages.faq.querySelector('.home-btn');
        const categoryBtn = this.pages.faq.querySelector('.category-btn');
        
        if (homeBtn) {
            homeBtn.addEventListener('click', () => {
                this.showWelcomePage();
            });
        }
        
        if (categoryBtn) {
            categoryBtn.addEventListener('click', () => {
                this.showCategoryQuestions(question.category);
            });
        }

        this.pages.faq.classList.remove('page-hidden');
        this.currentPage = 'faq';
    }


    // 主题切换功能
    toggleTheme() {
        const body = document.body;
        const themeIcon = document.querySelector('.theme-icon');
        
        if (body.classList.contains('dark-theme')) {
            body.classList.remove('dark-theme');
            themeIcon.textContent = '🌙';
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.add('dark-theme');
            themeIcon.textContent = '☀️';
            localStorage.setItem('theme', 'dark');
        }
    }

    // 初始化主题
    initializeTheme() {
        const savedTheme = localStorage.getItem('theme');
        const themeIcon = document.querySelector('.theme-icon');
        
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            if (themeIcon) themeIcon.textContent = '☀️';
        } else {
            document.body.classList.remove('dark-theme');
            if (themeIcon) themeIcon.textContent = '🌙';
        }
    }

    // 初始化浮动聊天窗口
    initializeFloatingChat() {
        try {
            if (typeof FloatingChatWindow !== 'undefined') {
                this.floatingChat = new FloatingChatWindow(this);
                console.log('✅ 浮动聊天窗口已启用');
                
                // 添加聊天窗口切换功能到Gemini按钮
                this.setupChatToggle();
            } else {
                console.log('ℹ️ FloatingChatWindow组件未找到，跳过初始化');
            }
        } catch (error) {
            console.warn('⚠️ 浮动聊天窗口初始化失败:', error.message);
        }
    }
    
    // 设置聊天窗口切换功能
    setupChatToggle() {
        const chatToggle = document.getElementById('chatToggle');
        if (chatToggle && this.floatingChat) {
        chatToggle.addEventListener('click', () => {
                this.floatingChat.toggle();
                
                // 切换按钮状态
                if (this.floatingChat.isVisible) {
                    chatToggle.classList.add('active');
            chatToggle.title = this.i18n.t('chatToggleClose');
                } else {
                    chatToggle.classList.remove('active');
            chatToggle.title = this.i18n.t('chatToggleOpen');
                }
            });
            // 初始title
            chatToggle.title = this.i18n.t(this.floatingChat?.isVisible ? 'chatToggleClose' : 'chatToggleOpen');
            
            console.log('✅ 聊天按钮事件已绑定');
        }
    }

    // 核心搜索功能 - 显示在搜索页面
    async performSearch(query) {
        // console.log('🔍 搜索:', query);
        
        // 切换到搜索页面
        this.showSearchPage();
        
        // 检查缓存 - 使用带语言的缓存键保持与DataManager一致
        const cacheKey = `${query}_${this.currentLanguage}`;
        if (this.searchCache.has(cacheKey)) {
            // console.log('📦 使用缓存结果 (键:', cacheKey, ')');
            this.displayResults(this.searchCache.get(cacheKey), query);
            return;
        }

        // 调试信息：显示当前缓存状态
        // console.log('🔍 搜索调试信息:', {
        //     query: query,
        //     currentLanguage: this.currentLanguage,
        //     cacheKey: cacheKey,
        //     cacheSize: this.searchCache.size,
        //     availableCacheKeys: Array.from(this.searchCache.keys())
        // });

        // 显示加载状态
        this.showSearchLoading();

        try {
            // 基础文本搜索
            let results = this.basicSearch(query);
            
            // 如果有Gemini助手且结果较少，尝试AI增强
            if (this.geminiAssistant && results.length < 3) {
                try {
                    const aiResults = await this.geminiAssistant.searchFAQData(query, this.currentLanguage);
                    if (aiResults.relatedQuestions?.length > 0) {
                        // 合并AI结果
                        results = this.mergeResults(results, aiResults.relatedQuestions);
                        // console.log('🤖 AI增强搜索结果');
                    }
                } catch (error) {
                    console.warn('⚠️ AI搜索失败，使用基础结果:', error.message);
                }
            }

            // 缓存结果（最多50个查询）- 使用带语言的缓存键
            const cacheKey = `${query}_${this.currentLanguage}`;
            if (this.searchCache.size >= 50) {
                const firstKey = this.searchCache.keys().next().value;
                this.searchCache.delete(firstKey);
            }
            this.searchCache.set(cacheKey, results);

            this.displayResults(results, query);
            this.lastQuery = query;
            this.currentResults = results;

        } catch (error) {
            console.error('❌ 搜索失败:', error);
            this.showSearchError('搜索时发生错误，请稍后重试');
        }
    }

    // 显示搜索页面
    showSearchPage() {
        this.hideAllPages();
        if (this.pages.search) {
            this.pages.search.classList.remove('page-hidden');
        }
        this.currentPage = 'search';
    }

    // 基础文本搜索 - 简单有效，集成模糊搜索能力，支持跨语言搜索
    basicSearch(query) {
        const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);
        const allQuestions = this.dataManager.getAllQuestions();
        const results = [];

        allQuestions.forEach(question => {
            // 搜索所有语言的内容，而不仅仅是当前语言
            const titleZh = (question.title.zh || '').toLowerCase();
            const titleEn = (question.title.en || '').toLowerCase();
            const titleMs = (question.title.ms || '').toLowerCase();
            
            const contentZh = (question.content.zh || '').toLowerCase();
            const contentEn = (question.content.en || '').toLowerCase();
            const contentMs = (question.content.ms || '').toLowerCase();
            
            const tags = question.tags || [];
            
            let score = 0;
            let matchedLanguage = this.currentLanguage; // 默认匹配语言

            // 1. 精确匹配 - 高分，检查所有语言
            searchTerms.forEach(term => {
                // 检查中文标题和内容
                if (titleZh.includes(term)) {
                    score += 10;
                    matchedLanguage = 'zh';
                }
                if (contentZh.includes(term)) {
                    score += 3;
                    matchedLanguage = 'zh';
                }
                
                // 检查英文标题和内容
                if (titleEn.includes(term)) {
                    score += 10;
                    matchedLanguage = 'en';
                }
                if (contentEn.includes(term)) {
                    score += 3;
                    matchedLanguage = 'en';
                }
                
                // 检查马来文标题和内容
                if (titleMs.includes(term)) {
                    score += 10;
                    matchedLanguage = 'ms';
                }
                if (contentMs.includes(term)) {
                    score += 3;
                    matchedLanguage = 'ms';
                }
                
                // 检查标签
                tags.forEach(tag => {
                    if (tag.toLowerCase().includes(term)) score += 5;
                });
            });

            // 2. 模糊匹配 - 如果没有精确匹配，尝试部分匹配（检查所有语言）
            if (score === 0) {
                const queryLower = query.toLowerCase();
                searchTerms.forEach(term => {
                    if (term.length >= 2) {
                        const partialTerm = term.substring(0, Math.max(2, term.length - 1));
                        
                        // 检查所有语言的部分匹配
                        if (titleZh.includes(partialTerm) || contentZh.includes(partialTerm)) {
                            score += 2;
                            matchedLanguage = 'zh';
                        }
                        if (titleEn.includes(partialTerm) || contentEn.includes(partialTerm)) {
                            score += 2;
                            matchedLanguage = 'en';
                        }
                        if (titleMs.includes(partialTerm) || contentMs.includes(partialTerm)) {
                            score += 2;
                            matchedLanguage = 'ms';
                        }
                    }
                });
                
                // 标题相似度检查（检查当前语言和匹配语言的标题）
                const currentTitle = question.title[this.currentLanguage] || question.title.zh || '';
                const matchedTitle = question.title[matchedLanguage] || question.title.zh || '';
                
                if (this.calculateSimpleSimilarity(queryLower, currentTitle.toLowerCase()) > 0.3) score += 3;
                if (this.calculateSimpleSimilarity(queryLower, matchedTitle.toLowerCase()) > 0.3) score += 2;
            }

            // 3. 优先级加权
            if (score > 0) {
                if (question.priority === 'high') score *= 1.2;
                else if (question.priority === 'medium') score *= 1.1;
                
                results.push({
                    ...question,
                    score,
                    matchType: score >= 5 ? 'exact' : 'fuzzy',
                    matchedLanguage // 记录哪种语言匹配了
                });
            }
        });

        // 按分数排序，返回前15个
        return results
            .sort((a, b) => b.score - a.score)
            .slice(0, 15);
    }

    // 简单相似度计算 - 基于公共字符
    calculateSimpleSimilarity(str1, str2) {
        if (!str1 || !str2) return 0;
        
        const shorter = str1.length < str2.length ? str1 : str2;
        const longer = str1.length < str2.length ? str2 : str1;
        
        if (shorter.length === 0) return 0;
        
        let commonChars = 0;
        for (let i = 0; i < shorter.length; i++) {
            if (longer.includes(shorter[i])) {
                commonChars++;
            }
        }
        
        return commonChars / shorter.length;
    }

    // 合并搜索结果
    mergeResults(basicResults, aiResults) {
        const resultMap = new Map();
        
        // 添加基础结果
        basicResults.forEach(result => {
            resultMap.set(result.id, result);
        });
        
        // 添加AI结果（如果不重复）
        aiResults.forEach(result => {
            if (!resultMap.has(result.id)) {
                resultMap.set(result.id, {
                    ...result,
                    score: result.score || 1,
                    matchType: 'ai'
                });
            }
        });

        return Array.from(resultMap.values())
            .sort((a, b) => b.score - a.score)
            .slice(0, 10);
    }

    // 显示搜索结果 - 在搜索页面显示
    displayResults(results, query) {
        const container = document.getElementById('searchResults');
        if (!container) return;

        if (results.length === 0) {
            container.innerHTML = `
                <div class="search-header">
                    <button class="back-btn">← ${this.i18n.t('searchBackToHome')}</button>
                    <h2>${this.i18n.t('search')}: "${query}"</h2>
                    <p>${this.i18n.t('searchTryDifferentKeywords')}</p>
                </div>
                <div class="no-results">
                    <div class="icon">🔍</div>
                    <h3>${this.i18n.t('searchNoResultsTitle')}</h3>
                    <p>${this.i18n.t('searchSuggestionTitle')}</p>
                    <ul>
                        <li>${this.i18n.t('searchSuggestion1')}</li>
                        <li>${this.i18n.t('searchSuggestion2')}</li>
                        <li>${this.i18n.t('searchSuggestion3')}</li>
                    </ul>
                    <button class="back-to-home-btn">${this.i18n.t('searchBackToHome')}</button>
                </div>
            `;
            
            // 添加返回首页按钮事件监听器
            const backToHomeBtn = container.querySelector('.back-to-home-btn');
            const searchBackBtn = container.querySelector('.back-btn');
            if (backToHomeBtn) {
                backToHomeBtn.addEventListener('click', () => {
                    this.showWelcomePage();
                });
            }
            if (searchBackBtn) {
                searchBackBtn.addEventListener('click', () => {
                    this.showWelcomePage();
                });
            }
            
            return;
        }

        // 计算结果统计
        const exactMatches = results.filter(r => r.matchType === 'exact').length;
        const aiMatches = results.filter(r => r.matchType === 'ai').length;
        const fuzzyMatches = results.filter(r => r.matchType === 'fuzzy').length;
        
        let summaryText = this.i18n.t('searchResultsCount', { count: results.length });
        if (exactMatches > 0 && (aiMatches > 0 || fuzzyMatches > 0)) {
            summaryText += ` (${exactMatches}${this.i18n.t('searchExactMatch')})`;
        }

        const html = results.map(result => this.createSearchResultHTML(result, query)).join('');
        
        container.innerHTML = `
            <div class="search-header">
                <button class="back-btn">← ${this.i18n.t('searchBackToHome')}</button>
                <h2>${this.i18n.t('search')}: "${query}"</h2>
                <p>${summaryText}</p>
            </div>
            <div class="search-results-list">
                ${html}
            </div>`;

        // 添加搜索结果页面的返回按钮事件监听器
        const searchBackBtn = container.querySelector('.back-btn');
        if (searchBackBtn) {
            searchBackBtn.addEventListener('click', () => {
                this.showWelcomePage();
            });
        }

        // 添加点击事件
        container.querySelectorAll('.search-result-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                const questionId = results[index].id;
                this.showQuestionDetail(questionId);
            });
        });
    }

    // 创建搜索结果HTML - 增强版
    createSearchResultHTML(result, query) {
        const title = result.title[this.currentLanguage] || result.title.zh;
        const content = result.content[this.currentLanguage] || result.content.zh;
        const excerpt = this.createExcerpt(content, 200);
        
        // 匹配类型图标和标签
        const matchTypeMap = {
            'exact': { icon: '🎯', label: this.i18n.t('searchExactMatch'), color: '#10b981' },
            'ai': { icon: '🤖', label: this.i18n.t('searchAiRecommend'), color: '#9333ea' },
            'fuzzy': { icon: '🔍', label: this.i18n.t('searchFuzzyMatch'), color: '#f59e0b' }
        };
        
        const matchInfo = matchTypeMap[result.matchType] || matchTypeMap['fuzzy'];
        const category = this.dataManager.getCategories()[result.category];
        const categoryName = category?.name[this.currentLanguage] || category?.name.zh || this.i18n.t('uncategorized');

        // 计算相关度分数显示
        const scorePercentage = Math.min(Math.round((result.score / 15) * 100), 100);

        return `
            <div class="search-result-item" data-id="${result.id}">
                <div class="search-result-header">
                    <div class="search-result-id">#${result.id}</div>
                    <div class="search-result-match-info">
                        <span class="match-type-badge" style="background: ${matchInfo.color}20; color: ${matchInfo.color}; border: 1px solid ${matchInfo.color}40;">
                            ${matchInfo.icon} ${matchInfo.label}
                        </span>
                        <span class="score-indicator" title="${this.i18n.t('searchRelevance')}: ${scorePercentage}%">
                            <div class="score-bar">
                                <div class="score-fill" style="width: ${scorePercentage}%"></div>
                            </div>
                        </span>
                    </div>
                    <div class="search-result-category">${categoryName}</div>
                </div>
                <h3 class="search-result-title">${this.highlightQuery(title, query)}</h3>
                <div class="search-result-excerpt">
                    ${this.highlightQuery(excerpt, query)}
                </div>
                <div class="search-result-meta">
                    <span class="result-timestamp">${new Date().toLocaleDateString()}</span>
                </div>
            </div>
        `;
    }


    // 高亮搜索词
    highlightQuery(text, query) {
        if (!query || !text) return text;
        
        const terms = query.split(/\s+/).filter(term => term.length > 0);
        let highlightedText = text;
        
        terms.forEach(term => {
            const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    // 创建内容摘要
    createExcerpt(content, maxLength) {
        const text = content.replace(/<[^>]*>/g, '').trim();
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    // 获取分类名称
    getCategoryName(categoryId) {
        const categories = this.dataManager.getCategories();
        const category = categories[categoryId];
        return category ? category.name[this.currentLanguage] || category.name.zh : '未分类';
    }

    // 正则表达式转义
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // 智能缓存清理 - 清理特定查询的所有语言缓存
    clearQueryCache(query) {
        if (!query) return;
        
        const keysToDelete = [];
        for (const key of this.searchCache.keys()) {
            // 匹配格式: "查询_语言" 或纯查询字符串（向后兼容）
            if (key === query || key.startsWith(`${query}_`)) {
                keysToDelete.push(key);
            }
        }
        
        keysToDelete.forEach(key => {
            this.searchCache.delete(key);
            console.log('🗑️ 清理缓存键:', key);
        });
    }

    // 获取查询的所有语言缓存键
    getQueryCacheKeys(query) {
        const keys = [];
        for (const key of this.searchCache.keys()) {
            if (key === query || key.startsWith(`${query}_`)) {
                keys.push(key);
            }
        }
        return keys;
    }


    // 语言切换
    switchLanguage(lang) {
        if (this.currentLanguage === lang) return;
        
        this.currentLanguage = lang;
        this.i18n.setLanguage(lang);
        
        // 根据当前页面重新渲染内容
        if (this.currentPage === 'welcome') {
            this.renderFAQCards();
        } else if (this.currentPage === 'categoryQuestions' && this.currentCategoryId) {
            this.showCategoryQuestions(this.currentCategoryId);
        } else if (this.currentPage === 'faq' && this.currentQuestionId) {
            this.showQuestionDetail(this.currentQuestionId);
        } else if (this.currentPage === 'search' && this.lastQuery) {
            // 搜索结果页面：智能缓存查找和结果复用
            const newCacheKey = `${this.lastQuery}_${lang}`;
            const oldCacheKey = `${this.lastQuery}_${this.currentLanguage}`;
            
            if (this.searchCache.has(newCacheKey)) {
                // 优先使用新语言的缓存结果
                console.log('🌐 语言切换：使用新语言缓存结果');
                this.displayResults(this.searchCache.get(newCacheKey), this.lastQuery);
            } else if (this.searchCache.has(oldCacheKey)) {
                // 复用原语言的缓存结果，重新渲染UI（跨语言搜索支持）
                console.log('🌐 语言切换：复用原语言缓存，重新渲染UI');
                const cachedResults = this.searchCache.get(oldCacheKey);
                // 将结果也缓存到新语言键下，避免重复搜索
                this.searchCache.set(newCacheKey, cachedResults);
                this.displayResults(cachedResults, this.lastQuery);
            } else {
                // 没有任何缓存，执行新搜索
                console.log('🌐 语言切换：缓存未命中，重新搜索');
                this.performSearch(this.lastQuery);
            }
        } else if (this.lastQuery) {
            // 其他情况下如果有搜索查询，重新搜索
            this.performSearch(this.lastQuery);
        }
        
        // 更新UI语言
        this.updateUILanguage();
        
        // 缓存健康检查和调试信息
        console.log('🌐 语言切换完成 - 缓存状态:', {
            fromLanguage: this.currentLanguage, // 注意：此时currentLanguage已经更新为新语言
            toLanguage: lang,
            cacheSize: this.searchCache.size,
            lastQuery: this.lastQuery,
            currentPage: this.currentPage,
            availableCacheKeys: Array.from(this.searchCache.keys()).slice(0, 5) // 只显示前5个避免日志过长
        });
        
        console.log('🌐 语言切换至:', lang);
    }

    // 更新UI语言
    updateUILanguage() {
        // 更新语言按钮状态
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.toggle('active', btn.getAttribute('data-lang') === this.currentLanguage);
        });

        // 更新占位符和按钮文本
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            const placeholders = {
                'zh': '搜索常见问题...',
                'en': 'Search FAQ...',
                'ms': 'Cari FAQ...'
            };
            searchInput.placeholder = placeholders[this.currentLanguage] || placeholders['zh'];
        }
        // 更新聊天按钮提示
        const chatToggle = document.getElementById('chatToggle');
        if (chatToggle && this.i18n) {
            chatToggle.title = this.i18n.t(this.floatingChat?.isVisible ? 'chatToggleClose' : 'chatToggleOpen');
        }
        
    }

    // 显示搜索加载状态 - 使用新的美化样式
    showSearchLoading() {
        const container = document.getElementById('searchResults');
        if (container) {
            container.innerHTML = `
                <div class="search-header">
                    <h2>${this.i18n.t('searching')}</h2>
                    <p>${this.i18n.t('searchingDescription')}</p>
                </div>
                <div class="search-loading">
                    ${this.createLoadingItemHTML()}
                    ${this.createLoadingItemHTML()}
                    ${this.createLoadingItemHTML()}
                </div>
            `;
        }
    }

    // 创建加载项HTML
    createLoadingItemHTML() {
        return `
            <div class="search-loading-item">
                <div class="loading-header"></div>
                <div class="loading-title"></div>
                <div class="loading-content"></div>
                <div class="loading-content"></div>
            </div>
        `;
    }

    // 隐藏页面加载提示
    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.remove('show');
            console.log('✅ 页面加载提示已隐藏');
        }
    }

    // 显示搜索错误
    showSearchError(message) {
        const container = document.getElementById('searchResults');
        if (container) {
            container.innerHTML = `
                <div class="search-header">
                    <h2>搜索出错</h2>
                    <p>搜索过程中发生了错误</p>
                </div>
                <div class="search-error" style="text-align: center; padding: 2rem; color: #d32f2f; background: #ffeaea; border-radius: 8px; margin: 2rem 0;">
                    <span>⚠️ ${message}</span>
                    <br><br>
                    <button class="back-to-home-btn">返回首页</button>
                </div>
            `;

            // 添加错误页面返回按钮事件监听器
            const errorBackBtn = container.querySelector('.back-to-home-btn');
            if (errorBackBtn) {
                errorBackBtn.addEventListener('click', () => {
                    this.showWelcomePage();
                });
            }
        }
    }

    // 清空搜索结果
    clearResults() {
        const container = document.getElementById('searchResults');
        if (container) {
            container.innerHTML = '';
        }
        this.lastQuery = '';
        this.currentResults = [];
    }

    // 获取系统状态 - 简化版本
    getSystemStatus() {
        return {
            cacheSize: this.searchCache.size,
            lastQuery: this.lastQuery,
            resultCount: this.currentResults.length,
            geminiEnabled: !!this.geminiAssistant,
            currentLanguage: this.currentLanguage,
            totalQuestions: this.dataManager.getAllQuestions().length
        };
    }

    // 设备检测方法
    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (window.innerWidth <= 768 && window.innerHeight <= 1024);
    }

    isIOSDevice() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }
}

// 应用初始化
let app;

function initializeApp() {
    try {
        // 等待必需的依赖加载
        if (typeof I18nManager === 'undefined' || typeof DataManager === 'undefined') {
            console.log('⏳ 等待依赖加载...');
            setTimeout(initializeApp, 100);
            return;
        }

        app = new FAQApp();
        window.app = app; // 全局访问
        console.log('🎉 简化版FAQ应用启动成功');
        
        // 隐藏页面加载提示
        app.hideLoading();
        
        // 调试信息
        console.log('📊 系统状态:', app.getSystemStatus());
        
    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        document.body.innerHTML = `
            <div style="text-align: center; padding: 50px; color: #d32f2f;">
                <h2>应用启动失败</h2>
                <p>${error.message}</p>
                <button class="reload-btn">重新加载</button>
            </div>
        `;

        // 添加重新加载按钮事件监听器
        const reloadBtn = document.querySelector('.reload-btn');
        if (reloadBtn) {
            reloadBtn.addEventListener('click', () => {
                location.reload();
            });
        }
    }
}

document.addEventListener('DOMContentLoaded', initializeApp);