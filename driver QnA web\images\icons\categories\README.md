# 分类图标

## 📁 文件列表

### 主要分类图标
- `icon_category_technical_32px.svg` - 技术问题
- `icon_category_financial_32px.svg` - 财务问题  
- `icon_category_service_32px.svg` - 服务流程
- `icon_category_emergency_32px.svg` - 紧急处理
- `icon_category_communication_32px.svg` - 沟通技巧
- `icon_category_career_32px.svg` - 职业发展
- `icon_category_rating_32px.svg` - 评价评分
- `icon_category_policy_32px.svg` - 平台政策
- `icon_category_compliance_32px.svg` - 法律合规
- `icon_category_advanced_32px.svg` - 高级功能

### 辅助图标
- `icon_category_general_32px.svg` - 通用问题
- `icon_category_international_32px.svg` - 国际服务

## 🎨 设计规范

### 图标样式
- **风格**：扁平化 + 线性图标
- **颜色**：主题色 + 白色
- **描边**：2px
- **圆角**：4px

### 尺寸规范
- **主尺寸**：32x32px
- **备用尺寸**：24x24px, 48x48px

### 颜色映射
```
技术问题 → 蓝色 (#3B82F6)
财务问题 → 绿色 (#10B981)
服务流程 → 橙色 (#F59E0B)
紧急处理 → 红色 (#EF4444)
沟通技巧 → 紫色 (#8B5CF6)
职业发展 → 青色 (#06B6D4)
评价评分 → 粉色 (#EC4899)
平台政策 → 灰色 (#6B7280)
法律合规 → 深红 (#DC2626)
高级功能 → 金色 (#F59E0B)
```

## 📱 实际使用示例

### HTML引用
```html
<!-- 技术问题图标 -->
<img src="images/icons/categories/icon_category_technical_32px.svg" alt="技术问题" class="category-icon">

<!-- 响应式图标 -->
<img src="images/icons/categories/icon_category_financial_32px.svg" 
     srcset="images/icons/categories/icon_category_financial_24px.svg 1x,
             images/icons/categories/icon_category_financial_32px.svg 2x"
     alt="财务问题">
```

### CSS使用
```css
.category-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
}

@media (max-width: 480px) {
    .category-icon {
        width: 24px;
        height: 24px;
    }
}
```