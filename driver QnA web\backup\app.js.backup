// GoMyHire司机FAQ系统主应用
class FAQApp {
    constructor() {
        // 首先初始化安全验证器
        this.securityValidator = window.securityValidator || new SecurityValidator();
        console.log('🛡️ 安全验证器已集成');
        
        this.i18n = new I18nManager();
        this.dataManager = new DataManager();

        // 初始化数据管理器（重要：确保统一分类系统正常工作）
        this.dataManager.initialize();

        // 调试信息：验证分类系统是否正常工作
        console.log('📋 DataManager初始化完成');
        const categories = this.dataManager.getCategories();
        console.log('📊 可用分类数量:', Object.keys(categories).length);
        console.log('📊 分类列表:', Object.keys(categories));

        // 验证分类系统完整性
        if (this.dataManager.validateCategorySystem) {
            const validation = this.dataManager.validateCategorySystem();
            if (!validation.success) {
                console.warn('⚠️ 分类系统验证失败:', validation.issues);
            } else {
                console.log('✅ 分类系统验证通过');
            }
        }
        
        // 安全地初始化 Gemini 助手
        try {
            if (window.CONFIG && window.CONFIG.gemini) {
                this.geminiAssistant = new GeminiSearchAssistant(window.CONFIG);
                this.geminiEnabled = true;

                // 设置DataManager引用，让Gemini助手能够访问FAQ数据
                this.geminiAssistant.setDataManager(this.dataManager);

                // API连接将在用户首次搜索时进行测试，避免初始化时的不必要调用
                console.log('✅ Gemini助手已初始化，API连接将在首次使用时测试');
            } else {
                console.warn('CONFIG not available, Gemini assistant disabled');
                this.geminiAssistant = null;
                this.geminiEnabled = false;
            }
        } catch (error) {
            console.error('Failed to initialize Gemini assistant:', error);
            this.geminiAssistant = null;
            this.geminiEnabled = false;
        }
        
        this.currentPage = 'welcome';
        this.currentQuestion = null;
        this.searchTimeout = null; // 用于防抖搜索
        this.currentTheme = localStorage.getItem('theme') || 'light';
        
        // 设置全局错误处理
        this.setupGlobalErrorHandling();

        // 初始化统一搜索引擎 (新架构)
        console.log('🔍 检查统一搜索引擎加载状态...');
        console.log('📦 UnifiedSearchEngine存在:', typeof UnifiedSearchEngine !== 'undefined');
        console.log('📦 SearchUIRenderer存在:', typeof SearchUIRenderer !== 'undefined');
        
        if (typeof UnifiedSearchEngine !== 'undefined') {
            this.unifiedSearchEngine = new UnifiedSearchEngine(
                this.dataManager,
                this.geminiAssistant,
                this.i18n
            );
            console.log('✅ 统一搜索引擎初始化成功');
            
            // RAG引擎会在UnifiedSearchEngine内部异步初始化
            // 我们可以监听初始化状态
            this.monitorRAGInitialization();
            
            // 初始化搜索UI渲染器
            const searchResultsContainer = document.getElementById('searchResults');
            console.log('🎯 搜索结果容器存在:', !!searchResultsContainer);
            
            if (searchResultsContainer && typeof SearchUIRenderer !== 'undefined') {
                this.searchUIRenderer = new SearchUIRenderer(searchResultsContainer, this.i18n);
                console.log('✅ 搜索UI渲染器初始化成功');
                console.log('🔧 修复已应用：搜索结果将正确显示');
            } else {
                console.error('❌ 搜索UI渲染器初始化失败');
                this.searchUIRenderer = null;
            }
        } else {
            console.warn('⚠️ UnifiedSearchEngine未加载，使用传统搜索架构');
            this.unifiedSearchEngine = null;
            this.searchUIRenderer = null;
        }

        // 初始化流式搜索引擎 (传统架构，保持兼容)
        this.streamingEnabled = window.CONFIG?.search?.streaming?.enabled ?? false;
        if (this.streamingEnabled && typeof StreamingSearchEngine !== 'undefined') {
            this.streamingEngine = new StreamingSearchEngine(
                this.geminiAssistant,
                this.dataManager,
                this.i18n
            );
            console.log('✅ 流式搜索引擎初始化成功 (兼容模式)');
        } else {
            this.streamingEngine = null;
            console.log('ℹ️ 流式搜索未启用或StreamingSearchEngine未加载');
        }

        // 初始化搜索降级策略管理器
        if (typeof SearchFallbackManager !== 'undefined') {
            this.fallbackManager = new SearchFallbackManager(
                this.geminiAssistant,
                this.streamingEngine,
                this.dataManager,
                this.i18n
            );
            console.log('✅ 搜索降级策略管理器初始化成功');
        } else {
            this.fallbackManager = null;
            console.log('⚠️ SearchFallbackManager未加载');
        }

        // 初始化性能优化器
        if (typeof PerformanceOptimizer !== 'undefined') {
            const perfConfig = {
                maxConcurrentRequests: window.CONFIG?.search?.streaming?.ui?.maxConcurrentRequests || 2,
                requestTimeoutMs: 10000,
                prefetchEnabled: true,
                throttleWindowMs: 2000,
                maxRequestsPerWindow: 3
            };
            
            this.performanceOptimizer = new PerformanceOptimizer(perfConfig);
            
            // 注册清理回调
            this.performanceOptimizer.memoryManager.registerCleanupCallback(() => {
                return this.performMemoryCleanup();
            });
            
            console.log('✅ 性能优化器初始化成功');
        } else {
            this.performanceOptimizer = null;
            console.log('⚠️ PerformanceOptimizer未加载');
        }

        // 初始化移动端交互管理器
        if (typeof MobileInteractionManager !== 'undefined') {
            const mobileConfig = {
                hapticFeedback: window.CONFIG?.mobile?.haptic?.enabled ?? true,
                gestureTimeout: window.CONFIG?.mobile?.gestures?.timeout || 300,
                longPressTimeout: window.CONFIG?.mobile?.gestures?.longPress?.timeout || 800,
                swipeThreshold: window.CONFIG?.mobile?.gestures?.swipe?.threshold || 100,
                doubleTapTimeout: window.CONFIG?.mobile?.gestures?.doubleTap?.timeout || 300
            };
            
            this.mobileManager = new MobileInteractionManager(mobileConfig);
            console.log('✅ 移动端交互管理器初始化成功');
        } else {
            this.mobileManager = null;
            console.log('⚠️ MobileInteractionManager未加载');
        }

        // 初始化智能搜索建议管理器
        if (typeof SmartSuggestionManager !== 'undefined') {
            const suggestionConfig = {
                maxSuggestions: window.CONFIG?.search?.suggestions?.maxSuggestions || 8,
                maxHistory: window.CONFIG?.search?.suggestions?.maxHistory || 50,
                minSearchLength: window.CONFIG?.search?.suggestions?.minSearchLength || 2,
                popularityWeight: 0.3,
                contextWeight: 0.4,
                semanticWeight: 0.3
            };
            
            this.suggestionManager = new SmartSuggestionManager(
                this.dataManager, 
                this.i18n, 
                suggestionConfig
            );
            console.log('✅ 智能搜索建议管理器初始化成功');
        } else {
            this.suggestionManager = null;
            console.log('⚠️ SmartSuggestionManager未加载');
        }

        // 初始化移动端优化管理器
        if (typeof MobileOptimizationManager !== 'undefined') {
            const mobileOptConfig = {
                keyboardThreshold: window.CONFIG?.mobile?.virtualKeyboard?.threshold || 150,
                orientationTransition: 300,
                iosKeyboardFix: window.CONFIG?.mobile?.virtualKeyboard?.iosKeyboardFix !== false,
                androidKeyboardFix: window.CONFIG?.mobile?.virtualKeyboard?.androidKeyboardFix !== false,
                safeAreaSupport: window.CONFIG?.mobile?.safeArea?.enabled !== false,
                disableUserZoom: window.CONFIG?.mobile?.performance?.disableUserZoom || false
            };
            
            this.mobileOptimizer = new MobileOptimizationManager(mobileOptConfig);
            console.log('✅ 移动端优化管理器初始化成功');
        } else {
            this.mobileOptimizer = null;
            console.log('⚠️ MobileOptimizationManager未加载');
        }

        // 初始化系统验证器（开发/测试模式）
        if (typeof SystemValidator !== 'undefined') {
            this.systemValidator = new SystemValidator(this);
            
            // 在开发模式下自动添加测试按钮
            if (window.CONFIG?.debug || window.location.search.includes('debug=true')) {
                this.addTestButton();
            }
            
            console.log('✅ 系统验证器初始化成功');
        } else {
            this.systemValidator = null;
            console.log('⚠️ SystemValidator未加载');
        }

        // 初始化主题
        this.initTheme();
        
        // 初始化性能基准测试管理器
        if (typeof PerformanceBenchmarkManager !== 'undefined') {
            this.benchmarkManager = new PerformanceBenchmarkManager(this);
            console.log('✅ 性能基准测试管理器初始化成功');
        } else {
            this.benchmarkManager = null;
            console.log('⚠️ PerformanceBenchmarkManager未加载');
        }

        this.init();
    }

    // 监控RAG向量引擎初始化状态
    async monitorRAGInitialization() {
        if (!this.unifiedSearchEngine) return;

        try {
            // 等待RAG引擎初始化完成
            const maxWaitTime = 10000; // 10秒超时
            const checkInterval = 500; // 每500ms检查一次
            let elapsedTime = 0;

            const checkRAGStatus = () => {
                return new Promise((resolve) => {
                    const interval = setInterval(() => {
                        elapsedTime += checkInterval;
                        
                        if (this.unifiedSearchEngine.ragInitialized) {
                            clearInterval(interval);
                            resolve(true);
                        } else if (elapsedTime >= maxWaitTime) {
                            clearInterval(interval);
                            resolve(false);
                        }
                    }, checkInterval);
                });
            };

            const isInitialized = await checkRAGStatus();
            
            if (isInitialized) {
                console.log('🧠 RAG向量搜索引擎初始化完成');
                
                // 获取RAG引擎统计信息
                const ragStats = this.unifiedSearchEngine.ragEngine?.getStats();
                if (ragStats) {
                    console.log(`📊 RAG统计: ${ragStats.documentsCount} 个文档, ${ragStats.vocabularySize} 个词汇`);
                }
                
                // 启用向量搜索功能
                this.ragEnabled = true;
                
                // 预加载常用查询的向量
                this.preloadCommonVectorQueries();
                
            } else {
                console.warn('⚠️ RAG向量引擎初始化超时，将仅使用传统搜索');
                this.ragEnabled = false;
            }
            
        } catch (error) {
            console.error('RAG引擎监控失败:', error);
            this.ragEnabled = false;
        }
        
        // 🔧 启动内存监控和自动清理
        this.startMemoryMonitoring();
    }

    // 预加载常用查询的向量
    async preloadCommonVectorQueries() {
        if (!this.ragEnabled || !this.unifiedSearchEngine?.ragEngine) return;

        const commonQueries = [
            '登录问题', '支付问题', '接单流程', '提现失败', 
            'login issue', 'payment problem', 'order flow', 'withdrawal failed',
            'masalah log masuk', 'masalah pembayaran', 'aliran pesanan'
        ];

        console.log('🔄 预加载常用向量查询...');
        
        try {
            const preloadPromises = commonQueries.map(async (query, index) => {
                // 错开预加载时间，避免同时请求过多
                await new Promise(resolve => setTimeout(resolve, index * 200));
                
                try {
                    await this.unifiedSearchEngine.ragEngine.semanticSearch(query, {
                        maxResults: 3,
                        similarityThreshold: 0.2
                    });
                } catch (error) {
                    console.debug(`预加载查询 "${query}" 失败:`, error.message);
                }
            });

            await Promise.allSettled(preloadPromises);
            console.log('✅ 向量查询预加载完成');
            
        } catch (error) {
            console.debug('向量查询预加载出错:', error);
        }
    }

    // 设置全局错误处理
    setupGlobalErrorHandling() {
        // 处理未捕获的JavaScript错误
        window.addEventListener('error', (event) => {
            this.logError('Uncaught Error', event.error || event.message, {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
            this.showErrorRecovery('系统遇到意外错误，正在尝试恢复...');
        });

        // 处理未处理的Promise rejection
        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', event.reason, {
                promise: event.promise,
                type: 'promise_rejection'
            });
            this.showErrorRecovery('网络请求失败，正在重试...');
            
            // 防止错误在控制台显示
            event.preventDefault();
        });

        // 处理资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window && event.target.tagName) {
                this.logError('Resource Load Error', `Failed to load ${event.target.tagName}: ${event.target.src || event.target.href}`, {
                    element: event.target.tagName,
                    source: event.target.src || event.target.href
                });
            }
        }, true);

        console.log('✅ 全局错误处理器设置完成');
    }

    // 错误日志记录
    logError(type, message, details = {}) {
        const errorInfo = {
            type,
            message: String(message),
            details,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            appState: {
                currentPage: this.currentPage,
                geminiEnabled: this.geminiEnabled,
                searchEngineAvailable: !!this.unifiedSearchEngine
            }
        };

        console.error(`🚨 ${type}:`, errorInfo);

        // 在生产环境中，可以将错误发送到监控服务
        // this.sendErrorToMonitoring(errorInfo);
    }

    // 显示错误恢复提示
    showErrorRecovery(message) {
        // 避免重复显示恢复提示
        if (this.isShowingRecovery) return;
        this.isShowingRecovery = true;

        const recoveryDiv = document.createElement('div');
        recoveryDiv.className = 'error-recovery-banner';
        recoveryDiv.innerHTML = `
            <div class="recovery-content">
                <span class="recovery-icon">🔄</span>
                <span class="recovery-message">${message}</span>
            </div>
        `;

        // 添加样式
        if (!document.getElementById('error-recovery-styles')) {
            const style = document.createElement('style');
            style.id = 'error-recovery-styles';
            style.textContent = `
                .error-recovery-banner {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    background: #ff6b35;
                    color: white;
                    padding: 10px;
                    text-align: center;
                    z-index: 10000;
                    animation: slideDown 0.3s ease-out;
                }
                .recovery-content {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                }
                .recovery-icon {
                    animation: spin 1s linear infinite;
                }
                @keyframes slideDown {
                    from { transform: translateY(-100%); }
                    to { transform: translateY(0); }
                }
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(recoveryDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (recoveryDiv.parentNode) {
                recoveryDiv.style.animation = 'slideDown 0.3s ease-in reverse';
                setTimeout(() => {
                    recoveryDiv.remove();
                    this.isShowingRecovery = false;
                }, 300);
            }
        }, 3000);
    }

    // 系统状态验证
    validateSystemState() {
        try {
            // 检查关键组件
            if (!this.dataManager) {
                this.logError('System Validation', 'DataManager not initialized');
                return false;
            }

            if (!this.i18n) {
                this.logError('System Validation', 'I18n manager not initialized');
                return false;
            }

            // 检查DOM元素
            const criticalElements = ['searchInput', 'searchResults', 'welcomePage'];
            for (const elementId of criticalElements) {
                if (!document.getElementById(elementId)) {
                    this.logError('System Validation', `Critical element missing: ${elementId}`);
                    return false;
                }
            }

            return true;
        } catch (error) {
            this.logError('System Validation', 'Validation check failed', { error: error.message });
            return false;
        }
    }

    // 显示错误消息
    showErrorMessage(message, duration = 5000) {
        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message-toast';
        errorDiv.textContent = message;

        // 添加样式（如果不存在）
        if (!document.getElementById('error-message-styles')) {
            const style = document.createElement('style');
            style.id = 'error-message-styles';
            style.textContent = `
                .error-message-toast {
                    position: fixed;
                    bottom: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #dc3545;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    z-index: 10001;
                    animation: fadeInUp 0.3s ease-out;
                    max-width: 90%;
                    text-align: center;
                    font-size: 14px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                }
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateX(-50%) translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(errorDiv);

        // 自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.style.animation = 'fadeInUp 0.3s ease-in reverse';
                setTimeout(() => errorDiv.remove(), 300);
            }
        }, duration);
    }
    
    // 初始化应用
    init() {
        try {
            console.log('🚀 开始初始化FAQ应用...');

            this.setupEventListeners();
            console.log('✅ 事件监听器设置完成');

            // 不在初始化时渲染分类，而是在用户点击时动态创建
            // this.renderCategories();
            // console.log('✅ 分类导航渲染完成');

            this.renderQuickStartButtons();
            console.log('✅ 快速开始按钮渲染完成');

            this.showWelcomePage();
            console.log('✅ 欢迎页面显示完成');

            this.setupBackToTop();
            console.log('✅ 返回顶部按钮设置完成');

            this.i18n.updatePageTexts();
            console.log('✅ 国际化文本更新完成');

            // 处理URL参数
            this.handleUrlParams();
            console.log('✅ URL参数处理完成');

            console.log('🎉 FAQ应用初始化成功！');

            // 隐藏加载提示
            this.hideLoadingIndicator();
            
            // 启动性能监控
            this.startPerformanceMonitoring();
            
            // 预加载常用搜索 (性能优化)
            setTimeout(() => {
                this.preloadCommonSearches();
            }, 2000); // 延迟2秒执行，避免影响初始化性能

        } catch (error) {
            console.error('❌ FAQ应用初始化失败:', error);
            console.error('错误堆栈:', error.stack);

            // 显示用户友好的错误信息
            this.showErrorMessage('应用初始化失败，请刷新页面重试。');
        }
    }

    // 显示错误信息
    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            z-index: 10000;
            max-width: 400px;
            text-align: center;
            font-family: Arial, sans-serif;
        `;
        errorDiv.innerHTML = `
            <h3>⚠️ 系统错误</h3>
            <p>${message}</p>
            <button onclick="location.reload()" style="
                background: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
            ">刷新页面</button>
        `;
        document.body.appendChild(errorDiv);
    }
    
    // 设置事件监听器 - 移动端优化版本
    setupEventListeners() {
        // 移动端底部导航
        this.setupBottomNavigation();
        
        // 浮动对话按钮 (FAB) - 移动端专用
        this.setupFABButtons();

        // 触摸反馈 - 移动端优化
        this.setupTouchFeedback();

        // 搜索功能 - 移动端优化
        const searchInput = document.getElementById('searchInput');
        const geminiToggle = document.getElementById('geminiToggle');
        
        // 实时搜索 - 输入时触发（移动端优化）
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value;
            this.performRealtimeSearch(query);
            
            // 生成智能搜索建议
            if (this.suggestionManager && window.CONFIG?.search?.suggestions?.enabled !== false) {
                this.showSmartSuggestions(query);
            }
        });
        
        // 🔧 增强键盘导航支持
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = null;
                }
                this.performSearch(e.target.value);
            } else if (e.key === 'Escape') {
                // ESC键清空搜索并隐藏结果
                e.target.value = '';
                this.hideSearchResults();
                this.hideStreamingContainer();
                e.target.blur();
            } else if (e.key === 'ArrowDown') {
                // 下箭头键导航到第一个搜索结果
                e.preventDefault();
                this.navigateToFirstResult();
            } else if (e.key === 'ArrowUp') {
                // 上箭头键导航到建议列表
                e.preventDefault();
                this.navigateToSuggestions();
            }
        });
        
        // Gemini状态按钮
        geminiToggle.addEventListener('click', (e) => {
            e.preventDefault();
            this.showGeminiStatus();
        });

        // 集成移动端交互管理器
        if (this.mobileManager) {
            this.setupMobileInteractions();
        }

        // 集成移动端优化管理器
        if (this.mobileOptimizer) {
            this.setupMobileOptimizations();
        }



        // 主题切换按钮
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // 语言切换按钮
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = btn.getAttribute('data-lang');
                this.switchLanguage(lang);
            });
        });
    }
    
    // 初始化主题
    initTheme() {
        // 应用保存的主题
        this.applyTheme(this.currentTheme);

        // 更新主题切换按钮图标
        this.updateThemeToggleIcon();
    }

    // 切换主题
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.currentTheme);
        this.applyTheme(this.currentTheme);
        this.updateThemeToggleIcon();

        // 添加切换动画效果
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    // 应用主题
    applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark-theme');
        } else {
            document.documentElement.classList.remove('dark-theme');
        }
    }

    // 更新主题切换按钮图标
    updateThemeToggleIcon() {
        const themeIcon = document.querySelector('.theme-icon');
        if (themeIcon) {
            themeIcon.textContent = this.currentTheme === 'light' ? '🌙' : '☀️';
        }

        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.title = this.currentTheme === 'light' ?
                this.i18n.t('switchToDarkTheme') :
                this.i18n.t('switchToLightTheme');
        }
    }

    // 切换语言
    switchLanguage(lang) {
        // 更新按钮状态
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-lang="${lang}"]`).classList.add('active');

        // 设置语言
        this.i18n.setLanguage(lang);

        // 重新渲染当前页面
        this.refreshCurrentPage();
        this.renderQuickStartButtons(); // 重新渲染快速访问按钮以更新语言
    }
    
    // 实时搜索（防抖） - 支持流式搜索
    performRealtimeSearch(query) {
        // 清除之前的定时器
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        // 如果查询为空，显示欢迎页面
        if (!query || query.trim().length === 0) {
            this.showWelcomePage();
            this.hideStreamingContainer();
            return;
        }
        
        // 🔧 智能防抖策略 - 基于设备和网络条件优化
        const getAdaptiveDebounceMs = () => {
            const baseDebounce = window.CONFIG?.search?.streaming?.debounceMs || 500;
            const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            
            // 移动端更快响应
            if (isMobile) {
                if (connection?.effectiveType === '2g') return 800;
                if (connection?.effectiveType === '3g') return 600;
                return 400; // 4G/5G移动端
            }
            
            // 桌面端基于网络优化
            if (connection?.effectiveType === 'slow-2g') return 1000;
            if (connection?.effectiveType === '2g') return 700;
            return baseDebounce; // 桌面端默认
        };
        
        const debounceMs = getAdaptiveDebounceMs();
        
        // 设置防抖延迟
        this.searchTimeout = setTimeout(() => {
            const trimmedQuery = query.trim();
            
            // 检查是否启用流式搜索
            if (this.streamingEnabled && this.streamingEngine) {
                this.performStreamingSearch(trimmedQuery);
            } else {
                // 回退到传统搜索
                this.performSearch(trimmedQuery);
            }
        }, debounceMs);
    }
    
    // 新增：流式搜索方法（集成降级策略）
    async performStreamingSearch(query) {
        const language = this.i18n.getCurrentLanguage();
        
        console.log(`🚀 开始流式搜索: "${query}" (语言: ${language})`);
        
        // 显示流式搜索容器
        this.showStreamingContainer();
        
        // 如果有降级管理器，使用降级策略
        if (this.fallbackManager) {
            try {
                await this.fallbackManager.performSearchWithFallback(
                    query,
                    language,
                    // 进度回调 - 处理各个阶段的更新
                    (progress) => {
                        this.handleFallbackProgress(query, progress);
                    },
                    // 完成回调 - 处理最终结果
                    (finalResult) => {
                        this.handleFallbackComplete(query, finalResult);
                    }
                );
            } catch (error) {
                console.error('所有搜索策略都失败:', error);
                this.showNoStreamingResults(query);
            }
        } else {
            // 回退到单一流式搜索
            try {
                await this.streamingEngine.performStreamingAnalysis(
                    query,
                    language,
                    // 进度回调 - 处理各个阶段的更新
                    (progress) => {
                        this.handleStreamingProgress(query, progress);
                    },
                    // 完成回调 - 处理最终结果
                    (finalResult) => {
                        this.handleStreamingComplete(query, finalResult);
                    }
                );
            } catch (error) {
                console.error('流式搜索失败:', error);
                // 最后的降级 - 传统搜索
                console.log('⬇️ 最终降级到传统搜索模式');
                this.performSearch(query, false);
            }
        }
    }
    
    // 处理流式搜索进度更新
    handleStreamingProgress(query, progress) {
        console.debug(`📊 流式搜索进度 - 阶段: ${progress.stage}`);
        
        switch (progress.stage) {
            case 'basic':
                this.updateSearchStatus('🔍 搜索基础结果...', 'searching');
                if (progress.results && progress.results.length > 0) {
                    this.showPartialResults(query, progress.results, 'basic');
                }
                break;
                
            case 'aiAnalysis':
                this.updateSearchStatus('🧠 AI意图分析中...', 'ai-processing');
                if (progress.intent) {
                    this.showIntentAnalysis(progress.intent, progress.confidence);
                }
                break;
                
            case 'suggestions':
                this.updateSearchStatus('💡 生成智能建议...', 'generating');
                if (progress.suggestions && progress.suggestions.length > 0) {
                    this.showSearchSuggestions(progress.suggestions);
                }
                break;
                
            default:
                this.updateSearchStatus('⚡ 优化结果中...', 'optimizing');
                break;
        }
    }
    
    // 处理流式搜索完成
    handleStreamingComplete(query, finalResult) {
        console.log('✅ 流式搜索完成:', finalResult);
        
        this.updateSearchStatus('⚡ 完成', 'complete');
        
        if (finalResult && finalResult.results && finalResult.results.length > 0) {
            // 显示最终优化的搜索结果
            this.showStreamingSearchResults(query, finalResult);
        } else {
            // 无结果处理
            this.showNoStreamingResults(query);
        }
    }
    
    // 处理降级策略进度更新
    handleFallbackProgress(query, progress) {
        console.debug(`📊 降级策略进度 - 策略: ${progress.strategy}, 阶段: ${progress.stage}`);
        
        const strategyNames = {
            'streaming-ai': '🚀 流式AI搜索',
            'standard-ai': '✨ 标准AI搜索',
            'fuzzy-match': '🔍 模糊匹配搜索',
            'basic-search': '📋 基础搜索'
        };
        
        if (progress.stage === 'trying') {
            const strategyName = strategyNames[progress.strategy] || progress.strategy;
            const message = `${strategyName} (${progress.attempt}/${progress.total})`;
            
            this.updateSearchStatus(message, this.getStrategyStatusType(progress.strategy));
        } else if (progress.stage && progress.data) {
            // 转发到原有的流式搜索进度处理
            this.handleStreamingProgress(query, progress.data);
        }
    }
    
    // 处理降级策略完成
    handleFallbackComplete(query, finalResult) {
        console.log('✅ 降级搜索完成:', finalResult);
        
        if (finalResult.error) {
            console.error('所有搜索策略失败:', finalResult.error);
            this.updateSearchStatus('❌ 搜索失败', 'error');
            this.showNoStreamingResults(query);
            return;
        }
        
        // 根据使用的策略显示不同的状态
        const strategyIcons = {
            'streaming-ai': '🚀',
            'standard-ai': '✨', 
            'fuzzy-match': '🔍',
            'basic-search': '📋'
        };
        
        const icon = strategyIcons[finalResult.strategy] || '⚡';
        const level = finalResult.fallbackLevel;
        let statusMessage = `${icon} 搜索完成`;
        
        if (level > 0) {
            statusMessage += ` (降级策略 ${level + 1})`;
        }
        
        this.updateSearchStatus(statusMessage, 'complete');
        
        // 显示结果
        if (finalResult.results && finalResult.results.length > 0) {
            // 添加降级策略信息
            finalResult.fallbackInfo = {
                strategy: finalResult.strategy,
                level: finalResult.fallbackLevel,
                totalAttempts: finalResult.totalAttempts
            };
            
            this.showStreamingSearchResults(query, finalResult);
        } else {
            this.showNoStreamingResults(query);
        }
    }
    
    // 获取策略状态类型
    getStrategyStatusType(strategy) {
        const statusTypes = {
            'streaming-ai': 'ai-processing',
            'standard-ai': 'ai-processing',
            'fuzzy-match': 'searching',
            'basic-search': 'searching'
        };
        
        return statusTypes[strategy] || 'searching';
    }
    
    // 执行搜索 - 重构版本，使用统一搜索引擎 (增强错误处理版)
    async performSearch(query = null, showLoading = false) {
        // 获取语言设置，带验证 - 移到方法开始处确保全局可访问
        const language = this.i18n?.getCurrentLanguage?.() || 'zh';
        
        try {
            // 输入验证和安全检查
            if (!query) {
                const searchInput = document.getElementById('searchInput');
                if (!searchInput) {
                    this.logError('Search Error', 'Search input element not found');
                    return;
                }
                query = searchInput.value.trim();
            }
            
            // 安全验证用户输入
            try {
                query = this.securityValidator.validateSearchInput(query);
                console.log('🔒 搜索输入已通过安全验证');
            } catch (error) {
                console.warn('🚨 搜索输入安全验证失败:', error.message);
                this.showNotification(`⚠️ ${error.message}`, 3000);
                return;
            }

            // 查询验证
            if (!query) {
                this.showWelcomePage();
                return;
            }
            
            // 系统状态验证
            if (!this.validateSystemState()) {
                this.showErrorMessage('系统初始化中，请稍后重试');
                return;
            }

            console.debug('开始搜索:', { query, language, showLoading });
            
        } catch (validationError) {
            // 处理输入验证和系统状态检查错误
            this.logError('Search Validation Error', validationError.message || validationError, {
                query,
                hasSystemState: !!this.validateSystemState,
                hasDataManager: !!this.dataManager,
                hasI18n: !!this.i18n
            });
            
            this.showErrorMessage('搜索初始化失败，请刷新页面重试');
            return;
        }
        
        // 首次使用时测试Gemini API连接
        if (this.geminiEnabled && this.geminiAssistant && !this.geminiAssistant.connectionTested) {
            try {
                console.debug('首次搜索，测试Gemini API连接...');
                const connectionSuccess = await this.geminiAssistant.testAPIConnection();
                this.geminiAssistant.connectionTested = true;

                if (connectionSuccess) {
                    console.log('✅ Gemini API 连接成功');
                } else {
                    console.warn('⚠️ Gemini API 连接失败，将使用传统搜索');
                    this.geminiEnabled = false;
                }
            } catch (error) {
                console.warn('⚠️ Gemini API 测试出错:', error);
                this.geminiEnabled = false;
                this.geminiAssistant.connectionTested = true;
            }
        }

        // 优先使用统一搜索引擎 (新架构)
        if (this.unifiedSearchEngine && this.searchUIRenderer) {
            console.log('🚀 使用统一搜索引擎进行搜索:', query);
            console.log('📊 搜索引擎状态:', {
                unifiedEngine: !!this.unifiedSearchEngine,
                uiRenderer: !!this.searchUIRenderer,
                language: language
            });

            try {
                // 清除之前的搜索结果
                this.showSearchPage();
                console.log('✅ 已切换到搜索页面');

                // 使用新的搜索架构
                console.log('🔍 开始执行统一搜索...');
                await this.unifiedSearchEngine.search(query, language, {
                    onProgress: (data) => {
                        console.log('📈 搜索进度更新:', data.stage, '结果数量:', data.results?.length || 0);
                        this.handleSearchProgress(data);
                    },
                    onComplete: (results) => {
                        console.log('✅ 搜索完成，结果数量:', results?.length || 0);
                        this.handleSearchComplete(query, results);
                    },
                    onError: (error) => {
                        console.error('❌ 搜索出错:', error);
                        this.handleSearchError(error);
                    }
                });

                return; // 新架构处理完成，直接返回

            } catch (error) {
                console.warn('统一搜索引擎出错，回退到传统搜索:', error);
                // 继续执行传统搜索逻辑
            }
        }
        
        // 传统搜索逻辑 (兼容模式)
        console.debug('使用传统搜索架构:', query);
        
        // 只在明确指定时显示加载指示器
        if (showLoading) {
            this.showLoadingOverlay(true);
            this.showStreamingIndicator(true);
        }
        
        try {
            let results;
            
            // 尝试使用 Gemini 增强搜索（静默模式，不显示错误）
            if (this.geminiEnabled && this.geminiAssistant && this.geminiAssistant.isAvailable()) {
                try {
                    const enhanced = await this.geminiAssistant.enhanceSearchQuery(query, language);

                    if (enhanced.enhanced) {
                        // 使用增强的查询进行搜索
                        results = await this.dataManager.searchQuestions(enhanced.enhancedQuery, language);

                        // 如果增强搜索结果不够好，回退到原始查询
                        if (results.length < 2) {
                            results = await this.dataManager.searchQuestions(query, language);
                        }

                        // 显示搜索增强信息
                        if (enhanced.suggestions && enhanced.suggestions.length > 0) {
                            this.showSearchEnhancement(enhanced);
                        }
                    } else {
                        results = await this.dataManager.searchQuestions(query, language);
                    }
                } catch (error) {
                    // 静默处理 Gemini 错误，不显示警告
                    results = await this.dataManager.searchQuestions(query, language);
                }
            } else {
                results = await this.dataManager.searchQuestions(query, language);
            }
            
            this.showSearchResults(query, results);
        } catch (error) {
            // 增强的错误处理
            this.logError('Search Failed', error.message || error, {
                query,
                language,
                showLoading,
                hasUnifiedEngine: !!this.unifiedSearchEngine,
                hasGeminiAssistant: !!this.geminiAssistant,
                stack: error.stack
            });

            // 根据错误类型提供不同的用户反馈
            let userMessage = '搜索遇到问题，请重试';
            
            if (error.message?.includes('timeout') || error.message?.includes('网络')) {
                userMessage = '网络连接超时，请检查网络后重试';
            } else if (error.message?.includes('API') || error.message?.includes('Gemini')) {
                userMessage = '智能搜索暂时不可用，已切换到基础搜索';
            } else if (error.message?.includes('数据') || error.message?.includes('Data')) {
                userMessage = '数据加载失败，正在重试...';
            }

            this.showErrorMessage(userMessage);

            // 尝试基础搜索作为最后的fallback
            if (this.dataManager && this.dataManager.searchQuestions) {
                try {
                    console.log('🔄 尝试基础搜索作为fallback');
                    const fallbackResults = await this.dataManager.searchQuestions(query, language);
                    if (fallbackResults && fallbackResults.length > 0) {
                        this.showSearchResults(query, fallbackResults);
                        this.showErrorRecovery('已切换到基础搜索模式');
                        return;
                    }
                } catch (fallbackError) {
                    this.logError('Fallback Search Failed', fallbackError.message);
                }
            }

            // 如果所有搜索都失败，显示无结果页面
            this.showNoResultsPage(query);
        } finally {
            if (showLoading) {
                this.showLoadingOverlay(false);
                this.showStreamingIndicator(false);
            }
        }
    }

    // 处理搜索进度 - 新架构的回调方法
    handleSearchProgress(data) {
        console.debug('搜索进度更新:', data.stage, '结果数量:', data.results?.length || 0);
        
        // 更新UI渲染器
        if (this.searchUIRenderer) {
            this.searchUIRenderer.updateResults({
                ...data,
                query: document.getElementById('searchInput').value.trim()
            });
        }
        
        // 更新搜索状态显示
        this.updateSearchStatus(data.stage, data.confidence);
    }

    // 处理搜索完成 - 新架构的回调方法
    handleSearchComplete(query, results) {
        console.debug('搜索完成:', query, '最终结果数量:', results?.length || 0);
        
        // 🔧 修复：渲染搜索结果到UI
        if (this.searchUIRenderer && results !== undefined) {
            this.searchUIRenderer.updateResults({
                stage: 'complete',
                results: results,
                confidence: 1.0,
                timestamp: Date.now(),
                query: query
            });
            console.debug('✅ 搜索结果已通过UIRenderer显示');
        } else {
            // 降级：使用传统的showSearchResults方法
            this.showSearchResults(query, results || []);
            console.debug('✅ 搜索结果已通过传统方法显示');
        }
        
        // 更新URL参数 - 使用's'参数存储搜索查询
        this.updateURLParam('s', query);
        
        // 记录搜索历史
        this.addToSearchHistory(query, results?.length || 0);
        
        // 显示搜索统计
        this.showSearchStats(query, results);
        
        // 预加载相关内容
        if (results && results.length > 0) {
            this.preloadRelatedContent(results[0]);
        }
    }

    // 处理搜索错误 - 新架构的回调方法
    handleSearchError(error) {
        console.error('❌ 搜索过程出错:', error);
        
        // 🔧 修复：通过UI渲染器显示错误状态
        if (this.searchUIRenderer) {
            this.searchUIRenderer.updateResults({
                stage: 'error',
                results: [],
                error: error.message || '搜索出现问题',
                timestamp: Date.now()
            });
            console.debug('✅ 错误状态已通过UIRenderer显示');
        } else {
            // 降级：显示传统错误页面
            this.showNoResultsPage('搜索出现问题，请重试');
        }
        
        // 显示用户友好的错误信息
        this.showNotification(this.i18n.t('searchError', '搜索出现问题，请重试'), 'error');
    }

    // 更新搜索状态
    updateSearchStatus(stage, confidence = 0.8) {
        const statusMessages = {
            'basic': this.i18n.t('searchingBasic', '正在快速搜索...'),
            'fuzzy': this.i18n.t('searchingFuzzy', '正在扩展搜索...'),
            'ai-enhanced': this.i18n.t('aiEnhancing', 'AI正在优化结果...'),
            'background-enhanced': this.i18n.t('backgroundAI', '后台AI优化完成'),
            'cached': this.i18n.t('cachedResults', '显示缓存结果'),
            'complete': this.i18n.t('searchComplete', '搜索完成')
        };

        const statusText = statusMessages[stage] || this.i18n.t('searching', '搜索中...');
        
        // 更新状态栏（如果存在）
        const statusBar = document.querySelector('.search-status');
        if (statusBar) {
            statusBar.textContent = `${statusText} (${Math.round(confidence * 100)}%)`;
        }
    }

    // 显示搜索统计
    showSearchStats(query, results) {
        if (!results || !this.searchUIRenderer) return;
        
        const stats = {
            total: results.length,
            exact: results.filter(r => r.matchType === 'exact').length,
            fuzzy: results.filter(r => r.matchType === 'fuzzy').length,
            aiEnhanced: results.filter(r => r.aiEnhanced).length
        };
        
        console.debug('搜索统计:', stats);
        
        // 在UI中显示统计信息
        const searchHeader = document.querySelector('.search-results-header');
        if (searchHeader) {
            const statsText = this.i18n.t('searchStats', 
                `找到 ${stats.total} 个结果${stats.aiEnhanced > 0 ? `，其中 ${stats.aiEnhanced} 个AI增强` : ''}`
            );
            
            const existingStats = searchHeader.querySelector('.search-stats');
            if (existingStats) {
                existingStats.textContent = statsText;
            } else {
                const statsElement = document.createElement('div');
                statsElement.className = 'search-stats';
                statsElement.textContent = statsText;
                searchHeader.appendChild(statsElement);
            }
        }
    }

    // 预加载相关内容
    preloadRelatedContent(topResult) {
        if (!topResult || !topResult.id) return;
        
        // 预加载相关问题
        const relatedQuestions = this.dataManager.getRelatedQuestions(topResult.id, 3);
        if (relatedQuestions && relatedQuestions.length > 0) {
            console.debug('预加载相关问题:', relatedQuestions.length, '个');
        }
    }

    // 搜索历史管理
    addToSearchHistory(query, resultCount) {
        try {
            const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            const entry = {
                query,
                resultCount,
                timestamp: Date.now(),
                language: this.i18n.getCurrentLanguage()
            };
            
            // 去重
            const filtered = history.filter(h => h.query !== query);
            filtered.unshift(entry);
            
            // 保持最近20条
            const trimmed = filtered.slice(0, 20);
            localStorage.setItem('searchHistory', JSON.stringify(trimmed));
            
        } catch (error) {
            console.warn('保存搜索历史失败:', error);
        }
    }

    // 重试搜索方法 (供错误处理使用)
    retrySearch() {
        const lastQuery = document.getElementById('searchInput')?.value?.trim();
        if (lastQuery) {
            console.log('重试搜索:', lastQuery);
            this.performSearch(lastQuery);
        }
    }

    // 预加载常用搜索 - 性能优化
    preloadCommonSearches() {
        if (!this.unifiedSearchEngine) return;
        
        const commonQueries = [
            '如何接单', '提现', '账号', '车辆', '客服',
            'how to receive orders', 'withdrawal', 'account', 'vehicle', 'customer service',
            'cara menerima pesanan', 'pengeluaran', 'akaun', 'kenderaan', 'khidmat pelanggan'
        ];
        
        console.log('开始预加载常用搜索...');
        this.unifiedSearchEngine.preloadCommonQueries(commonQueries);
    }
    
    // 显示搜索结果
    showSearchResults(query, results) {
        this.currentPage = 'search';
        this.hideAllPages();

        const searchPage = document.getElementById('searchPage');
        const searchResults = document.getElementById('searchResults');

        // 渲染搜索结果，包含搜索信息
        const count = results.length;

        if (results.length === 0) {
            searchResults.innerHTML = `
                <div class="search-header">
                    <h2>${this.i18n.t('searchResultsTitle')}</h2>
                    <p>${this.i18n.t('searchFor')} "${query}" ${this.i18n.t('foundResults', { count })}</p>
                </div>
                <div class="no-results">
                    <h3>${this.i18n.t('noResultsFound')}</h3>
                    <p>${this.i18n.t('noResultsDesc')}</p>
                    <button onclick="app.showWelcomePage()" class="back-to-home-btn">${this.i18n.t('backToHomeBtn')}</button>
                </div>
            `;
        } else {
            searchResults.innerHTML = `
                <div class="search-header">
                    <h2>${this.i18n.t('searchResultsTitle')}</h2>
                    <p>${this.i18n.t('searchFor')} "${query}" ${this.i18n.t('foundResults', { count })}</p>
                </div>
                <div class="search-results-list">
                    ${results.map(result => this.renderSearchResultItem(result)).join('')}
                </div>
            `;

            // 添加点击事件
            searchResults.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', () => {
                    const questionId = item.dataset.questionId;
                    this.showQuestion(questionId);
                });
            });
        }

        // 记录传统搜索行为
        this.recordSearchBehavior(query, results, {
            strategy: 'traditional',
            hasAIEnhancement: false,
            suggestionCount: 0,
            searchTime: 0
        });

        searchPage.classList.remove('page-hidden');
    }
    
    // 渲染搜索结果项
    renderSearchResultItem(question) {
        // 直接使用question对象，因为searchQuestions返回的是问题数组，不是result对象
        const lang = this.i18n.getCurrentLanguage();
        const categories = this.dataManager.getCategories();
        const category = categories[question.category];

        // 安全获取分类名称
        let categoryName = this.i18n.t('unknownCategory');
        if (category) {
            categoryName = category.name[lang];
        } else {
            // 尝试使用CategoryAdapter获取分类信息
            const categoryInfo = this.dataManager.getCategoryInfo ?
                this.dataManager.getCategoryInfo(question.category, lang) : null;
            if (categoryInfo) {
                categoryName = categoryInfo.name;
            }
        }

        // 生成摘要
        const excerpt = this.generateExcerpt(question.content[lang], 150);

        return `
            <div class="search-result-item" data-question-id="${question.id}">
                <div class="search-result-header">
                    <span class="search-result-id">${question.id}</span>
                    <h3 class="search-result-title">${question.title[lang]}</h3>
                    <span class="search-result-category">${categoryName}</span>
                </div>
                <div class="search-result-excerpt">${excerpt}</div>
            </div>
        `;
    }
    
    // 生成内容摘要
    generateExcerpt(content, maxLength) {
        const text = content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    // 渲染分类导航
    renderCategories() {
        console.log(' 🎨 开始渲染分类按钮...');
        
        // 检查是否存在 categories-container，如果不存在则动态创建
        let categoriesContainer = document.getElementById('categories-container');
        
        if (!categoriesContainer) {
            console.warn('⚠️ categories-container 不存在，动态创建中...');
            
            // 创建分类页面容器
            let categoriesPage = document.getElementById('categoriesPage');
            if (!categoriesPage) {
                categoriesPage = document.createElement('div');
                categoriesPage.id = 'categoriesPage';
                categoriesPage.className = 'categories-page page-hidden';
                
                // 添加分类页面标题
                const pageTitle = document.createElement('div');
                pageTitle.className = 'page-title';
                pageTitle.innerHTML = '<h2>选择分类</h2>';
                
                // 创建分类容器
                categoriesContainer = document.createElement('div');
                categoriesContainer.id = 'categories-container';
                categoriesContainer.className = 'categories-container';
                
                // 组装分类页面
                categoriesPage.appendChild(pageTitle);
                categoriesPage.appendChild(categoriesContainer);
                
                // 插入到主内容区域
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.appendChild(categoriesPage);
                    console.log('✅ 动态创建了分类页面和容器');
                } else {
                    console.error('❌ 无法找到main-content容器');
                    return;
                }
            } else {
                // 分类页面存在但容器不存在
                categoriesContainer = document.createElement('div');
                categoriesContainer.id = 'categories-container';
                categoriesContainer.className = 'categories-container';
                categoriesPage.appendChild(categoriesContainer);
            }
        }
        
        const categories = this.dataManager.getCategories();
        const lang = this.i18n.getCurrentLanguage();
        
        // 清空容器内容
        categoriesContainer.innerHTML = '';
        
        // 渲染分类按钮
        const categoryItems = Object.values(categories)
            .sort((a, b) => a.priority - b.priority)
            .map(category => {
                const questionCount = this.dataManager.getQuestionsByCategory(category.id).length;
                return `
                    <button class="category-btn" data-category="${category.id}">
                        <span class="category-icon">${category.icon}</span>
                        <span class="category-name">${category.name[lang]}</span>
                        <span class="category-count">${questionCount}</span>
                    </button>
                `;
            }).join('');
        
        categoriesContainer.innerHTML = categoryItems;
        
        // 添加点击事件
        categoriesContainer.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const categoryId = e.currentTarget.dataset.category;
                this.showCategoryQuestions(categoryId);
            });
        });
        
        console.log(`✅ 渲染了 ${Object.keys(categories).length} 个分类按钮`);
    }
    
    // 动态生成快速开始按钮
    renderQuickStartButtons() {
        // 使用faqCards容器而不是不存在的quick-start-buttons-container
        const container = document.getElementById('faqCards');
        if (!container) {
            console.warn('⚠️ faqCards容器不存在');
            return;
        }

        const categories = this.dataManager.getCategories();
        const quickStartCategories = Object.values(categories)
            .sort((a, b) => a.priority - b.priority);

        // 创建欢迎页面内容
        const lang = this.i18n.getCurrentLanguage();
        container.innerHTML = `
            <div class="welcome-content">
                <div class="welcome-header">
                    <h2>${this.i18n.t('welcomeFAQTitle')}</h2>
                    <p>${this.i18n.t('welcomeFAQDesc')}</p>
                </div>
                <div class="category-grid">
                    ${quickStartCategories.map(category => `
                        <button class="category-card" data-category="${category.id}" type="button">
                            <span class="category-icon">${category.icon}</span>
                            <span class="category-name" data-i18n-category-name="${category.id}">${category.name[lang]}</span>
                            <span class="category-count">${this.dataManager.getQuestionsByCategory(category.id).length} ${this.i18n.t('questionsCount')}</span>
                        </button>
                    `).join('')}
                </div>
            </div>
        `;

        // 为新生成的按钮添加事件监听器
        container.querySelectorAll('.category-card').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const categoryId = e.currentTarget.dataset.category;
                this.showCategoryPage(categoryId);
            });
        });

        console.log(`✅ 渲染了 ${quickStartCategories.length} 个分类卡片到欢迎页面`);
    }
    
    // 显示分类页面 - 重定向到showCategoryQuestions方法
    showCategoryPage(categoryId) {
        console.log(`🔍 重定向到分类问题页面: ${categoryId}`);
        this.showCategoryQuestions(categoryId);
    }
    
    // 渲染问题项
    renderQuestionItem(question) {
        const lang = this.i18n.getCurrentLanguage();
        const summary = this.generateExcerpt(question.content[lang], 100);
        
        return `
            <div class="question-item" data-question-id="${question.id}">
                <div class="question-header">
                    <span class="question-id">${question.id}</span>
                    <h3 class="question-title">${question.title[lang]}</h3>
                    <span class="question-priority priority-${question.priority}">
                        ${this.i18n.t('priority' + question.priority.charAt(0).toUpperCase() + question.priority.slice(1))}
                    </span>
                </div>
                <div class="question-summary">${summary}</div>
            </div>
        `;
    }
    
    // 显示问题详情
    showQuestion(questionId) {
        // 自动滚动到页面顶部
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });

        const question = this.dataManager.getQuestionById(questionId);
        if (!question) {
            console.warn(`⚠️ 问题不存在: ${questionId}`);
            return;
        }

        this.currentQuestion = question;
        this.currentPage = 'faq';
        this.hideAllPages();

        const faqPage = document.getElementById('faqPage');
        const faqContent = document.getElementById('faqContent');

        const lang = this.i18n.getCurrentLanguage();
        const categories = this.dataManager.getCategories();
        const category = categories[question.category];

        // 安全检查：如果分类不存在，尝试获取分类信息
        let categoryName = '未知分类';
        let categoryId = question.category;

        if (category) {
            categoryName = category.name[lang];
        } else {
            // 尝试使用CategoryAdapter获取分类信息
            const categoryInfo = this.dataManager.getCategoryInfo ?
                this.dataManager.getCategoryInfo(question.category, lang) : null;
            if (categoryInfo) {
                categoryName = categoryInfo.name;
                categoryId = categoryInfo.id;
            }
        }

        // 获取相关问题
        const relatedQuestions = this.dataManager.getRelatedQuestions(question.id);

        // 更新FAQ内容，包含面包屑和相关问题
        faqContent.innerHTML = `
            <div class="breadcrumb">
                <button onclick="app.showWelcomePage()" class="breadcrumb-link">${this.i18n.t('backToHome')}</button>
                <span class="breadcrumb-separator">></span>
                <button onclick="app.showCategoryPage('${categoryId}')" class="breadcrumb-link">${categoryName}</button>
                <span class="breadcrumb-separator">></span>
                <span class="breadcrumb-current">${question.title[lang]}</span>
            </div>
            <div class="faq-header">
                <span class="faq-id">${question.id}</span>
                <h1 class="faq-title">
                    ${question.title[lang]}
                    <span class="faq-priority priority-${question.priority}">
                        ${question.priority}
                    </span>
                </h1>
            </div>
            <div class="faq-body">
                ${question.content[lang]}
            </div>
            ${relatedQuestions.length > 0 ? `
                <div class="related-questions">
                    <h3>${this.i18n.t('relatedQuestionsTitle')}</h3>
                    <div class="related-list">
                        ${relatedQuestions.map(rq => `
                            <div class="related-item" onclick="app.showQuestion('${rq.id}')">
                                <span class="related-title">${rq.title[lang]}</span>
                                <span class="related-arrow">→</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        `;

        faqPage.classList.remove('page-hidden');
    }
    
    // 渲染相关问题
    renderRelatedQuestions(container, question) {
        const relatedQuestions = this.dataManager.getRelatedQuestions(question.id);
        const lang = this.i18n.getCurrentLanguage();
        
        if (relatedQuestions.length === 0) {
            container.classList.add('page-hidden');
            return;
        }
        
        container.innerHTML = `
            <h3>${this.i18n.t('relatedQuestions')}</h3>
            <div class="related-list">
                ${relatedQuestions.map(q => `
                    <a href="#" class="related-item" data-question-id="${q.id}">
                        <span class="related-item-id">${q.id}</span>
                        <span class="related-item-title">${q.title[lang]}</span>
                    </a>
                `).join('')}
            </div>
        `;
        
        // 添加点击事件
        container.querySelectorAll('.related-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const questionId = e.currentTarget.dataset.questionId;
                this.showQuestion(questionId);
            });
        });
        
        container.classList.remove('page-hidden');
    }
    
    // 显示搜索页面
    showSearchPage() {
        this.currentPage = 'search';
        this.hideAllPages();
        const searchPage = document.getElementById('searchPage');
        if (searchPage) {
            searchPage.classList.remove('page-hidden');
        }
    }

    // 显示欢迎页面
    showWelcomePage() {
        this.currentPage = 'welcome';
        this.hideAllPages();
        document.getElementById('welcomePage').classList.remove('page-hidden');
    }

    // 隐藏所有页面
    hideAllPages() {
        const pages = ['welcomePage', 'faqPage', 'searchPage', 'categoryPage', 'categoriesPage', 'categoryQuestionsPage'];
        pages.forEach(pageId => {
            const page = document.getElementById(pageId);
            if (page) {
                page.classList.add('page-hidden');
            }
        });
        
        // 隐藏聊天容器
        const chatContainer = document.getElementById('chatContainer');
        if (chatContainer) {
            chatContainer.remove();
        }
    }
    
    // 显示分类页面
    showCategoriesPage() {
        this.currentPage = 'categories';
        this.hideAllPages();
        
        // 确保分类已渲染
        if (!document.getElementById('categories-container')) {
            this.renderCategories();
        }
        
        const categoriesPage = document.getElementById('categoriesPage');
        if (categoriesPage) {
            categoriesPage.classList.remove('page-hidden');
            console.log('📂 显示分类页面');
        } else {
            console.error('❌ 分类页面不存在');
        }
    }
    
    // 显示分类问题页面
    showCategoryQuestions(categoryId) {
        console.log(`🔍 显示分类 ${categoryId} 的问题`);
        
        this.currentPage = 'categoryQuestions';
        this.hideAllPages();
        
        const questions = this.dataManager.getQuestionsByCategory(categoryId);
        const categories = this.dataManager.getCategories();
        const category = categories[categoryId];
        const lang = this.i18n.getCurrentLanguage();
        
        // 安全获取分类信息
        let categoryName = this.i18n.t('unknownCategory');
        let categoryIcon = '📋';

        if (category) {
            categoryName = category.name[lang];
            categoryIcon = category.icon || '📋';
        }
        
        // 创建分类问题页面
        let categoryQuestionsPage = document.getElementById('categoryQuestionsPage');
        if (!categoryQuestionsPage) {
            categoryQuestionsPage = document.createElement('div');
            categoryQuestionsPage.id = 'categoryQuestionsPage';
            categoryQuestionsPage.className = 'category-questions-page page-hidden';
            
            const mainContent = document.querySelector('.main-content');
            mainContent.appendChild(categoryQuestionsPage);
        }
        
        // 渲染分类问题内容
        categoryQuestionsPage.innerHTML = `
            <div class="page-header">
                <button class="back-btn" onclick="app.showWelcomePage()">${this.i18n.t('backToHomePage')}</button>
                <h2>${categoryIcon} ${categoryName}</h2>
                <p class="category-count">${this.i18n.t('totalQuestions', { count: questions.length })}</p>
            </div>
            <div class="questions-list">
                ${questions.map(q => `
                    <div class="question-item" onclick="app.showQuestion('${q.id}')">
                        <div class="question-text">${q.title[lang]}</div>
                        <div class="question-arrow">→</div>
                    </div>
                `).join('')}
            </div>
        `;
        
        categoryQuestionsPage.classList.remove('page-hidden');
    }
    
    // 刷新当前页面
    refreshCurrentPage() {
        this.renderCategories();
        
        switch (this.currentPage) {
            case 'welcome':
                this.showWelcomePage();
                break;
            case 'faq':
                if (this.currentQuestion) {
                    this.showQuestion(this.currentQuestion.id);
                }
                break;
            case 'search':
                // 重新执行搜索（显示加载指示器）
                this.performSearch(null, true);
                break;
            case 'category':
                // 需要保存当前分类ID
                break;
        }
    }
    
    // 处理URL参数 (增强版本 - 含清理机制)
    handleUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const questionId = urlParams.get('q');
        const category = urlParams.get('c');
        const search = urlParams.get('s');

        // 🔧 新增：清理机制 - 检查搜索参数的有效性
        if (search && search.trim().length > 0) {
            const trimmedSearch = search.trim();
            
            // 防止意外搜索：如果搜索内容看起来不是用户主动输入的，则清理它
            const isValidSearch = trimmedSearch.length >= 2 && 
                                  !trimmedSearch.includes('null') && 
                                  !trimmedSearch.includes('undefined');
            
            if (isValidSearch) {
                console.log(`📎 从URL恢复搜索查询: "${trimmedSearch}"`);
                document.getElementById('searchInput').value = trimmedSearch;
                this.performSearch(null, true);
            } else {
                console.warn(`🧹 清理无效的URL搜索参数: "${search}"`);
                this.clearURLSearchParam();
            }
        } else if (questionId) {
            this.showQuestion(questionId);
        } else if (category) {
            this.showCategoryPage(category);
        }
    }

    // 新增：清理URL搜索参数
    clearURLSearchParam() {
        const url = new URL(window.location);
        url.searchParams.delete('s');
        window.history.replaceState({}, '', url);
        console.log('🧹 已清理URL搜索参数');
    }

    // 更新URL参数 - 用于搜索历史和状态管理
    updateURLParam(key, value) {
        try {
            const url = new URL(window.location);
            if (value) {
                url.searchParams.set(key, value);
            } else {
                url.searchParams.delete(key);
            }
            // 使用 replaceState 避免在浏览器历史中创建新条目
            window.history.replaceState({}, '', url);
        } catch (error) {
            console.warn('更新URL参数失败:', error);
        }
    }

    // 移动端底部导航设置 - 替换原来的侧边栏功能
    setupBottomNavigation() {
        const bottomNav = document.querySelector('.bottom-nav');
        if (!bottomNav) return;

        // 底部导航点击事件
        bottomNav.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = item.dataset.tab;
                this.switchBottomNavTab(tab);
                
                // 触摸反馈
                if (navigator.vibrate) {
                    navigator.vibrate(30);
                }
            });
        });
    }

    // 切换底部导航标签
    switchBottomNavTab(tab) {
        // 更新活动状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

        // 根据标签切换功能
        switch(tab) {
            case 'faq':
                this.showWelcomePage();
                break;
            case 'chat':
                this.toggleChatMode();
                break;
        }
    }

    // 触摸反馈设置
    setupTouchFeedback() {
        // 为所有可点击元素添加触摸反馈
        const clickableElements = document.querySelectorAll(`
            button,
            .btn,
            .category-link,
            .lang-btn,
            .search-result-item,
            .quick-start-buttons button,
            [role="button"]
        `);

        clickableElements.forEach(element => {
            // 触摸开始
            element.addEventListener('touchstart', (e) => {
                element.classList.add('touch-active');

                // 轻微震动反馈
                if (navigator.vibrate) {
                    navigator.vibrate(10);
                }
            }, { passive: true });

            // 触摸结束
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.classList.remove('touch-active');
                }, 150);
            }, { passive: true });

            // 触摸取消
            element.addEventListener('touchcancel', (e) => {
                element.classList.remove('touch-active');
            }, { passive: true });
        });

        // 为搜索输入框添加虚拟键盘适配
        this.setupKeyboardAdaptation();
    }

    // 虚拟键盘适配
    setupKeyboardAdaptation() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;

        let initialViewportHeight = window.innerHeight;

        // 监听视口高度变化（虚拟键盘弹出/收起）
        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDiff = initialViewportHeight - currentHeight;

            if (heightDiff > 150) {
                // 虚拟键盘弹出
                document.body.classList.add('keyboard-open');

                // 确保搜索框可见
                setTimeout(() => {
                    searchInput.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }, 300);
            } else {
                // 虚拟键盘收起
                document.body.classList.remove('keyboard-open');
            }
        });

        // 输入框获得焦点时的处理
        searchInput.addEventListener('focus', () => {
            setTimeout(() => {
                searchInput.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }, 300);
        });
    }

    // 设置移动端交互功能
    setupMobileInteractions() {
        if (!this.mobileManager) return;

        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');
        const streamingContainer = document.querySelector('.streaming-search-container');
        
        // 初始化移动端交互管理器
        this.mobileManager.initialize();

        // 为搜索输入框设置手势控制
        if (searchInput) {
            // 长按搜索框显示语音输入提示（如果支持）
            this.mobileManager.addLongPressListener(searchInput, () => {
                this.showVoiceInputHint();
                this.mobileManager.triggerHapticFeedback('selection');
            });

            // 双击搜索框清空内容
            this.mobileManager.addDoubleTapListener(searchInput, () => {
                searchInput.value = '';
                searchInput.focus();
                this.showWelcomePage();
                this.hideStreamingContainer();
                this.mobileManager.triggerHapticFeedback('impact');
            });

            // 搜索框向下滑动取消当前搜索
            this.mobileManager.addSwipeListener(searchInput, 'down', () => {
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = null;
                }
                searchInput.blur();
                this.hideStreamingContainer();
                this.mobileManager.triggerHapticFeedback('notification');
            });
        }

        // 为流式搜索容器设置手势控制
        if (streamingContainer || document.querySelector('.search-results')) {
            // 向左滑动关闭搜索结果
            const targetElement = streamingContainer || searchResults;
            if (targetElement) {
                this.mobileManager.addSwipeListener(targetElement, 'left', () => {
                    this.hideStreamingContainer();
                    this.showWelcomePage();
                    this.mobileManager.triggerHapticFeedback('selection');
                });

                // 向右滑动刷新搜索结果
                this.mobileManager.addSwipeListener(targetElement, 'right', () => {
                    const currentQuery = searchInput?.value?.trim();
                    if (currentQuery) {
                        this.refreshSearchResults(currentQuery);
                        this.mobileManager.triggerHapticFeedback('impact');
                    }
                });
            }
        }

        // 为搜索结果项设置触摸反馈
        this.setupSearchResultsTouchFeedback();

        console.log('✅ 移动端交互功能设置完成');
    }

    // 为搜索结果设置触摸反馈
    setupSearchResultsTouchFeedback() {
        // 使用事件委托处理动态创建的搜索结果
        document.addEventListener('touchstart', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && e.target.closest) {
                const resultItem = e.target.closest('.search-result-item, .streaming-suggestion-item, .streaming-result-item');
                if (resultItem && this.mobileManager) {
                    this.mobileManager.triggerHapticFeedback('selection');
                    resultItem.classList.add('touch-pressed');
                }
            }
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && e.target.closest) {
                const resultItem = e.target.closest('.search-result-item, .streaming-suggestion-item, .streaming-result-item');
                if (resultItem) {
                    setTimeout(() => {
                        resultItem.classList.remove('touch-pressed');
                    }, 150);
                }
            }
        }, { passive: true });
    }

    // 显示语音输入提示
    showVoiceInputHint() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            return; // 浏览器不支持语音识别
        }

        // 创建提示toast
        const toast = document.createElement('div');
        toast.className = 'voice-input-toast';
        toast.textContent = this.i18n.getText('voiceInputAvailable') || '长按可使用语音输入';
        toast.style.cssText = `
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 10000;
            animation: fadeInOut 2s ease-in-out;
        `;

        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 2000);
    }

    // 刷新搜索结果
    refreshSearchResults(query) {
        console.log('🔄 刷新搜索结果:', query);
        
        // 重新执行搜索
        if (this.streamingEnabled && this.streamingEngine) {
            // 清除当前显示的结果
            this.hideStreamingContainer();
            
            // 延迟一下再开始新搜索，给用户视觉反馈
            setTimeout(() => {
                this.performRealtimeSearch(query);
            }, 200);
        } else {
            this.performSearch(query);
        }
    }

    // ========== 智能搜索建议功能 ==========

    // 显示智能搜索建议
    async showSmartSuggestions(query) {
        if (!this.suggestionManager) return;

        try {
            // 如果查询太短，显示默认建议
            if (!query || query.trim().length === 0) {
                if (window.CONFIG?.search?.suggestions?.showDefaultSuggestions !== false) {
                    const defaultSuggestions = await this.suggestionManager.generateSuggestions('', {
                        category: this.getCurrentCategory()
                    });
                    this.renderSmartSuggestions(defaultSuggestions, true);
                }
                return;
            }

            // 防抖处理
            if (this.suggestionTimeout) {
                clearTimeout(this.suggestionTimeout);
            }

            this.suggestionTimeout = setTimeout(async () => {
                const context = {
                    category: this.getCurrentCategory(),
                    page: this.currentPage,
                    language: this.i18n.getCurrentLanguage()
                };

                const suggestions = await this.suggestionManager.generateSuggestions(query, context);
                this.renderSmartSuggestions(suggestions, false, query);
            }, 300);

        } catch (error) {
            console.warn('生成智能建议失败:', error);
        }
    }

    // 渲染智能搜索建议
    renderSmartSuggestions(suggestions, isDefault = false, originalQuery = '') {
        if (!suggestions || suggestions.length === 0) {
            this.hideSmartSuggestions();
            return;
        }

        // 创建或获取建议容器
        let suggestionContainer = document.getElementById('smart-suggestions-container');
        if (!suggestionContainer) {
            suggestionContainer = document.createElement('div');
            suggestionContainer.id = 'smart-suggestions-container';
            suggestionContainer.className = 'smart-suggestions-container';
            
            // 插入到搜索框下方
            const searchSection = document.querySelector('.search-section');
            if (searchSection && searchSection.parentNode) {
                searchSection.parentNode.insertBefore(suggestionContainer, searchSection.nextSibling);
            }
        }

        // 生成建议HTML
        const suggestionsHTML = suggestions.map((suggestion, index) => `
            <div class="smart-suggestion-item" 
                 data-suggestion="${encodeURIComponent(suggestion.text)}" 
                 data-type="${suggestion.type}"
                 data-score="${suggestion.score}"
                 style="animation-delay: ${index * 50}ms">
                <div class="suggestion-content">
                    <span class="suggestion-icon">${this.getSuggestionIcon(suggestion.type)}</span>
                    <span class="suggestion-text">${suggestion.text}</span>
                </div>
                <div class="suggestion-meta">
                    <span class="suggestion-reason">${suggestion.reason}</span>
                    ${suggestion.frequency ? `<span class="suggestion-frequency">${suggestion.frequency}次</span>` : ''}
                </div>
            </div>
        `).join('');

        const headerText = isDefault ? 
            (this.i18n.getText('defaultSuggestions') || '推荐搜索') :
            (this.i18n.getText('smartSuggestions') || '智能建议');

        suggestionContainer.innerHTML = `
            <div class="suggestions-header">
                <span class="suggestions-title">${headerText}</span>
                <span class="suggestions-count">${suggestions.length}</span>
                <button class="suggestions-close" onclick="app.hideSmartSuggestions()" title="关闭建议">×</button>
            </div>
            <div class="suggestions-list">
                ${suggestionsHTML}
            </div>
            ${!isDefault ? `
                <div class="suggestions-footer">
                    <span class="suggestions-hint">
                        ${this.i18n.getText('suggestionHint') || '点击使用建议，长按查看详情'}
                    </span>
                </div>
            ` : ''}
        `;

        // 绑定事件
        this.bindSmartSuggestionEvents(suggestionContainer, originalQuery);

        // 显示容器
        suggestionContainer.classList.add('active');
        
        console.log('✨ 显示智能建议:', suggestions.length, '个');
    }

    // 绑定智能建议事件
    bindSmartSuggestionEvents(container, originalQuery) {
        const suggestionItems = container.querySelectorAll('.smart-suggestion-item');
        
        suggestionItems.forEach((item, index) => {
            const suggestionText = decodeURIComponent(item.dataset.suggestion);
            const suggestionType = item.dataset.type;
            
            // 点击事件
            item.addEventListener('click', () => {
                this.useSmartSuggestion(suggestionText, suggestionType, originalQuery);
                
                // 触觉反馈
                if (this.mobileManager) {
                    this.mobileManager.triggerHapticFeedback('selection');
                }
            });

            // 长按事件（显示详情）
            if (this.mobileManager) {
                this.mobileManager.addLongPressListener(item, () => {
                    this.showSuggestionDetails(suggestionText, suggestionType);
                    this.mobileManager.triggerHapticFeedback('impact');
                });
            }

            // 悬停效果（桌面端）
            item.addEventListener('mouseenter', () => {
                item.classList.add('hovered');
                
                // 预取相关数据
                if (this.suggestionManager) {
                    this.suggestionManager.recordSuggestionUsage({
                        text: suggestionText,
                        type: suggestionType
                    }, originalQuery);
                }
            });

            item.addEventListener('mouseleave', () => {
                item.classList.remove('hovered');
            });
        });
    }

    // 使用智能建议
    useSmartSuggestion(suggestionText, suggestionType, originalQuery = '') {
        console.log('🎯 使用智能建议:', suggestionText, suggestionType);
        
        // 记录建议使用
        if (this.suggestionManager) {
            this.suggestionManager.recordSuggestionUsage({
                text: suggestionText,
                type: suggestionType
            }, originalQuery);
        }

        // 更新搜索框
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = suggestionText;
            searchInput.focus();
        }

        // 隐藏建议
        this.hideSmartSuggestions();

        // 执行搜索
        setTimeout(() => {
            this.performRealtimeSearch(suggestionText);
        }, 100);
    }

    // 显示建议详情
    showSuggestionDetails(suggestionText, suggestionType) {
        const modal = document.createElement('div');
        modal.className = 'suggestion-details-modal';
        modal.innerHTML = `
            <div class="modal-backdrop" onclick="this.parentNode.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${this.i18n.getText('suggestionDetails') || '建议详情'}</h3>
                    <button class="modal-close" onclick="this.closest('.suggestion-details-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="suggestion-detail-item">
                        <strong>${this.i18n.getText('suggestionText') || '建议内容'}:</strong>
                        <span>${suggestionText}</span>
                    </div>
                    <div class="suggestion-detail-item">
                        <strong>${this.i18n.getText('suggestionType') || '建议类型'}:</strong>
                        <span>${this.getSuggestionTypeLabel(suggestionType)}</span>
                    </div>
                    <div class="suggestion-detail-item">
                        <strong>${this.i18n.getText('suggestionIcon') || '类型图标'}:</strong>
                        <span>${this.getSuggestionIcon(suggestionType)}</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.suggestion-details-modal').remove()">
                        ${this.i18n.getText('close') || '关闭'}
                    </button>
                    <button class="btn-primary" onclick="app.useSmartSuggestion('${suggestionText}', '${suggestionType}'); this.closest('.suggestion-details-modal').remove();">
                        ${this.i18n.getText('useSuggestion') || '使用建议'}
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.classList.add('active');
    }

    // 隐藏智能建议
    hideSmartSuggestions() {
        const container = document.getElementById('smart-suggestions-container');
        if (container) {
            container.classList.remove('active');
            
            // 延迟移除以保持动画
            setTimeout(() => {
                if (!container.classList.contains('active')) {
                    container.innerHTML = '';
                }
            }, 300);
        }
        
        // 清除定时器
        if (this.suggestionTimeout) {
            clearTimeout(this.suggestionTimeout);
            this.suggestionTimeout = null;
        }
    }

    // 获取建议类型图标
    getSuggestionIcon(type) {
        const icons = {
            history: '🕐',
            recent: '⏱️', 
            popular: '🔥',
            hot: '🌟',
            semantic: '🔗',
            context: '📍',
            category: '📂',
            completion: '✨',
            preference: '❤️',
            fallback: '💡'
        };
        return icons[type] || '💭';
    }

    // 获取建议类型标签
    getSuggestionTypeLabel(type) {
        const labels = {
            history: this.i18n.getText('suggestionHistory') || '搜索历史',
            recent: this.i18n.getText('suggestionRecent') || '最近搜索',
            popular: this.i18n.getText('suggestionPopular') || '热门搜索',
            hot: this.i18n.getText('suggestionHot') || '热门查询',
            semantic: this.i18n.getText('suggestionSemantic') || '相关词汇',
            context: this.i18n.getText('suggestionContext') || '上下文相关',
            category: this.i18n.getText('suggestionCategory') || '分类热门',
            completion: this.i18n.getText('suggestionCompletion') || '自动补全',
            preference: this.i18n.getText('suggestionPreference') || '个性化推荐',
            fallback: this.i18n.getText('suggestionFallback') || '默认建议'
        };
        return labels[type] || '智能建议';
    }

    // 获取当前分类
    getCurrentCategory() {
        // 从当前页面状态或URL参数获取分类
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('c') || this.currentCategory || null;
    }

    // 记录搜索行为（集成到现有搜索方法中）
    recordSearchBehavior(query, results, context = {}) {
        if (this.suggestionManager && window.CONFIG?.search?.suggestions?.enableLearning !== false) {
            const language = this.i18n.getCurrentLanguage();
            this.suggestionManager.recordSearch(query, language, results, {
                category: this.getCurrentCategory(),
                page: this.currentPage,
                ...context
            });
        }
    }

    // ========== 移动端优化功能 ==========

    // 设置移动端优化
    setupMobileOptimizations() {
        if (!this.mobileOptimizer) return;

        // 初始化移动端优化
        this.mobileOptimizer.initialize();
        
        // 监听键盘事件
        window.addEventListener('mobileoptimization:keyboardopen', (e) => {
            this.onMobileKeyboardOpen(e.detail);
        });

        window.addEventListener('mobileoptimization:keyboardclose', (e) => {
            this.onMobileKeyboardClose(e.detail);
        });

        // 监听布局重新计算事件
        window.addEventListener('mobileoptimization:layoutrecalculated', (e) => {
            this.onMobileLayoutRecalculated(e.detail);
        });

        console.log('✅ 移动端优化功能设置完成');
    }

    // 移动端键盘打开处理
    onMobileKeyboardOpen(detail) {
        console.log('⌨️ 移动端键盘打开:', detail.height + 'px');
        
        // 隐藏智能建议（避免遮挡）
        if (detail.height > 200) { // 只有在键盘足够高时才隐藏
            this.hideSmartSuggestions();
        }
        
        // 调整流式搜索容器
        this.adjustStreamingContainerForKeyboard(true, detail.height);
        
        // 触觉反馈
        if (this.mobileManager) {
            this.mobileManager.triggerHapticFeedback('selection');
        }
    }

    // 移动端键盘关闭处理
    onMobileKeyboardClose(detail) {
        console.log('⌨️ 移动端键盘关闭');
        
        // 恢复流式搜索容器
        this.adjustStreamingContainerForKeyboard(false);
        
        // 如果搜索框有内容，重新显示建议
        const searchInput = document.getElementById('searchInput');
        if (searchInput && searchInput.value.trim()) {
            setTimeout(() => {
                this.showSmartSuggestions(searchInput.value);
            }, 300); // 等待键盘关闭动画完成
        }
    }

    // 移动端布局重新计算处理
    onMobileLayoutRecalculated(detail) {
        console.log('📱 移动端布局重新计算:', detail.orientationState);
        
        // 重新计算搜索容器大小
        this.recalculateSearchContainerSizes();
        
        // 如果有活动的流式搜索，重新调整
        const streamingContainer = document.querySelector('.streaming-search-container');
        if (streamingContainer && streamingContainer.classList.contains('active')) {
            setTimeout(() => {
                this.adjustStreamingContainerLayout();
            }, 100);
        }
    }

    // 调整流式搜索容器以适应键盘
    adjustStreamingContainerForKeyboard(keyboardOpen, keyboardHeight = 0) {
        const streamingContainer = document.querySelector('.streaming-search-container');
        if (!streamingContainer) return;

        if (keyboardOpen) {
            const maxHeight = window.innerHeight - keyboardHeight - 150; // 150px 缓冲
            streamingContainer.style.maxHeight = `${Math.max(200, maxHeight)}px`;
            streamingContainer.classList.add('keyboard-mode');
        } else {
            streamingContainer.style.maxHeight = '';
            streamingContainer.classList.remove('keyboard-mode');
        }
    }

    // 重新计算搜索容器大小
    recalculateSearchContainerSizes() {
        const containers = [
            document.querySelector('.streaming-search-container'),
            document.getElementById('smart-suggestions-container')
        ];

        containers.forEach(container => {
            if (container && container.classList.contains('active')) {
                // 重置尺寸让CSS重新计算
                container.style.width = '';
                container.style.height = '';
                container.style.maxHeight = '';
                
                // 强制重新布局
                container.offsetHeight; // 触发重排
            }
        });
    }

    // 调整流式搜索容器布局
    adjustStreamingContainerLayout() {
        const streamingContainer = document.querySelector('.streaming-search-container');
        if (!streamingContainer) return;

        // 获取搜索框位置
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;

        const searchRect = searchInput.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        
        // 计算可用空间
        const availableHeight = viewportHeight - searchRect.bottom - 20; // 20px 缓冲
        
        if (availableHeight > 100) {
            streamingContainer.style.maxHeight = `${availableHeight}px`;
        }
    }

    // 获取移动端优化统计
    getMobileOptimizationStats() {
        if (!this.mobileOptimizer) return null;
        
        return {
            ...this.mobileOptimizer.getStats(),
            timestamp: new Date().toISOString()
        };
    }

    // 强制重新检测移动端状态
    forceMobileStateDetection() {
        if (this.mobileOptimizer) {
            this.mobileOptimizer.forceKeyboardDetection();
        }
    }

    // ========== 系统测试功能 ==========

    // 添加测试按钮（调试模式）
    addTestButton() {
        const testButton = document.createElement('button');
        testButton.id = 'system-test-button';
        testButton.innerHTML = '🧪 运行测试';
        testButton.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            background: #8b5cf6;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
        `;

        testButton.addEventListener('mouseenter', () => {
            testButton.style.background = '#7c3aed';
            testButton.style.transform = 'translateY(-2px)';
        });

        testButton.addEventListener('mouseleave', () => {
            testButton.style.background = '#8b5cf6';
            testButton.style.transform = 'translateY(0)';
        });

        testButton.addEventListener('click', () => {
            this.runSystemTest();
        });

        document.body.appendChild(testButton);
        console.log('🧪 测试按钮已添加（调试模式）');
    }

    // 运行系统测试
    async runSystemTest() {
        if (!this.systemValidator) {
            console.warn('系统验证器未初始化');
            return;
        }

        console.log('🚀 开始系统测试...');
        
        // 显示测试进度
        this.showTestProgress();
        
        try {
            const report = await this.systemValidator.runFullTestSuite();
            
            // 隐藏进度，显示结果
            this.hideTestProgress();
            this.systemValidator.createVisualReport(report);
            
            // 记录测试结果
            console.log('📊 测试完成，健康度:', report.summary.overallHealth + '%');
            
            return report;
            
        } catch (error) {
            this.hideTestProgress();
            console.error('系统测试失败:', error);
            this.showNotification('系统测试执行失败: ' + error.message, 'error');
        }
    }

    // 显示测试进度
    showTestProgress() {
        const progressHTML = `
            <div id="test-progress-modal" class="test-progress-modal">
                <div class="test-progress-content">
                    <div class="test-progress-header">
                        <h3>🧪 系统测试进行中...</h3>
                    </div>
                    <div class="test-progress-body">
                        <div class="progress-spinner"></div>
                        <p id="test-progress-text">正在初始化测试套件...</p>
                        <div class="progress-bar">
                            <div class="progress-fill" id="test-progress-fill"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const progressElement = document.createElement('div');
        progressElement.innerHTML = progressHTML;
        document.body.appendChild(progressElement);

        // 添加样式
        this.addTestProgressStyles();

        // 模拟进度更新
        this.updateTestProgress();
    }

    // 更新测试进度
    updateTestProgress() {
        const progressText = document.getElementById('test-progress-text');
        const progressFill = document.getElementById('test-progress-fill');
        
        const steps = [
            '正在检查核心功能...',
            '正在测试流式搜索...',
            '正在测试智能建议...',
            '正在测试移动端优化...',
            '正在测试系统性能...',
            '正在测试浏览器兼容性...',
            '正在测试可访问性...',
            '正在生成测试报告...'
        ];

        let currentStep = 0;
        const interval = setInterval(() => {
            if (currentStep < steps.length) {
                progressText.textContent = steps[currentStep];
                progressFill.style.width = `${((currentStep + 1) / steps.length) * 100}%`;
                currentStep++;
            } else {
                clearInterval(interval);
            }
        }, 800);

        // 保存清理函数
        this.testProgressCleanup = () => clearInterval(interval);
    }

    // 隐藏测试进度
    hideTestProgress() {
        const progressModal = document.getElementById('test-progress-modal');
        if (progressModal) {
            progressModal.remove();
        }
        
        if (this.testProgressCleanup) {
            this.testProgressCleanup();
        }
    }

    // 添加测试进度样式
    addTestProgressStyles() {
        if (document.querySelector('#test-progress-styles')) return;

        const styles = `
            <style id="test-progress-styles">
            .test-progress-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }
            
            .test-progress-content {
                background: white;
                border-radius: 12px;
                padding: 32px;
                max-width: 400px;
                width: 90%;
                text-align: center;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            }
            
            .test-progress-header h3 {
                margin: 0 0 24px 0;
                color: #374151;
                font-size: 18px;
            }
            
            .progress-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f4f6;
                border-top: 4px solid #8b5cf6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 16px auto;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .progress-bar {
                width: 100%;
                height: 8px;
                background: #f3f4f6;
                border-radius: 4px;
                overflow: hidden;
                margin-top: 16px;
            }
            
            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #8b5cf6, #a855f7);
                border-radius: 4px;
                width: 0%;
                transition: width 0.5s ease;
            }
            
            #test-progress-text {
                margin: 16px 0;
                color: #6b7280;
                font-size: 14px;
            }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    // 快速健康检查
    async runQuickHealthCheck() {
        if (!this.systemValidator) return null;

        const quickTests = [
            'app-initialization',
            'data-manager',
            'streaming-search-basic',
            'smart-suggestions',
            'memory-usage'
        ];

        console.log('⚡ 运行快速健康检查...');
        
        const startTime = performance.now();
        const results = {
            passed: 0,
            failed: 0,
            warnings: 0,
            total: quickTests.length
        };

        // 运行基本检查
        try {
            this.systemValidator.testAppInitialization();
            this.systemValidator.testDataManager();
            await this.systemValidator.testStreamingSearch();
            await this.systemValidator.testSmartSuggestions();
            this.systemValidator.testMemoryUsage();
            
            // 统计结果
            const recentResults = this.systemValidator.testResults.detailedResults.slice(-quickTests.length);
            recentResults.forEach(result => {
                switch (result.status) {
                    case 'passed': results.passed++; break;
                    case 'failed': results.failed++; break;
                    case 'warning': results.warnings++; break;
                }
            });

        } catch (error) {
            results.failed = quickTests.length;
            console.error('快速健康检查失败:', error);
        }

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        const healthScore = (results.passed / results.total * 100).toFixed(0);
        
        console.log(`⚡ 快速健康检查完成: ${healthScore}% (${duration}ms)`);
        console.log(`✅ 通过: ${results.passed}, ❌ 失败: ${results.failed}, ⚠️ 警告: ${results.warnings}`);

        return {
            score: parseInt(healthScore),
            duration,
            results,
            timestamp: new Date().toISOString()
        };
    }

    // 获取系统状态摘要
    getSystemStatusSummary() {
        const status = {
            timestamp: new Date().toISOString(),
            components: {},
            performance: {},
            health: 'unknown'
        };

        // 检查各组件状态
        const components = [
            'dataManager', 'i18n', 'geminiAssistant', 'unifiedSearchEngine', 'streamingEngine',
            'fallbackManager', 'performanceOptimizer', 'suggestionManager',
            'mobileManager', 'mobileOptimizer', 'systemValidator'
        ];

        components.forEach(component => {
            status.components[component] = !!this[component];
        });

        // 添加RAG引擎状态
        status.components.ragEngine = this.ragEnabled === true;
        status.ragDetails = {
            enabled: this.ragEnabled === true,
            initialized: this.unifiedSearchEngine?.ragInitialized === true,
            available: typeof RAGVectorEngine !== 'undefined'
        };

        // 如果统一搜索引擎可用，获取详细统计
        if (this.unifiedSearchEngine) {
            const searchStats = this.unifiedSearchEngine.getStats();
            status.searchEngineStats = searchStats;
        }

        // 性能指标
        if (this.performanceOptimizer) {
            const perfReport = this.performanceOptimizer.getPerformanceReport();
            status.performance = perfReport;
        }

        // 计算整体健康度 (包括RAG引擎)
        const totalComponents = components.length + 1; // +1 for RAG engine
        const enabledComponents = Object.values(status.components).filter(Boolean).length;
        const healthPercentage = (enabledComponents / totalComponents * 100).toFixed(0);
        
        if (healthPercentage >= 90) {
            status.health = 'excellent';
        } else if (healthPercentage >= 70) {
            status.health = 'good';
        } else if (healthPercentage >= 50) {
            status.health = 'fair';
        } else {
            status.health = 'poor';
        }

        status.healthScore = parseInt(healthPercentage);
        
        return status;
    }

    // 显示加载遮罩
    showLoadingOverlay(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.add('show');
        } else {
            overlay.classList.remove('show');
        }
    }

    // 隐藏底部加载提示
    hideLoadingIndicator() {
        // 查找所有可能的加载提示元素
        const loadingElements = document.querySelectorAll('p');
        loadingElements.forEach(element => {
            const text = element.textContent.trim();
            if (text === '加载中...' || text === 'Loading...' || text === 'Memuatkan...') {
                element.style.display = 'none';
            }
        });
    }
    
    // 设置FAB按钮
    setupFABButtons() {
        const fabChat = document.getElementById('fabChat');

        if (fabChat) {
            fabChat.addEventListener('click', () => {
                this.toggleChatMode();
                if (navigator.vibrate) {
                    navigator.vibrate(50);
                }
            });
        }
    }

    // 切换聊天模式
    toggleChatMode() {
        if (this.currentPage === 'chat') {
            this.showWelcomePage();
            return;
        }
        
        this.currentPage = 'chat';
        this.hideAllPages();
        this.showChatInterface();
        
        // 更新底部导航
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector('[data-tab="chat"]').classList.add('active');
    }

    // 显示聊天界面
    showChatInterface() {
        const chatContainer = document.createElement('div');
        chatContainer.id = 'chatContainer';
        chatContainer.className = 'chat-container';
        chatContainer.innerHTML = `
            <div class="chat-header">
                <button class="chat-back" onclick="app.closeChat()">←</button>
                <h2>💬 AI助手</h2>
                <button class="close-chat" onclick="app.closeChat()">×</button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message bot-message">
                    <div class="avatar" style="background-image: url('gmh logo.png')" aria-hidden="true"></div>
                    <div class="message-body">
                        <div class="message-content">
                            👋 你好！我是GoMyHire AI助手，有什么可以帮到你的吗？
                        </div>
                        <div class="message-meta"><span class="message-time">${this.i18n.formatMessageTime(new Date())}</span></div>
                    </div>
                </div>
            </div>
            <div class="chat-input-container">
                <input type="text" id="chatInput" placeholder="输入你的问题..." class="chat-input" aria-label="聊天输入框">
                <button class="send-btn" onclick="app.sendChatMessage()">
                    <span class="icon">📤</span>
                </button>
            </div>
        `;

        // 清除之前可能存在的聊天容器
        const existingChat = document.getElementById('chatContainer');
        if (existingChat) {
            existingChat.remove();
        }

        // 找到主内容容器并添加聊天界面
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.appendChild(chatContainer);
        } else {
            console.error('主内容容器未找到');
            return;
        }

        // 添加active类以显示聊天界面
        chatContainer.classList.add('active');

        // 设置聊天输入框事件
        const chatInput = document.getElementById('chatInput');
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            }
        });

        chatInput.focus();
    }

    // 发送聊天消息
    async sendChatMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();
        
        if (!message) return;
        
        // 添加用户消息
        this.addChatMessage(message, 'user');
        chatInput.value = '';
        
        // 添加等待指示器
        this.addChatMessage('...', 'bot', 'typing');
        
        try {
            if (this.geminiEnabled && this.geminiAssistant) {
                const response = await this.geminiAssistant.chat(message, this.i18n.getCurrentLanguage());
                this.replaceTypingMessage(response);
            } else {
                // 简单的FAQ搜索回复
                const results = await this.dataManager.searchQuestions(message, this.i18n.getCurrentLanguage());
                if (results.length > 0) {
                    const reply = this.generateSimpleReply(results);
                    this.replaceTypingMessage(reply);
                } else {
                    this.replaceTypingMessage('抱歉，没有找到相关答案。请尝试其他关键词，或联系客服。');
                }
            }
        } catch (error) {
            console.error('聊天回复失败:', error);
            this.replaceTypingMessage('抱歉，暂时无法回复。请稍后再试。');
        }
    }

    // 添加聊天消息
    addChatMessage(content, type, className = '') {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message ${className}`;

        // avatar for bot/user
        const avatarHtml = type === 'bot' ? `<div class="avatar" style="background-image: url('gmh logo.png')" aria-hidden="true"></div>` : `<div class="avatar" style="background-image: url('')" aria-hidden="true"></div>`;

        // typing indicator
        let contentHtml = '';
        if (className === 'typing') {
            contentHtml = `<div class="message-body"><div class="message-content"><span class="dots"><span></span><span></span><span></span></span></div><div class="message-meta"><span class="message-time">${this.i18n.t('justNow')}</span></div></div>`;
        } else {
            const ts = this.i18n.formatMessageTime(new Date());
            contentHtml = `<div class="message-body"><div class="message-content">${content}</div><div class="message-meta"><span class="message-time">${ts}</span></div></div>`;
        }

        messageDiv.innerHTML = avatarHtml + contentHtml;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        return messageDiv;
    }

    // 替换输入中的消息
    replaceTypingMessage(content) {
        const typingMessages = document.querySelectorAll('.typing');
        typingMessages.forEach(msg => msg.remove());
        this.addChatMessage(content, 'bot');
    }

    // 生成简单回复
    generateSimpleReply(results) {
        const topResults = results.slice(0, 3);
        let reply = '我找到了一些相关信息：\n\n';
        
        topResults.forEach((result, index) => {
            const lang = this.i18n.getCurrentLanguage();
            const question = result.question;
            reply += `${index + 1}. **${question.title[lang]}**\n点击查看详情\n\n`;
        });
        
        return reply;
    }

    // 关闭聊天
    closeChat() {
        const chatContainer = document.getElementById('chatContainer');
        if (chatContainer) {
            chatContainer.remove();
        }

        // 清除对话历史
        if (this.geminiAssistant && this.geminiAssistant.clearChatHistory) {
            this.geminiAssistant.clearChatHistory();
        }

        // 更新底部导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector('[data-tab="faq"]').classList.add('active');

        this.showWelcomePage();
    }

    // 处理紧急联系


    // 设置返回顶部按钮
    setupBackToTop() {
        const backToTopBtn = document.getElementById('backToTopBtn');
        if (!backToTopBtn) {
            // 创建返回顶部按钮
            const btn = document.createElement('button');
            btn.id = 'backToTopBtn';
            btn.className = 'back-to-top';
            btn.innerHTML = '↑';
            document.body.appendChild(btn);
            
            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    btn.classList.add('show');
                } else {
                    btn.classList.remove('show');
                }
            });
            
            btn.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
                if (navigator.vibrate) {
                    navigator.vibrate(20);
                }
            });
            return;
        }

        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // 显示搜索增强建议
    showSearchEnhancement(enhanced) {
        const enhancementInfo = document.createElement('div');
        enhancementInfo.className = 'search-enhancement-info';
        enhancementInfo.innerHTML = `
            <div class="enhancement-header">
                <span class="gemini-icon">✨</span>
                <span>AI 搜索助手建议</span>
            </div>
            <div class="enhancement-suggestions">
                ${enhanced.suggestions.map(suggestion => 
                    `<button class="suggestion-btn" onclick="app.searchSuggestion('${suggestion}')">${suggestion}</button>`
                ).join('')}
            </div>
        `;
        
        // 插入到搜索结果页面
        const searchPage = document.getElementById('searchPage');
        const existingInfo = searchPage.querySelector('.search-enhancement-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        const searchInfo = document.getElementById('searchInfo');
        searchInfo.parentNode.insertBefore(enhancementInfo, searchInfo.nextSibling);
    }

    // 使用建议进行搜索
    searchSuggestion(suggestion) {
        document.getElementById('searchInput').value = suggestion;
        this.performSearch(null, true);
    }

    // 显示Gemini状态
    showGeminiStatus() {
        if (!this.geminiAssistant) {
            this.showNotification('⚠️ Gemini AI 助手未初始化\n请检查配置文件', 5000);
            return;
        }

        const status = this.geminiAssistant.getStatus();
        const statusMessage = status.available
            ? `✨ Gemini AI 助手已启用\n模型: ${status.model}\n状态: 正常运行\nFAQ数据: ${status.dataManagerConnected ? '已连接' : '未连接'}`
            : `⚠️ Gemini AI 助手不可用\n请检查API配置`;

        this.showNotification(statusMessage, 5000);
    }
    
    // 显示流式指示器
    showStreamingIndicator(show) {
        let indicator = document.getElementById('streamingIndicator');
        
        if (show && !indicator) {
            indicator = document.createElement('div');
            indicator.id = 'streamingIndicator';
            indicator.className = 'streaming-indicator';
            indicator.innerHTML = `
                <div class="streaming-content">
                    <div class="streaming-dots">
                        <span></span><span></span><span></span>
                    </div>
                    <span class="streaming-text">AI 正在分析...</span>
                </div>
            `;
            
            const searchContainer = document.querySelector('.search-container');
            searchContainer.appendChild(indicator);
        } else if (!show && indicator) {
            indicator.remove();
        }
    }
    


    // 显示反馈
    showFeedback() {
        const feedback = prompt('请输入您的反馈意见：');
        if (feedback && feedback.trim()) {
            // 这里可以发送反馈到服务器
            console.log('用户反馈:', feedback);
            this.showNotification(this.i18n.t('thankYouForFeedback'));
        }
    }

    // ========== 流式搜索UI方法 ==========
    
    // 显示流式搜索容器
    showStreamingContainer() {
        let container = document.getElementById('streamingSearchContainer');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'streamingSearchContainer';
            container.className = 'streaming-search-container';
            container.innerHTML = `
                <div class="streaming-header">
                    <div class="streaming-status" id="streamingStatus">
                        <div class="status-indicator" id="statusIndicator"></div>
                        <div class="status-text" id="statusText">准备搜索...</div>
                    </div>
                    <button class="streaming-cancel-btn" id="streamingCancelBtn">
                        <span>取消</span>
                    </button>
                </div>
                <div class="streaming-content" id="streamingContent">
                    <!-- 动态内容 -->
                </div>
            `;
            
            // 找到搜索输入框容器
            const searchContainer = document.querySelector('.search-container');
            if (searchContainer) {
                searchContainer.appendChild(container);
                
                // 绑定取消按钮事件
                const cancelBtn = container.querySelector('#streamingCancelBtn');
                cancelBtn.addEventListener('click', () => {
                    this.cancelStreamingSearch();
                });
            }
        }
        
        // 显示容器
        container.classList.add('active');
        
        // 初始化状态
        this.updateSearchStatus('🔍 开始搜索...', 'searching');
    }
    
    // 隐藏流式搜索容器
    hideStreamingContainer() {
        const container = document.getElementById('streamingSearchContainer');
        if (container) {
            container.classList.remove('active');
            // 清空内容
            const content = container.querySelector('#streamingContent');
            if (content) {
                content.innerHTML = '';
            }
        }
    }
    
    // 更新搜索状态
    updateSearchStatus(message, statusType) {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        if (statusIndicator && statusText) {
            // 移除所有状态类
            statusIndicator.className = 'status-indicator';
            // 添加新状态类
            statusIndicator.classList.add(statusType);
            
            // 更新文本
            statusText.textContent = message;
        }
    }
    
    // 显示部分结果（基础搜索结果）
    showPartialResults(query, results, resultType) {
        const content = document.getElementById('streamingContent');
        if (!content) return;
        
        const sectionId = `${resultType}-results`;
        let section = document.getElementById(sectionId);
        
        if (!section) {
            section = document.createElement('div');
            section.id = sectionId;
            section.className = `streaming-section ${resultType}-section`;
            
            const title = this.getResultSectionTitle(resultType);
            section.innerHTML = `
                <div class="section-header">
                    <h3>${title}</h3>
                    <span class="result-count">${results.length} 个结果</span>
                </div>
                <div class="section-content" id="${resultType}-content"></div>
            `;
            
            content.appendChild(section);
        }
        
        // 渲染结果
        const sectionContent = section.querySelector('.section-content');
        this.renderStreamingResults(sectionContent, results, resultType);
        
        // 添加动画
        this.animateStreamingSection(section);
    }
    
    // 获取结果分区标题
    getResultSectionTitle(resultType) {
        const titles = {
            'basic': '🔍 即时搜索结果',
            'enhanced': '✨ AI增强结果',
            'suggestions': '💡 智能建议',
            'final': '⚡ 优化结果'
        };
        return titles[resultType] || '搜索结果';
    }
    
    // 渲染流式结果
    renderStreamingResults(container, results, resultType) {
        const animationDelay = window.CONFIG?.search?.streaming?.ui?.animationDelay || 100;
        
        container.innerHTML = '';
        
        results.forEach((result, index) => {
            const resultElement = this.createStreamingResultElement(result, resultType);
            resultElement.style.animationDelay = `${index * animationDelay}ms`;
            container.appendChild(resultElement);
        });
    }
    
    // 创建流式结果元素
    createStreamingResultElement(result, resultType) {
        const language = this.i18n.getCurrentLanguage();
        const question = result.question || result;
        
        const element = document.createElement('div');
        element.className = `streaming-result-item ${resultType}-item`;
        element.dataset.questionId = question.id;
        
        const title = question.title?.[language] || '未知标题';
        const excerpt = this.generateExcerpt(question.content?.[language] || '', 120);
        const confidence = result.confidence ? Math.round(result.confidence * 100) : null;
        
        element.innerHTML = `
            <div class="result-header">
                <span class="result-id">${question.id}</span>
                ${confidence ? `<span class="confidence-badge">${confidence}%</span>` : ''}
            </div>
            <div class="result-title">${title}</div>
            <div class="result-excerpt">${excerpt}</div>
        `;
        
        // 添加点击事件
        element.addEventListener('click', () => {
            this.showQuestion(question.id);
            this.hideStreamingContainer();
        });
        
        return element;
    }
    
    // 显示意图分析
    showIntentAnalysis(intent, confidence) {
        const content = document.getElementById('streamingContent');
        if (!content) return;
        
        let analysisSection = document.getElementById('intent-analysis');
        if (!analysisSection) {
            analysisSection = document.createElement('div');
            analysisSection.id = 'intent-analysis';
            analysisSection.className = 'streaming-section analysis-section';
            
            content.appendChild(analysisSection);
        }
        
        const confidencePercent = Math.round((confidence || 0.5) * 100);
        analysisSection.innerHTML = `
            <div class="section-header">
                <h3>🧠 AI意图分析</h3>
                <span class="confidence-score">${confidencePercent}% 置信度</span>
            </div>
            <div class="intent-content">
                <div class="intent-result">
                    <span class="intent-label">识别问题类型：</span>
                    <span class="intent-value">${intent}</span>
                </div>
            </div>
        `;
        
        this.animateStreamingSection(analysisSection);
    }
    
    // 显示搜索建议
    showSearchSuggestions(suggestions) {
        const content = document.getElementById('streamingContent');
        if (!content) return;
        
        let suggestionsSection = document.getElementById('search-suggestions');
        if (!suggestionsSection) {
            suggestionsSection = document.createElement('div');
            suggestionsSection.id = 'search-suggestions';
            suggestionsSection.className = 'streaming-section suggestions-section';
            
            content.appendChild(suggestionsSection);
        }
        
        const suggestionItems = suggestions.map(suggestion => `
            <button class="suggestion-chip" data-suggestion="${suggestion.text}">
                <span class="suggestion-text">${suggestion.text}</span>
                <span class="suggestion-type">${this.getSuggestionTypeLabel(suggestion.type)}</span>
            </button>
        `).join('');
        
        suggestionsSection.innerHTML = `
            <div class="section-header">
                <h3>💡 智能搜索建议</h3>
                <span class="suggestion-count">${suggestions.length} 个建议</span>
            </div>
            <div class="suggestions-content">
                ${suggestionItems}
            </div>
        `;
        
        // 绑定建议点击事件
        suggestionsSection.querySelectorAll('.suggestion-chip').forEach(chip => {
            chip.addEventListener('click', () => {
                const suggestion = chip.dataset.suggestion;
                this.searchSuggestion(suggestion);
            });
        });
        
        this.animateStreamingSection(suggestionsSection);
    }
    
    // 获取建议类型标签
    getSuggestionTypeLabel(type) {
        const labels = {
            'keyword': '关键词',
            'intent': '相关',
            'category': '分类'
        };
        return labels[type] || '';
    }
    
    // 显示流式搜索最终结果
    showStreamingSearchResults(query, finalResult) {
        // 清空之前的内容
        const content = document.getElementById('streamingContent');
        if (content) {
            content.innerHTML = '';
        }
        
        // 显示最终结果
        this.showPartialResults(query, finalResult.results, 'final');
        
        // 如果有建议，也显示出来
        if (finalResult.suggestions && finalResult.suggestions.length > 0) {
            this.showSearchSuggestions(finalResult.suggestions);
        }
        
        // 记录搜索行为
        this.recordSearchBehavior(query, finalResult.results, {
            strategy: finalResult.strategy,
            hasAIEnhancement: finalResult.enhanced,
            suggestionCount: finalResult.suggestions?.length || 0,
            searchTime: finalResult.searchTime || 0
        });
        
        // 更新统计信息
        this.updateSearchStats(finalResult);
    }
    
    // 显示无流式结果
    showNoStreamingResults(query) {
        const content = document.getElementById('streamingContent');
        if (!content) return;
        
        content.innerHTML = `
            <div class="no-results-section">
                <div class="no-results-icon">😔</div>
                <h3>未找到相关结果</h3>
                <p>抱歉，没有找到与"${query}"相关的内容</p>
                <div class="no-results-actions">
                    <button class="action-btn" onclick="app.showWelcomePage()">
                        返回首页
                    </button>
                    <button class="action-btn secondary" onclick="app.showCategoriesPage()">
                        浏览分类
                    </button>
                </div>
            </div>
        `;
    }
    
    // 更新搜索统计
    updateSearchStats(result) {
        // 可以添加搜索统计信息的显示
        console.log('搜索完成统计:', {
            query: result.query,
            totalResults: result.totalFound,
            enhanced: result.enhanced,
            suggestions: result.suggestions?.length || 0
        });
    }
    
    // 流式分区动画
    animateStreamingSection(section) {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        
        // 触发重排后应用动画
        requestAnimationFrame(() => {
            section.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        });
    }
    
    // 取消流式搜索
    cancelStreamingSearch() {
        console.log('🚫 用户取消流式搜索');
        
        // 取消流式搜索引擎
        if (this.streamingEngine) {
            this.streamingEngine.cancelCurrentStream();
        }
        
        // 隐藏容器
        this.hideStreamingContainer();
        
        // 触觉反馈
        if (navigator.vibrate) {
            navigator.vibrate([50, 100, 50]);
        }
        
        // 显示取消提示
        this.showNotification('搜索已取消', 2000);
    }
    
    // 使用建议进行搜索
    searchSuggestion(suggestion) {
        console.log('🔍 使用建议搜索:', suggestion);
        
        // 更新搜索输入框
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = suggestion;
        }
        
        // 隐藏当前结果
        this.hideStreamingContainer();
        
        // 执行新搜索
        setTimeout(() => {
            this.performRealtimeSearch(suggestion);
        }, 200);
    }

    // ========== 性能优化方法 ==========
    
    // 执行内存清理
    async performMemoryCleanup() {
        console.log('🧹 执行应用内存清理...');
        
        try {
            // 清理流式搜索缓存
            if (this.streamingEngine) {
                this.streamingEngine.cleanup();
            }
            
            // 清理降级策略统计（保留最近的）
            if (this.fallbackManager) {
                const stats = this.fallbackManager.getStats();
                if (stats.totalAttempts > 1000) {
                    this.fallbackManager.resetStats();
                }
            }
            
            // 清理DOM中的流式搜索容器
            this.hideStreamingContainer();
            const container = document.getElementById('streamingSearchContainer');
            if (container) {
                const content = container.querySelector('#streamingContent');
                if (content) {
                    content.innerHTML = '';
                }
            }
            
            // 清理事件监听器（移除不必要的）
            this.cleanupEventListeners();
            
            console.log('✅ 内存清理完成');
            
        } catch (error) {
            console.warn('内存清理过程中出错:', error);
        }
    }
    
    // 清理不必要的事件监听器
    cleanupEventListeners() {
        // 移除旧的搜索建议点击事件
        const oldSuggestions = document.querySelectorAll('.suggestion-chip[data-cleanup="true"]');
        oldSuggestions.forEach(chip => {
            chip.remove();
        });
        
        // 标记当前建议以便将来清理
        const currentSuggestions = document.querySelectorAll('.suggestion-chip');
        currentSuggestions.forEach(chip => {
            chip.dataset.cleanup = 'true';
        });
    }
    
    // 获取性能报告
    getPerformanceReport() {
        if (!this.performanceOptimizer) {
            return { error: '性能优化器未启用' };
        }
        
        const baseReport = this.performanceOptimizer.getPerformanceReport();
        
        // 添加应用特定的性能指标
        const appMetrics = {
            streamingSearchEnabled: !!this.streamingEngine,
            fallbackManagerEnabled: !!this.fallbackManager,
            currentTheme: this.currentTheme,
            currentLanguage: this.i18n?.getCurrentLanguage(),
            
            // 搜索相关指标
            searchStats: this.fallbackManager?.getStats() || null,
            streamingEngineStatus: this.streamingEngine?.getStatus() || null,
            
            // DOM性能指标
            domElementCount: document.getElementsByTagName('*').length,
            activeEventListeners: this.countActiveEventListeners(),
            
            // 内存相关
            memoryInfo: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                allocated: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
            } : null
        };
        
        return {
            ...baseReport,
            application: appMetrics
        };
    }
    
    // 统计活动的事件监听器
    countActiveEventListeners() {
        let count = 0;
        
        // 统计主要交互元素
        const interactiveElements = document.querySelectorAll(
            'button, input, .suggestion-chip, .streaming-result-item, .category-card'
        );
        count += interactiveElements.length;
        
        return count;
    }
    
    // 预取相关搜索结果
    async prefetchRelatedQueries(query, language) {
        if (this.performanceOptimizer && this.performanceOptimizer.prefetcher) {
            try {
                await this.performanceOptimizer.prefetchRelatedQueries(query, language);
                console.debug('🔮 相关查询预取完成');
            } catch (error) {
                console.debug('预取失败:', error);
            }
        }
    }
    
    // 优化的Gemini API调用
    async optimizedGeminiRequest(url, options = {}) {
        if (this.performanceOptimizer) {
            try {
                return await this.performanceOptimizer.optimizedRequest(url, options);
            } catch (error) {
                console.warn('优化请求失败，回退到普通请求:', error);
                return fetch(url, options);
            }
        } else {
            return fetch(url, options);
        }
    }
    
    // 性能监控定时器
    startPerformanceMonitoring() {
        if (!this.performanceOptimizer) return;
        
        setInterval(() => {
            const report = this.getPerformanceReport();
            
            // 检查内存使用情况
            if (report.application?.memoryInfo) {
                const usedMB = parseInt(report.application.memoryInfo.used);
                if (usedMB > 100) { // 超过100MB
                    console.warn('⚠️ 内存使用过高:', usedMB + 'MB');
                    this.performMemoryCleanup();
                }
            }
            
            // 检查错误率
            if (report.requests?.errorRequests > 10) {
                console.warn('⚠️ 错误率过高，可能需要检查网络连接');
            }
            
        }, 30000); // 每30秒检查一次
    }

    // 显示通知
    showNotification(message, duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'notification purple-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 隐藏动画
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    }
    
    // 快速性能基准测试
    async runQuickPerformanceBenchmark() {
        const testQueries = ['登录问题', 'payment issue', '应用崩溃'];
        const results = {};
        const iterations = 2; // 快速测试只测试2次
        
        for (const query of testQueries) {
            const queryResults = {
                query,
                iterations: [],
                stats: {}
            };
            
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                
                try {
                    if (this.unifiedSearchEngine) {
                        let searchResults = [];
                        let hasRAG = false;
                        let hasAI = false;
                        
                        await this.unifiedSearchEngine.search(query, null, {
                            onProgress: (progress) => {
                                if (progress.stage === 'rag-semantic') hasRAG = true;
                                if (progress.stage === 'ai-enhanced') hasAI = true;
                            },
                            onComplete: (results) => {
                                searchResults = results;
                            }
                        });
                        
                        queryResults.iterations.push({
                            success: true,
                            time: Date.now() - startTime,
                            resultsCount: searchResults.length,
                            hasRAG,
                            hasAI
                        });
                        
                    }
                    
                } catch (error) {
                    queryResults.iterations.push({
                        success: false,
                        error: error.message,
                        time: Date.now() - startTime
                    });
                }
                
                // 避免缓存影响
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // 计算统计数据
            const successfulRuns = queryResults.iterations.filter(r => r.success);
            if (successfulRuns.length > 0) {
                const times = successfulRuns.map(r => r.time);
                const resultCounts = successfulRuns.map(r => r.resultsCount);
                
                queryResults.stats = {
                    avgTime: times.reduce((a, b) => a + b, 0) / times.length,
                    minTime: Math.min(...times),
                    maxTime: Math.max(...times),
                    avgResults: resultCounts.reduce((a, b) => a + b, 0) / resultCounts.length,
                    successRate: (successfulRuns.length / iterations) * 100,
                    ragUsage: successfulRuns.filter(r => r.hasRAG).length,
                    aiUsage: successfulRuns.filter(r => r.hasAI).length
                };
            }
            
            results[query] = queryResults;
        }
        
        // 总体统计
        const overallStats = this.calculateOverallTestStats(results);
        
        return {
            timestamp: new Date().toISOString(),
            queries: results,
            overallStats,
            testType: 'quick_benchmark'
        };
    }
    
    // 计算总体测试统计
    calculateOverallTestStats(results) {
        const allIterations = Object.values(results).flatMap(r => r.iterations.filter(i => i.success));
        
        if (allIterations.length === 0) {
            return { error: '没有成功的测试结果' };
        }
        
        const times = allIterations.map(i => i.time);
        const resultCounts = allIterations.map(i => i.resultsCount);
        
        return {
            totalTests: allIterations.length,
            avgSearchTime: times.reduce((a, b) => a + b, 0) / times.length,
            minSearchTime: Math.min(...times),
            maxSearchTime: Math.max(...times),
            avgResultsCount: resultCounts.reduce((a, b) => a + b, 0) / resultCounts.length,
            ragUsageRate: (allIterations.filter(i => i.hasRAG).length / allIterations.length) * 100,
            aiUsageRate: (allIterations.filter(i => i.hasAI).length / allIterations.length) * 100,
            performanceGrade: this.calculateTestPerformanceGrade(times.reduce((a, b) => a + b, 0) / times.length)
        };
    }
    
    // 性能等级计算
    calculateTestPerformanceGrade(avgTime) {
        if (avgTime <= 500) return 'A+ (优秀)';
        if (avgTime <= 1000) return 'A (良好)';
        if (avgTime <= 2000) return 'B (可接受)';
        if (avgTime <= 3000) return 'C (需要优化)';
        return 'D (急需优化)';
    }
    
    // 初始性能基准测试
    async runInitialPerformanceBenchmark() {
        try {
            console.log('📈 执行初始性能基准测试...');
            
            if (this.benchmarkManager) {
                // 运行快速基准测试
                const quickBenchmark = await this.runQuickPerformanceBenchmark();
                console.log('🏆 初始性能测试完成:', quickBenchmark.overallStats);
                
                // 存储基准结果
                this.performanceBenchmark = quickBenchmark;
                
                // 如果性能不达标，给出建议
                if (quickBenchmark.overallStats.performanceGrade.includes('D') || 
                    quickBenchmark.overallStats.performanceGrade.includes('C')) {
                    console.warn('⚠️ 初始性能测试显示需要优化');
                    this.suggestPerformanceOptimizations(quickBenchmark);
                }
            }
        } catch (error) {
            console.error('初始性能基准测试失败:', error);
        }
    }
    
    // 性能优化建议
    suggestPerformanceOptimizations(benchmark) {
        const suggestions = [];
        
        if (benchmark.overallStats.avgSearchTime > 2000) {
            suggestions.push('考虑增加缓存大小或启用更多并行处理');
        }
        
        if (benchmark.overallStats.avgResultsCount < 3) {
            suggestions.push('可能需要调整相似度阈值或改进词汇表');
        }
        
        if (benchmark.overallStats.ragUsageRate < 50) {
            suggestions.push('RAG向量搜索使用率较低，检查初始化配置');
        }
        
        if (suggestions.length > 0) {
            console.log('🔧 性能优化建议:', suggestions);
        }
    }
    
    // 运行完整性能基准测试
    async runFullPerformanceBenchmark() {
        if (!this.benchmarkManager) {
            console.warn('性能基准测试管理器不可用');
            return null;
        }
        
        console.log('🚀 开始完整性能基准测试...');
        const result = await this.benchmarkManager.runFullBenchmark();
        
        if (result.benchmark) {
            console.log('🏆 完整性能基准测试完成:');
            console.log('总体评分:', result.benchmark.overallScore);
            console.log('测试时间:', result.benchmark.totalTime + 'ms');
            console.table(result.report.details);
            
            if (result.report.recommendations.length > 0) {
                console.log('📊 优化建议:', result.report.recommendations);
            }
            
            return result;
        } else {
            console.error('完整性能基准测试失败:', result.error);
            return null;
        }
    }

    // 🆕 内存监控和自动清理系统
    startMemoryMonitoring() {
        if (!window.performance || !performance.memory) {
            console.warn('⚠️ 浏览器不支持内存监控');
            return;
        }

        // 每30秒检查一次内存使用
        this.memoryMonitorInterval = setInterval(() => {
            this.checkMemoryUsage();
        }, 30000);

        console.log('🔧 内存监控系统已启动');
    }

    // 检查内存使用情况
    checkMemoryUsage() {
        if (!performance.memory) return;

        const memoryInfo = {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit
        };

        // 内存使用率
        const usagePercent = (memoryInfo.used / memoryInfo.limit) * 100;
        
        // 内存使用超过100MB或使用率超过70%时清理
        const criticalMemory = 100 * 1024 * 1024; // 100MB
        const criticalPercent = 70;

        if (memoryInfo.used > criticalMemory || usagePercent > criticalPercent) {
            console.warn(`⚠️ 内存使用较高: ${Math.round(memoryInfo.used / (1024 * 1024))}MB (${Math.round(usagePercent)}%)`);
            this.performMemoryCleanup();
        }

        // 调试模式下记录内存使用
        if (this.debugMode) {
            console.debug(`💾 内存使用: ${Math.round(memoryInfo.used / (1024 * 1024))}MB (${Math.round(usagePercent)}%)`);
        }
    }

    // 执行内存清理
    performMemoryCleanup() {
        console.log('🧹 执行内存清理...');

        try {
            // 清理搜索缓存
            if (this.unifiedSearchEngine) {
                this.unifiedSearchEngine.clearCache?.();
            }

            // 清理流式搜索缓存
            if (this.streamingEngine) {
                this.streamingEngine.cleanup?.();
            }

            // 清理性能优化器缓存
            if (this.performanceOptimizer) {
                this.performanceOptimizer.cleanup?.();
            }

            // 清理移动端优化器
            if (this.mobileOptimizer) {
                this.mobileOptimizer.cleanup?.();
            }

            // 手动垃圾回收提示
            if (window.gc) {
                window.gc();
                console.log('🗑️ 手动垃圾回收完成');
            }

            console.log('✅ 内存清理完成');
            
            // 显示用户提示
            this.showNotification('系统已自动优化内存使用', 3000);

        } catch (error) {
            console.error('❌ 内存清理失败:', error);
        }
    }

    // 停止内存监控（在页面卸载时调用）
    stopMemoryMonitoring() {
        if (this.memoryMonitorInterval) {
            clearInterval(this.memoryMonitorInterval);
            this.memoryMonitorInterval = null;
            console.log('🔧 内存监控系统已停止');
        }
    }

    // 🆕 键盘导航辅助方法
    navigateToFirstResult() {
        const firstResult = document.querySelector('.streaming-result-item, .search-result-item');
        if (firstResult) {
            firstResult.focus();
            firstResult.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            
            // 添加键盘导航事件
            this.setupResultNavigation(firstResult);
        }
    }

    navigateToSuggestions() {
        const firstSuggestion = document.querySelector('.suggestion-chip');
        if (firstSuggestion) {
            firstSuggestion.focus();
            firstSuggestion.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    setupResultNavigation(element) {
        element.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                const nextElement = element.nextElementSibling;
                if (nextElement && (nextElement.classList.contains('streaming-result-item') || nextElement.classList.contains('search-result-item'))) {
                    nextElement.focus();
                    nextElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                const prevElement = element.previousElementSibling;
                if (prevElement && (prevElement.classList.contains('streaming-result-item') || prevElement.classList.contains('search-result-item'))) {
                    prevElement.focus();
                    prevElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                } else {
                    // 回到搜索框
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) searchInput.focus();
                }
            } else if (e.key === 'Enter') {
                e.preventDefault();
                element.click();
            }
        });
    }
}

// 应用启动
let app;

function initializeApp() {
    // 检查所有必需的依赖是否已加载
    if (typeof I18nManager === 'undefined') {
        console.warn('I18nManager not loaded, retrying...');
        setTimeout(initializeApp, 100);
        return;
    }
    
    if (typeof DataManager === 'undefined') {
        console.warn('DataManager not loaded, retrying...');
        setTimeout(initializeApp, 100);
        return;
    }
    
    if (typeof GeminiSearchAssistant === 'undefined') {
        console.warn('GeminiSearchAssistant not loaded, retrying...');
        setTimeout(initializeApp, 100);
        return;
    }
    
    if (!window.CONFIG) {
        console.warn('CONFIG not loaded, retrying...');
        setTimeout(initializeApp, 100);
        return;
    }
    
    try {
        app = new FAQApp();
        window.app = app; // 确保全局可访问
        console.log('FAQ App initialized successfully');
    } catch (error) {
        console.error('Failed to initialize FAQ App:', error);
        // 显示错误信息给用户
        document.body.innerHTML = `
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>应用初始化失败</h2>
                <p>请刷新页面重试，或检查控制台获取详细错误信息。</p>
                <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px;">刷新页面</button>
            </div>
        `;
    }
}

document.addEventListener('DOMContentLoaded', initializeApp);

// 🔧 页面卸载时的清理
window.addEventListener('beforeunload', () => {
    if (window.app) {
        window.app.stopMemoryMonitoring?.();
        console.log('🧹 应用清理完成');
    }
});
