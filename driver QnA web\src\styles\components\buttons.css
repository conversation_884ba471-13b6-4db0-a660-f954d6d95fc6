/*
 * 文件路径: src/styles/components/buttons.css
 * 文件描述: 定义了应用程序中各种按钮组件的全面样式集。它建立了一个基础按钮样式，并提供了尺寸、类型（主色、次色、轮廓、幽灵、玻璃拟态、危险、成功）和形状（圆形）的多种变体。此外，它还包含了主题切换和语言选择器等功能性按钮的样式，并处理了加载状态和响应式调整。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于在以下文件中定义的CSS变量（设计令牌）：
 *     - `src/styles/tokens/spacing.css` (用于 `padding` 和移动端间距)。
 *     - `src/styles/tokens/typography.css` (用于 `font-size` 和 `font-weight`)。
 *     - `src/styles/tokens/colors.css` (用于文本和背景颜色，包括品牌色和状态色)。
 *     - `src/styles/tokens/radius.css` (用于 `border-radius`)。
 *     - `src/styles/tokens/shadows.css` (用于 `box-shadow`)。
 *     - `src/styles/tokens/transitions.css` (用于 `transition` 属性)。
 *     - `src/styles/themes/variables.css` (用于渐变和玻璃拟态效果的颜色和模糊值)。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 为应用程序提供一套一致且视觉吸引力的交互式按钮。
 *   - 提供广泛的按钮样式和尺寸，以适应各种UI上下文。
 *   - 确保一致的悬停、激活和禁用状态。
 *   - 支持可访问性功能，如减少动画和高对比度模式。
 *   - 实现异步操作的加载指示器。
 * 关键部分/规则:
 *   - `.btn`: 基础按钮样式，定义了显示、对齐、内边距、边框、圆角、字体、光标和过渡等通用属性。
 *   - `.btn:disabled`: 禁用按钮的样式。
 *   - **尺寸变体**: `.btn--xs`, `.btn--sm`, `.btn--lg`, `.btn--xl` 调整内边距、字体大小和圆角。
 *   - **类型变体**:
 *     - `.btn--primary`: 带有渐变背景和阴影的主要行动按钮。
 *     - `.btn--secondary`: 带有纯色背景和边框的次要按钮。
 *     - `.btn--outline`: 透明背景和彩色边框的轮廓按钮。
 *     - `.btn--ghost`: 透明背景和微妙文本颜色的幽灵按钮。
 *     - `.btn--glass`: 带有半透明背景和模糊效果的玻璃拟态按钮。
 *     - `.btn--danger`, `.btn--success`: 用于破坏性/肯定性操作的按钮，具有特定的背景颜色。
 *   - **形状变体**: `.btn--circle` 用于圆形按钮，并进行尺寸调整。
 *   - **功能性按钮**: `.theme-toggle`, `.chat-toggle`, `.lang-btn` 定义了特定UI控件的样式，通常使用玻璃拟态效果。
 *   - **语言切换器 (`.language-switcher`)**: 样式化语言选择按钮的容器。
 *   - **加载状态 (`.btn--loading`)**: 添加旋转加载动画，并在加载状态时隐藏按钮文本。
 *   - **响应式优化 (`@media`)**: 针对小屏幕调整按钮内边距、字体大小和功能性按钮尺寸。
 *   - **动画 (`@keyframes spin`)**: 定义加载器的旋转动画。
 *   - **可访问性 (`@media (prefers-reduced-motion: reduce)`, `@media (prefers-contrast: high)`)**: 调整过渡和边框宽度以适应可访问性偏好。
 * 使用约定:
 *   - 按钮通常使用 `.btn` 类以及一个或多个修饰符类创建（例如，`<button class="btn btn--primary btn--lg">Click Me</button>`）。
 *   - 语言选择器等功能性按钮有其特定的类。
 */
/* 组件样式 - 按钮组件 */

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-lg);
  border: 1px solid transparent;
  border-radius: var(--radius-button);
  font-size: var(--font-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-button);
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮尺寸变体 */
.btn--xs {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-xs);
  border-radius: var(--radius-sm);
}

.btn--sm {
  padding: var(--space-xs) var(--space-md);
  font-size: var(--font-sm);
  border-radius: var(--radius-md);
}

.btn--lg {
  padding: var(--space-md) var(--space-2xl);
  font-size: var(--font-lg);
  border-radius: var(--radius-xl);
}

.btn--xl {
  padding: var(--space-lg) var(--space-3xl);
  font-size: var(--font-xl);
  border-radius: var(--radius-xl);
}

/* 主要按钮样式 */
.btn--primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-button);
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn--primary:active {
  transform: translateY(-1px);
}

/* 次要按钮样式 */
.btn--secondary {
  background: var(--surface-color);
  color: var(--text-primary);
  border-color: var(--border-color);
  box-shadow: var(--shadow-button);
}

.btn--secondary:hover {
  background: var(--background-secondary);
  border-color: var(--primary-500);
  transform: translateY(-1px);
}

/* 轮廓按钮样式 */
.btn--outline {
  background: transparent;
  color: var(--primary-600);
  border-color: var(--primary-500);
}

.btn--outline:hover {
  background: var(--primary-50);
  color: var(--primary-700);
  border-color: var(--primary-600);
}

[data-theme="dark"] .btn--outline:hover {
  background: var(--primary-900);
  color: var(--primary-300);
}

/* 幽灵按钮样式 */
.btn--ghost {
  background: transparent;
  color: var(--text-secondary);
  border-color: transparent;
}

.btn--ghost:hover {
  background: var(--background-secondary);
  color: var(--text-primary);
}

/* 玻璃拟态按钮样式 */
.btn--glass {
  background: var(--glass-background);
  color: var(--text-inverse);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.btn--glass:hover {
  background: var(--glass-background-intense);
  border-color: var(--glass-border-intense);
  transform: translateY(-1px);
}

/* 危险按钮样式 */
.btn--danger {
  background: var(--danger-color);
  color: var(--text-inverse);
}

.btn--danger:hover {
  background: color-mix(in srgb, var(--danger-color) 90%, black 10%);
  transform: translateY(-1px);
}

/* 成功按钮样式 */
.btn--success {
  background: var(--success-color);
  color: var(--text-inverse);
}

.btn--success:hover {
  background: color-mix(in srgb, var(--success-color) 90%, black 10%);
  transform: translateY(-1px);
}

/* 圆形按钮样式 */
.btn--circle {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: var(--radius-full);
}

.btn--circle.btn--sm {
  width: 32px;
  height: 32px;
}

.btn--circle.btn--lg {
  width: 48px;
  height: 48px;
}

/* 功能按钮样式 */
.theme-toggle,
.chat-toggle,
.lang-btn {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border-subtle);
  background: var(--glass-background);
  color: var(--text-inverse);
  cursor: pointer;
  transition: var(--transition-button);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-sm);
  font-weight: var(--font-weight-medium);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-toggle:hover,
.chat-toggle:hover,
.lang-btn:hover {
  background: var(--glass-background-intense);
  border-color: var(--glass-border);
  transform: translateY(-1px);
}

.theme-toggle:active,
.chat-toggle:active,
.lang-btn:active {
  transform: translateY(0);
}

/* 语言切换器特定样式 */
.language-switcher {
  display: flex;
  gap: var(--space-xs);
  background: var(--glass-background-subtle);
  padding: var(--space-xs);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border-subtle);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.lang-btn {
  width: 28px;
  height: 28px;
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  border: 1px solid transparent;
  background: transparent;
}

.lang-btn.active {
  background: var(--glass-background-intense);
  border-color: var(--glass-border);
  color: var(--text-inverse);
}

/* 图标按钮样式 */
.btn--icon-only {
  padding: var(--space-sm);
  width: auto;
  height: auto;
  aspect-ratio: 1;
}

/* 加载状态 */
.btn--loading {
  color: transparent;
  pointer-events: none;
}

.btn--loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .btn {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-sm);
  }
  
  .btn--lg {
    padding: var(--space-sm) var(--space-xl);
    font-size: var(--font-base);
  }
  
  .theme-toggle,
  .chat-toggle {
    width: 32px;
    height: 32px;
    font-size: var(--font-xs);
  }
  
  .lang-btn {
    width: 24px;
    height: 24px;
    font-size: var(--font-xs);
  }
}

@media (max-width: 320px) {
  .theme-toggle,
  .chat-toggle,
  .lang-btn {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
}

/* 动画关键帧 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: var(--transition-colors);
  }
  
  .btn:hover,
  .btn--primary:hover,
  .btn--secondary:hover,
  .btn--glass:hover,
  .theme-toggle:hover,
  .chat-toggle:hover,
  .lang-btn:hover {
    transform: none;
  }
  
  .btn--loading::after {
    animation: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }
  
  .btn--outline,
  .btn--ghost {
    border-width: 2px;
  }
  
  .btn:focus-visible {
    outline: 3px solid var(--primary-500);
    outline-offset: 2px;
  }
}

/* 返回按钮样式 */
.back-btn,
.back-to-home-btn {
  background: var(--glass-background);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-size: var(--font-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-button);
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
}

.back-btn:hover,
.back-to-home-btn:hover {
  background: var(--glass-background-intense);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.back-btn:active,
.back-to-home-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.back-btn:focus-visible,
.back-to-home-btn:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
  margin: 0;
  font-size: var(--font-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 面包屑导航样式 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
  padding: var(--space-sm) var(--space-md);
  background: var(--glass-background);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.breadcrumb-item {
  background: none;
  border: none;
  color: var(--primary-600);
  cursor: pointer;
  font-size: var(--font-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  transition: var(--transition-button);
}

.breadcrumb-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
}

.breadcrumb-separator {
  color: var(--text-secondary);
  font-size: var(--font-sm);
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 移动设备优化 */
@media (max-width: 768px) {
  .back-btn,
  .back-to-home-btn {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-xs);
    min-height: 36px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
  
  .page-header h2 {
    font-size: var(--font-lg);
    margin: 0;
  }
  
  .breadcrumb {
    flex-wrap: wrap;
    gap: var(--space-xs);
  }
  
  .breadcrumb-item {
    font-size: var(--font-xs);
    padding: var(--space-xs);
  }
}