/*
 * 文件路径: src/styles/themes/variables.css
 * 文件描述: 定义了应用程序中与主题相关的通用变量，特别是聚焦于“玻璃拟态”（glassmorphism）效果和各种渐变。这些变量作为不同主题（亮色/暗色）的基础，本身不直接绑定到特定主题，而是为主题相关的样式提供可重用的值。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 依赖于 `src/styles/tokens/colors.css` 中定义的颜色变量（例如：`--primary-500`, `--secondary-color`）。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 提供一个集中定义可重用CSS变量的位置，这些变量在不同主题中通用，或属于特定的设计美学（如玻璃拟态）。
 *   - 确保在整个UI中应用玻璃拟态效果和渐变的一致性。
 *   - 作为核心设计令牌（颜色、间距）和特定主题实现之间的中间层。
 * 关键部分:
 *   - `--glass-background`, `--glass-background-intense`, `--glass-background-subtle`: 定义了具有不同透明度的玻璃拟态背景效果的 `rgba` 值。
 *   - `--glass-border`, `--glass-border-intense`, `--glass-border-subtle`: 定义了具有不同透明度的玻璃拟态边框效果的 `rgba` 值。
 *   - `--glass-blur`, `--glass-blur-intense`, `--glass-blur-subtle`: 定义了玻璃拟态效果的模糊值。
 *   - `--gradient-primary`, `--gradient-secondary`, `--gradient-header`: 使用品牌主色和辅助色定义了线性渐变。
 *   - `--gradient-text`: 定义了专门用于文本元素的线性渐变。
 * 使用约定:
 *   - 其他CSS文件，特别是特定主题文件（`light.css`, `dark.css`）或组件样式，可以使用这些变量来应用一致的玻璃拟态效果和渐变。
 */
/* 主题系统 - 主题变量 */

/* 玻璃拟态效果变量 */
:root {
  /* 玻璃拟态基础变量 */
  --glass-background: rgba(255, 255, 255, 0.1);
  --glass-background-intense: rgba(255, 255, 255, 0.15);
  --glass-background-subtle: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-border-intense: rgba(255, 255, 255, 0.3);
  --glass-border-subtle: rgba(255, 255, 255, 0.1);

  /* 模糊强度 */
  --glass-blur: 20px;
  --glass-blur-intense: 40px;
  --glass-blur-subtle: 10px;

  /* 渐变背景 */
  --gradient-primary: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  --gradient-secondary: linear-gradient(135deg, var(--primary-400), var(--secondary-color));
  --gradient-header: linear-gradient(135deg, 
    rgba(168, 85, 247, 0.9), 
    rgba(184, 61, 186, 0.9)
  );
  
  /* 文本渐变 */
  --gradient-text: linear-gradient(135deg, var(--primary-600), var(--secondary-color));
}