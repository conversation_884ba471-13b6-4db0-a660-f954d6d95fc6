/*
 * 文件路径: src/styles/tokens/shadows.css
 * 文件描述: 定义了应用程序的阴影系统，通过CSS自定义属性（变量）提供了一致的盒阴影值。它包含了从微妙到显著的阴影等级，以及一个内阴影。此外，还为常见的UI组件提供了语义化的阴影定义，并为品牌和状态指示器提供了彩色阴影。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 依赖于 `src/styles/tokens/colors.css` 中定义的 `--shadow-color` 变量。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 集中管理所有阴影定义，确保应用程序中UI元素深度感和视觉一致性。
 *   - 通过从单一来源应用阴影效果的更改，简化设计更新。
 *   - 促进语义化使用，使开发者能够应用有意义的阴影样式（例如：`--shadow-card`），而不是单独的 `box-shadow` 值。
 *   - 提供彩色阴影，增强品牌识别度并突出状态。
 * 关键部分:
 *   - `--shadow-xs` 到 `--shadow-2xl`: 定义了可伸缩的外部盒阴影系统。
 *   - `--shadow-inner`: 定义了一个内盒阴影。
 *   - `--shadow-button`, `--shadow-card`, `--shadow-modal`, `--shadow-dropdown`, `--shadow-floating`: 语义化定义，映射到基本阴影值，为特定组件类型提供一致的样式。
 *   - `--shadow-primary`, `--shadow-success`, `--shadow-warning`, `--shadow-danger`: 定义了彩色阴影，使用从颜色调色板派生的 `rgba` 值，用于品牌和状态指示器。
 * 使用约定:
 *   - 其他CSS文件应通过 `var()` 函数引用这些阴影变量（例如：`box-shadow: var(--shadow-card);`）来应用一致的阴影效果。
 */
/* 设计令牌 - 阴影系统 */
:root {
  /* 阴影等级系统 */
  --shadow-xs: 0 1px 2px 0 var(--shadow-color);
  --shadow-sm: 0 1px 3px 0 var(--shadow-color), 0 1px 2px -1px var(--shadow-color);
  --shadow-md: 0 4px 6px -1px var(--shadow-color), 0 2px 4px -2px var(--shadow-color);
  --shadow-lg: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -4px var(--shadow-color);
  --shadow-xl: 0 20px 25px -5px var(--shadow-color), 0 8px 10px -6px var(--shadow-color);
  --shadow-2xl: 0 25px 50px -12px var(--shadow-color);
  --shadow-inner: inset 0 2px 4px 0 var(--shadow-color);

  /* 语义化阴影 */
  --shadow-button: var(--shadow-sm);
  --shadow-card: var(--shadow-md);
  --shadow-modal: var(--shadow-2xl);
  --shadow-dropdown: var(--shadow-lg);
  --shadow-floating: var(--shadow-xl);

  /* 彩色阴影 */
  --shadow-primary: 0 12px 40px rgba(168, 85, 247, 0.3);
  --shadow-success: 0 12px 40px rgba(16, 185, 129, 0.3);
  --shadow-warning: 0 12px 40px rgba(245, 158, 11, 0.3);
  --shadow-danger: 0 12px 40px rgba(239, 68, 68, 0.3);
}