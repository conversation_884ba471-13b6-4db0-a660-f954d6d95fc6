// 移动端交互优化管理器
// 专门处理触摸反馈、手势控制、虚拟键盘适配

class MobileInteractionManager {
    constructor(config = {}) {
        // 支持传递config对象或app实例（向后兼容）
        if (config.app) {
            this.app = config.app;
            this.config = { ...config };
        } else {
            // 通过全局变量获取app实例
            this.app = window.app || window.faqApp;
            this.config = config;
        }
        
        // 合并默认配置
        this.config = {
            enableHapticFeedback: window.CONFIG?.search?.streaming?.ui?.enableTouchFeedback ?? true,
            enableCancelGesture: window.CONFIG?.search?.streaming?.ui?.enableCancelGesture ?? true,
            swipeThreshold: 50, // 手势滑动阈值
            longPressThreshold: 500, // 长按阈值
            doubleTapThreshold: 300, // 双击阈值
            ...this.config
        };

        this.touchState = {
            startX: 0,
            startY: 0,
            startTime: 0,
            isLongPress: false,
            lastTap: 0,
            activeElement: null,
            userHasInteracted: false // 追踪用户交互状态
        };

        this.gestureHandlers = new Map();
        this.initialized = false;
        this.init();
    }

    // 初始化移动端交互
    init() {
        console.log('📱 初始化移动端交互管理器...');

        // 设置用户交互检测
        this.setupUserActivationTracking();
        
        // 设置基础触摸事件
        this.setupTouchEvents();
        
        // 设置流式搜索的手势控制
        this.setupStreamingGestures();
        
        // 设置虚拟键盘适配
        this.setupVirtualKeyboardHandling();
        
        // 设置设备方向变化处理
        this.setupOrientationHandling();
        
        // 设置系统级交互
        this.setupSystemInteractions();

        console.log('✅ 移动端交互管理器初始化完成');
        this.initialized = true;
    }

    // 设置用户交互激活检测
    setupUserActivationTracking() {
        // 监听首次用户交互以启用触觉反馈
        const trackUserInteraction = () => {
            if (!this.touchState.userHasInteracted) {
                this.touchState.userHasInteracted = true;
                console.log('📱 用户首次交互检测，启用触觉反馈');
                
                // 移除一次性事件监听器
                document.removeEventListener('touchstart', trackUserInteraction, { passive: true });
                document.removeEventListener('click', trackUserInteraction);
                document.removeEventListener('keydown', trackUserInteraction);
            }
        };
        
        // 监听多种交互方式
        document.addEventListener('touchstart', trackUserInteraction, { passive: true });
        document.addEventListener('click', trackUserInteraction);
        document.addEventListener('keydown', trackUserInteraction);
    }

    // 添加initialize方法作为init的别名（兼容app.js调用）
    initialize() {
        // 如果已经初始化过，直接返回
        if (this.initialized) {
            return this;
        }
        return this.init();
    }

    // 添加app.js期望的事件监听器方法
    addLongPressListener(element, callback) {
        if (!element || !callback) return;
        
        const handler = (e) => {
            if (e.target === element || element.contains(e.target)) {
                callback(e);
            }
        };
        
        this.gestureHandlers.set(`longpress_${element.id || Math.random()}`, handler);
        // 长按功能已经在handleLongPress中实现
        console.log('📱 长按监听器已添加');
    }

    addDoubleTapListener(element, callback) {
        if (!element || !callback) return;
        
        const handler = (e) => {
            if (e.target === element || element.contains(e.target)) {
                callback(e);
            }
        };
        
        this.gestureHandlers.set(`doubletap_${element.id || Math.random()}`, handler);
        // 双击功能已经在handleDoubleTap中实现
        console.log('📱 双击监听器已添加');
    }

    addSwipeListener(element, callback) {
        if (!element || !callback) return;
        
        const handler = (e) => {
            if (e.target === element || element.contains(e.target)) {
                callback(e);
            }
        };
        
        this.gestureHandlers.set(`swipe_${element.id || Math.random()}`, handler);
        // 滑动功能已经在setupStreamingGestures中实现
        console.log('📱 滑动监听器已添加');
    }

    // 触觉反馈方法别名（兼容app.js调用）
    triggerHapticFeedback(type = 'light') {
        return this.provideTactileFeedback(type);
    }

    // 设置基础触摸事件
    setupTouchEvents() {
        // 为所有可交互元素添加触摸反馈
        const interactiveSelectors = [
            '.streaming-result-item',
            '.suggestion-chip', 
            '.streaming-cancel-btn',
            '.action-btn',
            '.category-card',
            'button'
        ].join(', ');

        // 使用事件委托提高性能
        document.addEventListener('touchstart', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && e.target.closest) {
                const element = e.target.closest(interactiveSelectors);
                if (element) {
                    this.handleTouchStart(e, element);
                }
            }
        }, { passive: false });

        document.addEventListener('touchend', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && e.target.closest) {
                const element = e.target.closest(interactiveSelectors);
                if (element) {
                    this.handleTouchEnd(e, element);
                }
            }
        }, { passive: true });

        document.addEventListener('touchcancel', (e) => {
            this.handleTouchCancel(e);
        }, { passive: true });
    }

    // 处理触摸开始
    handleTouchStart(event, element) {
        const touch = event.touches[0];
        
        this.touchState = {
            startX: touch.clientX,
            startY: touch.clientY,
            startTime: Date.now(),
            isLongPress: false,
            activeElement: element
        };

        // 添加触摸视觉反馈
        this.addTouchFeedback(element, 'active');

        // 轻微的触觉反馈
        this.provideTactileFeedback('light');

        // 设置长按检测
        this.setupLongPressDetection(element);

        // 检测双击
        this.detectDoubleTap(element);
    }

    // 处理触摸结束
    handleTouchEnd(event, element) {
        const touchDuration = Date.now() - this.touchState.startTime;

        // 移除触摸视觉反馈
        setTimeout(() => {
            this.removeTouchFeedback(element, 'active');
        }, 150);

        // 根据触摸时长提供不同反馈
        if (touchDuration > this.config.longPressThreshold) {
            this.handleLongPress(element);
        } else {
            this.handleTap(element);
        }

        this.touchState.activeElement = null;
    }

    // 处理触摸取消
    handleTouchCancel(event) {
        if (this.touchState.activeElement) {
            this.removeTouchFeedback(this.touchState.activeElement, 'active');
            this.touchState.activeElement = null;
        }
    }

    // 添加触摸视觉反馈
    addTouchFeedback(element, type) {
        element.classList.add(`touch-${type}`);
        
        // 添加临时CSS样式
        if (type === 'active') {
            const originalTransform = element.style.transform;
            element.style.transform = `${originalTransform} scale(0.98)`;
            element.style.transition = 'transform 0.1s ease';
        }
    }

    // 移除触摸视觉反馈
    removeTouchFeedback(element, type) {
        element.classList.remove(`touch-${type}`);
        
        if (type === 'active') {
            element.style.transform = '';
            element.style.transition = '';
        }
    }

    // 设置长按检测
    setupLongPressDetection(element) {
        setTimeout(() => {
            if (this.touchState.activeElement === element) {
                this.touchState.isLongPress = true;
                this.handleLongPress(element);
            }
        }, this.config.longPressThreshold);
    }

    // 检测双击
    detectDoubleTap(element) {
        const now = Date.now();
        const timeSinceLastTap = now - this.touchState.lastTap;

        if (timeSinceLastTap < this.config.doubleTapThreshold) {
            this.handleDoubleTap(element);
        }

        this.touchState.lastTap = now;
    }

    // 处理点击
    handleTap(element) {
        this.provideTactileFeedback('medium');
        
        // 添加点击波纹效果
        this.createRippleEffect(element, this.touchState.startX, this.touchState.startY);
    }

    // 处理长按
    handleLongPress(element) {
        console.log('👆 长按检测:', element.className);
        
        this.provideTactileFeedback('heavy');
        
        // 为不同类型的元素提供不同的长按功能
        if (element.classList.contains('streaming-result-item')) {
            this.showResultQuickActions(element);
        } else if (element.classList.contains('suggestion-chip')) {
            this.showSuggestionOptions(element);
        }
    }

    // 处理双击
    handleDoubleTap(element) {
        console.log('👆 双击检测:', element.className);
        
        this.provideTactileFeedback('success');
        
        // 双击快速操作
        if (element.classList.contains('streaming-result-item')) {
            const questionId = element.dataset.questionId;
            if (questionId) {
                this.app.showQuestion(questionId);
                this.app.hideStreamingContainer();
            }
        }
    }

    // 创建点击波纹效果
    createRippleEffect(element, x, y) {
        const ripple = document.createElement('div');
        ripple.className = 'touch-ripple';
        
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const left = x - rect.left - size / 2;
        const top = y - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${left}px;
            top: ${top}px;
            background: radial-gradient(circle, rgba(168, 85, 247, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            animation: ripple 0.6s ease-out;
        `;
        
        // 确保元素有相对定位
        const position = getComputedStyle(element).position;
        if (position === 'static') {
            element.style.position = 'relative';
        }
        
        element.appendChild(ripple);
        
        // 动画完成后移除
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    // 设置流式搜索的手势控制
    setupStreamingGestures() {
        let touchStartX = 0;
        let touchStartY = 0;
        let touchStartTime = 0;
        let isSwiping = false;

        document.addEventListener('touchstart', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (!e.target || e.target.nodeType !== Node.ELEMENT_NODE || !e.target.closest) return;
            const container = e.target.closest('#streamingSearchContainer');
            if (!container) return;

            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            touchStartTime = Date.now();
            isSwiping = false; // 重置滑动状态
        }, { passive: true });

        document.addEventListener('touchmove', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (!e.target || e.target.nodeType !== Node.ELEMENT_NODE || !e.target.closest) return;
            const container = e.target.closest('#streamingSearchContainer');
            if (!container || !this.config.enableCancelGesture) return;

            const isStreaming = container.classList.contains('active') && container.classList.contains('streaming');
            if (!isStreaming) return;

            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const deltaX = currentX - touchStartX;
            const deltaY = currentY - touchStartY;

            // 如果已经开始滑动，则继续阻止默认行为
            if (isSwiping) {
                e.preventDefault();
                return;
            }

            // 只有当垂直移动显著大于水平移动时，才考虑为垂直滑动
            if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 10) {
                isSwiping = true;
                e.preventDefault(); // 开始滑动，阻止滚动
            }
            
        }, { passive: false });

        document.addEventListener('touchend', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (!e.target || e.target.nodeType !== Node.ELEMENT_NODE || !e.target.closest) {
                isSwiping = false;
                return;
            }
            const container = e.target.closest('#streamingSearchContainer');
            if (!container || !isSwiping) {
                isSwiping = false;
                return;
            }

            const touchEndY = e.changedTouches[0].clientY;
            const deltaY = touchEndY - touchStartY;
            const deltaTime = Date.now() - touchStartTime;

            // 快速向上滑动取消搜索
            if (deltaY < -this.config.swipeThreshold && deltaTime < 500) {
                this.handleSwipeUp();
            }
            
            // 向下滑动隐藏结果
            if (deltaY > this.config.swipeThreshold * 1.5 && deltaTime < 500) {
                this.handleSwipeDown();
            }

            isSwiping = false; // 手势结束，重置状态
        }, { passive: true });
    }

    // 处理向上滑动（取消搜索）
    handleSwipeUp() {
        console.log('👆 向上滑动 - 取消搜索');
        this.provideTactileFeedback('warning');
        this.app.cancelStreamingSearch();
    }

    // 处理向下滑动（隐藏结果）
    handleSwipeDown() {
        console.log('👆 向下滑动 - 隐藏结果');
        this.provideTactileFeedback('light');
        this.app.hideStreamingContainer();
    }

    // 设置虚拟键盘处理
    setupVirtualKeyboardHandling() {
        let initialViewportHeight = window.innerHeight;
        let isKeyboardOpen = false;

        // 监听视口高度变化
        const handleResize = () => {
            const currentHeight = window.innerHeight;
            const heightDiff = initialViewportHeight - currentHeight;
            
            if (heightDiff > 150 && !isKeyboardOpen) {
                // 键盘打开
                isKeyboardOpen = true;
                this.handleKeyboardOpen(heightDiff);
            } else if (heightDiff < 50 && isKeyboardOpen) {
                // 键盘关闭
                isKeyboardOpen = false;
                this.handleKeyboardClose();
            }
        };

        window.addEventListener('resize', handleResize);
        
        // 监听输入框焦点
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('focus', () => {
                setTimeout(() => {
                    if (isKeyboardOpen) {
                        this.adjustLayoutForKeyboard();
                    }
                }, 300);
            });

            searchInput.addEventListener('blur', () => {
                setTimeout(() => {
                    if (!isKeyboardOpen) {
                        this.resetLayoutForKeyboard();
                    }
                }, 100);
            });
        }
    }

    // 处理键盘打开
    handleKeyboardOpen(keyboardHeight) {
        console.log('⌨️ 虚拟键盘打开，高度:', keyboardHeight);
        
        document.body.classList.add('keyboard-open');
        
        // 调整流式搜索容器高度
        const container = document.getElementById('streamingSearchContainer');
        if (container) {
            const maxHeight = Math.max(window.innerHeight - 200, 200);
            container.style.maxHeight = `${maxHeight}px`;
        }
        
        // 确保搜索输入框可见
        this.ensureSearchInputVisible();
    }

    // 处理键盘关闭
    handleKeyboardClose() {
        console.log('⌨️ 虚拟键盘关闭');
        
        document.body.classList.remove('keyboard-open');
        
        // 恢复容器高度
        const container = document.getElementById('streamingSearchContainer');
        if (container) {
            container.style.maxHeight = '';
        }
    }

    // 调整键盘布局
    adjustLayoutForKeyboard() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }

    // 重置键盘布局
    resetLayoutForKeyboard() {
        // 恢复正常滚动位置
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // 确保搜索输入框可见
    ensureSearchInputVisible() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            const rect = searchInput.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            
            if (rect.bottom > viewportHeight || rect.top < 0) {
                searchInput.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        }
    }

    // 设置设备方向变化处理
    setupOrientationHandling() {
        const handleOrientationChange = () => {
            setTimeout(() => {
                console.log('📱 设备方向改变');
                
                // 重新计算容器尺寸
                const container = document.getElementById('streamingSearchContainer');
                if (container && container.classList.contains('active')) {
                    this.adjustContainerForOrientation();
                }
                
                // 确保搜索输入框仍然可见
                this.ensureSearchInputVisible();
            }, 500); // 等待方向变化完成
        };

        window.addEventListener('orientationchange', handleOrientationChange);
        screen.orientation?.addEventListener('change', handleOrientationChange);
    }

    // 调整容器以适应方向变化
    adjustContainerForOrientation() {
        const container = document.getElementById('streamingSearchContainer');
        if (!container) return;

        const isLandscape = window.innerWidth > window.innerHeight;
        const maxHeight = isLandscape ? '60vh' : '70vh';
        
        container.style.maxHeight = maxHeight;
        
        // 横屏时调整搜索建议布局
        const suggestionsContent = container.querySelector('.suggestions-content');
        if (suggestionsContent) {
            if (isLandscape) {
                suggestionsContent.style.flexDirection = 'row';
                suggestionsContent.style.flexWrap = 'wrap';
            } else {
                suggestionsContent.style.flexDirection = '';
                suggestionsContent.style.flexWrap = '';
            }
        }
    }

    // 设置系统级交互
    setupSystemInteractions() {
        // 阻止默认的长按菜单（只在特定元素上）
        document.addEventListener('contextmenu', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && e.target.closest) {
                if (e.target.closest('.streaming-result-item, .suggestion-chip, #searchInput')) {
                    e.preventDefault();
                }
            }
        });

        // 阻止双击缩放（只在特定交互元素上，不影响滚动）
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (e) => {
            const now = Date.now();
            if (now - lastTouchEnd <= 300) {
                // 这是双击
                // 防御性检查：确保 e.target 存在且是 DOM 元素
                if (e.target && e.target.nodeType === Node.ELEMENT_NODE && e.target.closest) {
                    if (e.target.closest('.streaming-result-item, .suggestion-chip, #searchInput')) {
                        e.preventDefault();
                    }
                }
            }
            lastTouchEnd = now;
        });

        // 防止意外选择文本（只在特定元素上）
        document.addEventListener('selectstart', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && e.target.closest) {
                if (e.target.closest('.streaming-result-item, .suggestion-chip, #searchInput')) {
                    e.preventDefault();
                }
            }
        });
    }

    // 显示结果快速操作
    showResultQuickActions(element) {
        const questionId = element.dataset.questionId;
        if (!questionId) return;

        // 创建快速操作菜单
        const menu = document.createElement('div');
        menu.className = 'quick-actions-menu';
        menu.innerHTML = `
            <div class="quick-action" data-action="open">
                <span class="action-icon">👁️</span>
                <span class="action-text">查看详情</span>
            </div>
            <div class="quick-action" data-action="share">
                <span class="action-icon">📤</span>
                <span class="action-text">分享</span>
            </div>
            <div class="quick-action" data-action="bookmark">
                <span class="action-icon">🔖</span>
                <span class="action-text">收藏</span>
            </div>
        `;

        // 添加样式
        menu.style.cssText = `
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            padding: 8px;
            display: flex;
            gap: 4px;
            animation: quickMenuAppear 0.3s ease;
        `;

        // 添加到元素
        element.style.position = 'relative';
        element.appendChild(menu);

        // 绑定事件
        menu.addEventListener('click', (e) => {
            // 防御性检查：确保 e.target 存在且是 DOM 元素
            let action = null;
            if (e.target && e.target.nodeType === Node.ELEMENT_NODE && e.target.closest) {
                const actionElement = e.target.closest('.quick-action');
                action = actionElement?.dataset.action;
            }
            if (action) {
                this.handleQuickAction(action, questionId);
                menu.remove();
            }
        });

        // 3秒后自动移除
        setTimeout(() => {
            if (menu.parentNode) {
                menu.remove();
            }
        }, 3000);
    }

    // 处理快速操作
    handleQuickAction(action, questionId) {
        this.provideTactileFeedback('success');
        
        switch (action) {
            case 'open':
                this.app.showQuestion(questionId);
                this.app.hideStreamingContainer();
                break;
            case 'share':
                this.shareQuestion(questionId);
                break;
            case 'bookmark':
                this.bookmarkQuestion(questionId);
                break;
        }
    }

    // 分享问题
    shareQuestion(questionId) {
        if (navigator.share) {
            navigator.share({
                title: 'GoMyHire FAQ',
                text: `查看这个常见问题：${questionId}`,
                url: `${window.location.origin}${window.location.pathname}?q=${questionId}`
            });
        } else {
            // 复制链接
            const url = `${window.location.origin}${window.location.pathname}?q=${questionId}`;
            navigator.clipboard.writeText(url).then(() => {
                this.app.showNotification('链接已复制', 2000);
            });
        }
    }

    // 收藏问题
    bookmarkQuestion(questionId) {
        let bookmarks = JSON.parse(localStorage.getItem('faq-bookmarks') || '[]');
        
        if (!bookmarks.includes(questionId)) {
            bookmarks.push(questionId);
            localStorage.setItem('faq-bookmarks', JSON.stringify(bookmarks));
            this.app.showNotification('已添加到收藏', 2000);
        } else {
            this.app.showNotification('已经在收藏中', 2000);
        }
    }

    // 显示建议选项
    showSuggestionOptions(element) {
        const suggestion = element.dataset.suggestion;
        if (!suggestion) return;

        // 简单的选项提示
        const tooltip = document.createElement('div');
        tooltip.className = 'suggestion-tooltip';
        tooltip.textContent = '长按以添加到常用搜索';
        tooltip.style.cssText = `
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1001;
        `;

        element.style.position = 'relative';
        element.appendChild(tooltip);

        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.remove();
            }
        }, 2000);

        // 添加到常用搜索
        this.addToFrequentSearches(suggestion);
    }

    // 添加到常用搜索
    addToFrequentSearches(query) {
        let frequent = JSON.parse(localStorage.getItem('frequent-searches') || '[]');
        
        // 移除已存在的项目
        frequent = frequent.filter(item => item.query !== query);
        
        // 添加到开头
        frequent.unshift({
            query: query,
            count: 1,
            lastUsed: Date.now()
        });

        // 保持最多10个
        frequent = frequent.slice(0, 10);
        
        localStorage.setItem('frequent-searches', JSON.stringify(frequent));
        this.app.showNotification('已添加到常用搜索', 2000);
    }

    // 提供触觉反馈
    provideTactileFeedback(type = 'light') {
        // 检查基础条件
        if (!this.config.enableHapticFeedback || !navigator.vibrate) return;
        
        // 检查用户交互状态 - Chrome要求用户激活后才能使用navigator.vibrate
        if (!this.touchState.userHasInteracted) {
            console.debug('📱 触觉反馈被跳过：等待用户首次交互');
            return;
        }

        const patterns = {
            light: [10],
            medium: [30],
            heavy: [50],
            success: [10, 50, 10],
            warning: [100, 50, 100],
            error: [50, 100, 50, 100, 50]
        };

        const pattern = patterns[type] || patterns.light;
        
        try {
            navigator.vibrate(pattern);
        } catch (error) {
            console.debug('📱 触觉反馈调用失败:', error.message);
            // 静默失败，不影响用户体验
        }
    }

    // 获取移动端状态
    getMobileStatus() {
        return {
            isTouch: 'ontouchstart' in window,
            isKeyboardOpen: document.body.classList.contains('keyboard-open'),
            orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait',
            viewportSize: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            supportedFeatures: {
                vibration: !!navigator.vibrate,
                share: !!navigator.share,
                clipboard: !!navigator.clipboard,
                orientation: !!screen.orientation
            }
        };
    }

    // 清理资源
    cleanup() {
        // 移除所有触摸反馈类
        document.querySelectorAll('.touch-active').forEach(el => {
            el.classList.remove('touch-active');
        });

        // 清理快速操作菜单
        document.querySelectorAll('.quick-actions-menu').forEach(menu => {
            menu.remove();
        });

        // 重置键盘状态
        document.body.classList.remove('keyboard-open');
    }
}

// 添加CSS动画（如果不存在）
if (!document.getElementById('mobile-interaction-styles')) {
    const styles = document.createElement('style');
    styles.id = 'mobile-interaction-styles';
    styles.textContent = `
        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        @keyframes quickMenuAppear {
            0% {
                opacity: 0;
                transform: translateX(-50%) scale(0.8);
            }
            100% {
                opacity: 1;
                transform: translateX(-50%) scale(1);
            }
        }

        .quick-actions-menu {
            animation: quickMenuAppear 0.3s ease !important;
        }

        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s ease;
            min-width: 60px;
        }

        .quick-action:hover {
            background: rgba(168, 85, 247, 0.1);
        }

        .action-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .action-text {
            font-size: 10px;
            color: #6b7280;
            text-align: center;
        }

        /* 键盘打开时的样式调整 */
        .keyboard-open .streaming-search-container {
            max-height: calc(100vh - 200px);
        }

        .keyboard-open .main-content {
            padding-bottom: 0;
        }
    `;
    document.head.appendChild(styles);
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileInteractionManager;
} else {
    window.MobileInteractionManager = MobileInteractionManager;
}