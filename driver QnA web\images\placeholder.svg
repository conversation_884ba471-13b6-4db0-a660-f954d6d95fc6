<svg xmlns="http://www.w3.org/2000/svg" width="1200" height="675" viewBox="0 0 1200 675" role="img" aria-label="Image placeholder">
  <defs>
    <linearGradient id="g" x1="0" x2="1" y1="0" y2="1">
      <stop offset="0%" stop-color="#f2f2f2"/>
      <stop offset="100%" stop-color="#e3e3e3"/>
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#g)"/>
  <g fill="none" stroke="#c8c8c8" stroke-width="4">
    <rect x="60" y="60" width="1080" height="555" rx="12"/>
    <path d="M120 555 L420 300 L660 480 L900 360 L1080 510"/>
    <circle cx="330" cy="270" r="36"/>
  </g>
  <g font-family="Segoe UI, Roboto, Helvetica, Arial, sans-serif" fill="#666">
    <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-size="32">Image not available</text>
  </g>
</svg>
