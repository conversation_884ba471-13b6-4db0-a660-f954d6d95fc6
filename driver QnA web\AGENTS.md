# Repository Guidelines

## Project Structure & Module Organization
- `index.html`: Single-page entry; loads all modules under `src/`.
- `src/core/`: App shell and infrastructure (`app.js`, `data.js`, `i18n.js`, `config.js`, `*-env-loader.js`).
- `src/components/`: UI widgets (e.g., `floating-chat.js`).
- `src/search/`: AI integration and search helpers (`gemini-assistant.js`).
- `src/styles/`: Global, tokens, themes, and component CSS.
- `tests/`: Browser-based test pages (`tests/test-search.html`).
- `tools/`: Content/restructuring helpers (node-less scripts run in browser/console).
- `docs/`: Deployment and technical notes. Assets in `images/`.

## Build, Test, and Development Commands
- Run locally (static):
  - Python: `python -m http.server 8080` then open `http://localhost:8080/`.
  - Or open `index.html` directly in a browser.
- Quick test: open `tests/test-search.html` and use DevTools console to watch logs.
- Netlify: configured via `netlify.toml`; set environment variables there.

## Coding Style & Naming Conventions
- JavaScript: ES6+, semicolons required, trailing commas avoided. Functions/vars `camelCase`; classes `PascalCase`.
- Filenames: `kebab-case` for JS/CSS (`floating-chat.js`, `search-results.css`).
- CSS: prefer existing tokens/themes under `src/styles/`; avoid inline styles.
- HTML: use `data-i18n-*` for translatable text; keep scripts external.
- Indentation: match existing files (spaces). Keep diffs minimal.

## Testing Guidelines
- No runner configured. Add browser tests under `tests/` and open them directly.
- Name test pages descriptively (e.g., `tests/test-chat.html`).
- Validate: search results rendering, chat replies, language switch, and theme toggle. Capture console warnings.

## Commit & Pull Request Guidelines
- Commits: follow Conventional Commits (e.g., `feat:`, `fix:`, `docs:`, `refactor:`, `style:`, `test:`, `chore:`). One focused change per commit.
- PRs: include purpose, screenshots/GIFs for UI, steps to reproduce/verify, and linked issues. Note any config changes.

## Security & Configuration Tips
- Do not commit API keys. Provide Gemini keys via env loaders (`local-env-loader.js`/Netlify env) to `window.CONFIG.gemini.apiKey`.
- Respect CSP in `config.js`; external calls should target `https://generativelanguage.googleapis.com` only.
