<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言切换测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .lang-btn { margin: 5px; padding: 10px 15px; cursor: pointer; }
        .lang-btn.active { background: #007bff; color: white; }
        .test-element { margin: 10px 0; padding: 10px; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>语言切换功能测试</h1>
    
    <div class="test-section">
        <h2>语言按钮</h2>
        <button class="lang-btn active" id="zh-btn" data-key="lang-zh">中文</button>
        <button class="lang-btn" id="en-btn" data-key="lang-en">English</button>
        <button class="lang-btn" id="ms-btn" data-key="lang-ms">Bahasa</button>
    </div>
    
    <div class="test-section">
        <h2>测试元素</h2>
        <div class="test-element" data-key="site-title">测试标题</div>
        <div class="test-element" data-key="toc-title">目录</div>
        <div class="test-element" data-key="section1-title">第一章</div>
    </div>
    
    <div class="test-section">
        <h2>调试信息</h2>
        <div id="debug-info"></div>
    </div>
    
    <script>
        // 简化的翻译对象
        const translations = {
            zh: {
                'lang-zh': '中文',
                'lang-en': 'English',
                'lang-ms': 'Bahasa',
                'site-title': '司机指南',
                'toc-title': '目录',
                'section1-title': '工作流程指南'
            },
            en: {
                'lang-zh': '中文',
                'lang-en': 'English', 
                'lang-ms': 'Bahasa',
                'site-title': 'Driver Guide',
                'toc-title': 'Table of Contents',
                'section1-title': 'Workflow Guide'
            },
            ms: {
                'lang-zh': '中文',
                'lang-en': 'English',
                'lang-ms': 'Bahasa',
                'site-title': 'Panduan Pemandu',
                'toc-title': 'Senarai Kandungan',
                'section1-title': 'Panduan Aliran Kerja'
            }
        };
        
        let currentLanguage = 'zh';
        
        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }
        
        function switchLanguage(lang) {
            updateDebugInfo('切换语言到: ' + lang);
            currentLanguage = lang;
            
            // 更新按钮状态
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            const activeBtn = document.getElementById(lang + '-btn');
            if (activeBtn) {
                activeBtn.classList.add('active');
                updateDebugInfo('激活按钮: ' + lang + '-btn');
            }
            
            // 更新页面内容
            updatePageContent(lang);
        }
        
        function updatePageContent(lang) {
            updateDebugInfo('更新页面内容为: ' + lang);
            const elements = document.querySelectorAll('[data-key]');
            updateDebugInfo('找到 ' + elements.length + ' 个需要翻译的元素');
            
            elements.forEach(element => {
                const key = element.getAttribute('data-key');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                    updateDebugInfo('更新元素 ' + key + ' 为: ' + translations[lang][key]);
                } else {
                    updateDebugInfo('警告: 未找到翻译 ' + lang + '.' + key);
                }
            });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('DOM 加载完成');
            
            // 设置语言按钮事件监听器
            const languageButtons = ['zh-btn', 'en-btn', 'ms-btn'];
            languageButtons.forEach(buttonId => {
                const button = document.getElementById(buttonId);
                if (button) {
                    const lang = buttonId.replace('-btn', '');
                    updateDebugInfo('设置按钮事件: ' + buttonId + ' -> ' + lang);
                    
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        updateDebugInfo('按钮点击: ' + lang);
                        switchLanguage(lang);
                    });
                } else {
                    updateDebugInfo('错误: 未找到按钮 ' + buttonId);
                }
            });
            
            // 初始化页面内容
            updatePageContent(currentLanguage);
        });
    </script>
</body>
</html>