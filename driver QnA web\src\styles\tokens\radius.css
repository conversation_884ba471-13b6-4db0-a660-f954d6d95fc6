/*
 * 文件路径: src/styles/tokens/radius.css
 * 文件描述: 定义了应用程序的圆角系统，通过CSS自定义属性（变量）提供了一致的UI元素圆角值。它包含了从微小到完全圆形的圆角等级，以及按钮、卡片、模态框和输入框等常见组件的语义化圆角定义。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 集中管理所有圆角定义，确保应用程序中UI元素曲率的视觉一致性。
 *   - 通过从单一来源应用圆角更改，简化设计更新。
 *   - 促进语义化使用，使开发者能够应用有意义的圆角样式（例如：`--radius-button`），而不是单独的像素值。
 * 关键部分:
 *   - `--radius-xs` 到 `--radius-2xl`: 定义了一个可伸缩的圆角系统。
 *   - `--radius-full`: 一个较大的值，用于创建完美的圆形或药丸状元素。
 *   - `--radius-button`, `--radius-card`, `--radius-modal`, `--radius-input`: 语义化定义，映射到基本圆角值，为特定组件类型提供一致的样式。
 * 使用约定:
 *   - 其他CSS文件应通过 `var()` 函数引用这些圆角变量（例如：`border-radius: var(--radius-card);`）来应用一致的圆角。
 */
/* 设计令牌 - 圆角系统 */
:root {
  /* 圆角等级系统 */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;

  /* 语义化圆角 */
  --radius-button: var(--radius-lg);
  --radius-card: var(--radius-xl);
  --radius-modal: var(--radius-2xl);
  --radius-input: var(--radius-md);
}