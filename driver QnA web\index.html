<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=yes">
    <title>GoMyHire 司机FAQ | Driver FAQ | FAQ Pemandu</title>
    
    <!-- API密钥配置 - Netlify部署专用 -->
    <meta name="env:GEMINI_API_KEY" content="AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s">
    <meta name="env:GEMINI_MODEL" content="gemini-2.5-flash">
    
    <!-- Netlify环境变量注入 -->
    <script type="application/json" id="netlify-env">
        {
            "GEMINI_API_KEY": "AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s",
            "GEMINI_MODEL": "gemini-2.5-flash"
        }
    </script>
    
    <link rel="stylesheet" href="src/styles/index.css">
    <link rel="icon" type="png" href="gmh logo.png">
</head>
<body>
    <!-- 手机端头部 - 重构为上下两层 -->
    <header class="header">
        <!-- 上层：Logo区域 -->
        <div class="header-top">
            <div class="logo-container">
                <img class="gmh-logo-img" src="gmh logo.png" alt="GMH Logo" />
            </div>
        </div>

        <!-- 下层：搜索和功能区域 -->
        <div class="header-bottom">
            <!-- 搜索区域 (70%) -->
            <div class="search-section">
                <div class="search-input-wrapper">
                    <input type="text" id="searchInput" placeholder="搜索问题..." class="search-input" data-i18n-placeholder="searchPlaceholder">
                    <button id="geminiToggle" class="gemini-toggle active" type="button" title="AI助手已启用">
                        <span class="gemini-icon">✨</span>
                    </button>
                </div>
            </div>

            <!-- 功能按钮区域 (30%) -->
            <div class="controls-section">
                <!-- 主题切换按钮 -->
                <button id="themeToggle" class="theme-toggle" type="button" title="切换主题">
                    <span class="theme-icon">🌙</span>
                </button>

                <!-- 语言切换器 -->
                <div class="language-switcher">
                    <button class="lang-btn active" data-lang="zh" type="button">中</button>
                    <button class="lang-btn" data-lang="en" type="button">EN</button>
                    <button class="lang-btn" data-lang="ms" type="button">MS</button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 欢迎页面 -->
        <div id="welcomePage" class="welcome-page">
            <div class="faq-cards" id="faqCards">
                <!-- FAQ卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 分类页面 -->
        <div id="categoriesPage" class="categories-page page-hidden">
            <div class="page-title">
                <h2>选择分类</h2>
            </div>
            <!-- 
                分类容器结构说明：
                - id="categories-container" 是静态HTML中定义的容器
                - JavaScript会在此容器内动态生成 <div class="category-grid"> 
                - 实际的分类卡片会放在 .category-grid 容器中
                - CSS中针对 .category-grid 设置了强制三列布局
            -->
            <div id="categories-container" class="categories-container">
                <!-- 分类按钮将通过JavaScript动态生成，结构如下：
                     <div class="category-grid">
                       <div class="category-card">...</div>
                       <div class="category-card">...</div>
                       ...
                     </div>
                -->
            </div>
        </div>

        <!-- 分类问题页面 -->
        <div id="categoryQuestionsPage" class="category-questions-page page-hidden">
            <!-- 分类问题内容将通过JavaScript动态生成 -->
        </div>

        <!-- FAQ详情页面 -->
        <div id="faqPage" class="faq-page page-hidden">
            <div class="faq-content" id="faqContent">
                <!-- FAQ内容将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 搜索结果页面 -->
        <div id="searchPage" class="search-page page-hidden">
            <div class="search-results" id="searchResults">
                <!-- 搜索结果将通过JavaScript动态生成 -->
            </div>
        </div>
    </main>

    <!-- 聊天窗口按钮 -->
    <button id="chatToggle" class="chat-toggle floating-chat-btn chat-button-above-nav" type="button" title="打开AI聊天助手">
        <span class="chat-icon">💬</span>
    </button>

    <!-- 手机端底部导航 -->
    <nav class="bottom-nav">
        <a href="#" class="nav-item active" data-tab="faq">
            <span class="icon">📋</span>
            <span>FAQ</span>
        </a>
    </nav>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="purple-loader"></div>
        <p data-i18n="loading" class="purple-text-gradient">加载中...</p>
    </div>

    <!-- JavaScript文件 - 本地/Netlify通用版本 -->
    <script src="src/core/config.js"></script>
    <script src="src/core/local-env-loader.js"></script>
    <script src="src/core/data.js"></script>
    <script src="src/core/i18n.js"></script>
    <script src="src/search/gemini-assistant.js"></script>
    <script src="src/components/floating-chat.js"></script>
    <script src="src/core/app.js"></script>
</body>
</html>
