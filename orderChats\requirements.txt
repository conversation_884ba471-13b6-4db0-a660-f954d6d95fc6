# 知识库系统依赖
# 用于订单对话记录的结构化知识库管理

# 核心依赖
watchdog>=3.0.0          # 文件系统监控
schedule>=1.2.0          # 定时任务调度

# 可选依赖（用于高级功能）
pandas>=1.5.0           # 数据处理和分析
numpy>=1.24.0           # 数值计算
matplotlib>=3.6.0       # 数据可视化
seaborn>=0.12.0         # 统计图表
scikit-learn>=1.2.0     # 机器学习功能

# 开发和测试依赖
pytest>=7.0.0           # 单元测试
pytest-cov>=4.0.0       # 测试覆盖率
black>=22.0.0           # 代码格式化
flake8>=5.0.0           # 代码检查
mypy>=1.0.0             # 类型检查