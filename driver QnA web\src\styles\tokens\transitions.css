/*
 * 文件路径: src/styles/tokens/transitions.css
 * 文件描述: 定义了应用程序的过渡和动画系统，通过CSS自定义属性（变量）提供了一致的持续时间和缓动函数，以实现流畅的UI变化。它还为各种UI元素提供了常用和语义化的过渡组合。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 集中管理所有过渡和动画定义，确保应用程序中用户体验的一致性和流畅性。
 *   - 通过提供预定义、可重用的值，简化动画和过渡的应用。
 *   - 促进语义化使用，使开发者能够应用有意义的过渡样式（例如：`--transition-button`），而不是单独的属性值。
 * 关键部分:
 *   - `--transition-fast`, `--transition-normal`, `--transition-slow`: 定义了过渡的标准持续时间。
 *   - `--ease-linear`, `--ease-in`, `--ease-out`, `--ease-in-out`, `--ease-bounce`: 定义了常用的缓动函数（时间函数），用于动画。
 *   - `--transition-colors`, `--transition-opacity`, `--transition-transform`, `--transition-all`: 定义了常用的过渡属性组合，方便一次性应用多个过渡。
 *   - `--transition-button`, `--transition-card`, `--transition-modal`: 语义化定义，结合了特定组件类型的过渡属性，确保动画行为的一致性。
 * 使用约定:
 *   - 其他CSS文件应通过 `var()` 函数引用这些过渡变量（例如：`transition: var(--transition-button);`）来应用一致的过渡效果。
 */
/* 设计令牌 - 过渡和动画系统 */
:root {
  /* 过渡时间 */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  /* 缓动函数 */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* 常用组合过渡 */
  --transition-colors: color var(--transition-normal) var(--ease-in-out),
                       background-color var(--transition-normal) var(--ease-in-out),
                       border-color var(--transition-normal) var(--ease-in-out);
  
  --transition-opacity: opacity var(--transition-normal) var(--ease-in-out);
  
  --transition-transform: transform var(--transition-normal) var(--ease-in-out);
  
  --transition-all: all var(--transition-normal) var(--ease-in-out);

  /* 语义化过渡 */
  --transition-button: var(--transition-colors), var(--transition-transform);
  --transition-card: var(--transition-all);
  --transition-modal: var(--transition-opacity), var(--transition-transform);
}