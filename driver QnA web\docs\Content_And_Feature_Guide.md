# 内容与功能指南

**报告日期:** 2025年9月1日

## 1. 内容美化

### 1.1. 核心功能

*   **视觉增强**: 6种精美内容容器 (`info-box`, `procedure-box`, 等)。
*   **交互体验**: 平滑动画、键盘导航、无障碍支持。
*   **实用功能**: 一键复制、智能识别、多语言支持。

### 1.2. 使用方法

*   **交互式工具 (推荐)**: 打开 `tools/interactive-beautifier.html`。
*   **批量处理**:
    ```bash
    cd tools/
    node run-beautification.js
    ```
*   **手动应用**:
    1.  引入样式: `<link rel="stylesheet" href="src/styles/enhanced-ui.css">`
    2.  引入脚本: `<script src="src/scripts/enhanced-interactions.js"></script>`

---
*本部分整合了 `docs/BEAUTIFICATION_GUIDE.md` 的核心内容。*

## 2. FAQ内容集成

### 2.1. 集成优化目标

*   **问题总数**: 89个 → 85个。
*   **重复内容**: 12个重复问题 → 0个。
*   **内容完整性**: 75% → 95%。

### 2.2. 核心集成问题

*   **FC-CT-01 Ctrip平台问题**: 合并账号绑定、双平台操作等内容。
*   **FC-IN-01 司机收入问题**: 合并收入查看、计算公式、提现流程等内容。

---
*本部分整合了 `docs/guides/FAQ_Integration_Task_Guide.md` 的核心内容。*

## 3. 图片资源管理

### 3.1. 文件命名规范

*   **格式**: `faq-[问题ID]-[步骤编号]-[语言].png`
*   **示例**: `faq-ct-01-step1-zh.png`

### 3.2. 图片集成

*   **自动集成**: 图片通过 `imageAssets.getImageHtml()` 函数自动集成到FAQ内容中。
*   **占位符**: 图片不存在时，会显示占位符。

### 3.3. 图片规格

*   **格式**: PNG或JPG。
*   **分辨率**: 推荐 1200x900 像素。
*   **文件大小**: 不超过 500KB。

---
*本部分整合了 `docs/technical/IMAGE_MANIFEST.md` 的核心内容。*
