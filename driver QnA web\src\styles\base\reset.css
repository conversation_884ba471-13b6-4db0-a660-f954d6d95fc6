/*
 * 文件路径: src/styles/base/reset.css
 * 文件描述: 提供了一个现代化且具有明确设计倾向的CSS重置。其主要目的是标准化不同浏览器之间的样式，为应用程序的设计提供一个一致的起点。它移除了默认的浏览器样式，并为响应式和可访问的UI开发奠定了基础。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 使用了在 `src/styles/tokens/colors.css` 中定义的CSS变量（`--shadow-color`, `--border-focus`, `--background-secondary`, `--border-color`, `--text-tertiary`）以及在 `src/styles/tokens/radius.css` 中定义的变量（`--radius-full`）。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - **跨浏览器一致性**: 消除不同浏览器默认样式之间的不一致性。
 *   - **干净的起点**: 提供一个干净且可预测的基础，用于应用自定义样式，而无需与浏览器默认样式冲突。
 *   - **可访问性**: 优雅地处理了焦点轮廓等基本可访问性功能。
 *   - **响应式设计基础**: 全局设置 `box-sizing: border-box;`，这是直观响应式布局的基础。
 * 关键重置/规则:
 *   - `*, *::before, *::after`: 对所有元素应用 `box-sizing: border-box;`，简化布局计算。
 *   - `margin: 0; padding: 0;`: 移除所有元素的默认外边距和内边距。
 *   - `html`: 设置文本大小调整，禁用点击高亮，并优化文本渲染。
 *   - `body`: 设置默认 `line-height`，并启用字体抗锯齿以使文本更平滑。
 *   - `img, picture, video, canvas, svg`: 确保媒体元素是块级且响应式的。
 *   - `input, button, textarea, select`: 继承其父元素的字体样式。
 *   - `button`: 重置默认按钮样式，设置光标。
 *   - `a`: 移除默认文本装饰并继承颜色。
 *   - `p, h1-h6`: 设置 `overflow-wrap: break-word;` 以更好地处理文本。
 *   - `:focus`, `:focus-visible`: 管理焦点轮廓以提高可访问性，使用自定义的焦点边框颜色。
 *   - `::-webkit-scrollbar-*`: 为基于WebKit的浏览器滚动条设置样式，使用已定义的设计令牌来设置背景、滑块颜色和悬停状态。
 * 使用约定:
 *   - 此文件通常应作为第一个加载的CSS文件之一，以确保其重置在其他样式之前应用。
 */
/* 基础样式 - 样式重置 */

/* 现代化CSS重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
}

a {
  text-decoration: none;
  color: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

#root,
#__next {
  isolation: isolate;
}

/* 移除默认的focus outline，但保持可访问性 */
:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}