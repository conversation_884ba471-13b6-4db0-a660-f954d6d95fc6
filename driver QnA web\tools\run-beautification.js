#!/usr/bin/env node

/**
 * FAQ内容批量美化运行脚本
 * 用途：实际运行FAQ内容美化，处理真实的data.js数据
 * 运行方式：node tools/run-beautification.js
 */

const fs = require('fs');
const path = require('path');

// 动态加载FAQ美化工具
const FAQBeautifier = require('./beautify-faq-content.js');

class FAQBeautificationRunner {
    constructor() {
        this.beautifier = new FAQBeautifier();
        this.dataPath = path.join(__dirname, '..', 'src', 'core', 'data.js');
        this.backupPath = path.join(__dirname, '..', 'backup', 'data-before-beautification.js');
        this.outputPath = path.join(__dirname, '..', 'src', 'core', 'data-beautified.js');
    }

    /**
     * 加载FAQ数据
     */
    async loadData() {
        console.log('📂 正在加载FAQ数据...');
        
        try {
            const dataContent = fs.readFileSync(this.dataPath, 'utf8');
            
            // 创建安全的数据环境
            const dataContext = {};
            
            // 执行数据文件，获取faqData
            const dataScript = dataContent.replace(
                /module\.exports\s*=\s*.*;?/g, ''
            ).replace(
                /export\s+default\s+.*;?/g, ''
            );
            
            // 使用eval加载数据（注意：仅用于内部工具）
            eval(dataScript + '\ndataContext.faqData = faqData;');
            
            if (!dataContext.faqData) {
                throw new Error('无法找到faqData对象');
            }
            
            console.log(`✅ 成功加载 ${dataContext.faqData.questions.length} 个问题`);
            return dataContext.faqData;
            
        } catch (error) {
            console.error('❌ 加载数据失败:', error.message);
            throw error;
        }
    }

    /**
     * 创建备份
     */
    createBackup() {
        console.log('💾 创建数据备份...');
        
        try {
            const dataContent = fs.readFileSync(this.dataPath, 'utf8');
            fs.writeFileSync(this.backupPath, dataContent);
            console.log(`✅ 备份已创建: ${this.backupPath}`);
        } catch (error) {
            console.error('❌ 创建备份失败:', error.message);
            throw error;
        }
    }

    /**
     * 运行美化过程
     */
    async runBeautification() {
        console.log('🎨 开始FAQ内容美化过程...');
        console.log('=' .repeat(50));

        try {
            // 1. 创建备份
            this.createBackup();

            // 2. 加载数据
            const faqData = await this.loadData();

            // 3. 运行美化
            const result = await this.beautifier.beautifyFAQData(faqData);

            // 4. 生成报告
            const report = this.beautifier.generateReport(result.stats);
            console.log('\n' + report);

            // 5. 保存美化后的数据
            await this.saveBeautifiedData(result.data);

            // 6. 创建示例页面
            await this.createDemoPage();

            console.log('\n🎉 美化过程完成！');
            console.log('📁 文件位置:');
            console.log(`  原始备份: ${this.backupPath}`);
            console.log(`  美化数据: ${this.outputPath}`);
            console.log(`  文档说明: 查看 docs/ 目录下的相关文档`);

            return result;

        } catch (error) {
            console.error('❌ 美化过程失败:', error);
            throw error;
        }
    }

    /**
     * 保存美化后的数据
     */
    async saveBeautifiedData(data) {
        console.log('💾 保存美化后的数据...');

        try {
            // 读取原始文件头部注释
            const originalContent = fs.readFileSync(this.dataPath, 'utf8');
            const headerMatch = originalContent.match(/\/\*[\s\S]*?\*\//);
            const header = headerMatch ? headerMatch[0] : '/**
 * GoMyHire司机FAQ数据库 - 美化版本
 * 已应用增强视觉样式
 */';

            // 生成新的数据文件
            const beautifiedContent = `${header}

// ===== 美化版本数据 =====
// 生成时间: ${new Date().toISOString()}
// 美化问题数: ${data.questions.filter(q => q._enhanced).length}

${this.generateDataFileContent(data)}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { faqData, sharedContentTemplates, unifiedCategorySystem, unifiedSearchTags, imageAssets };
} else {
    window.faqData = faqData;
}`;

            fs.writeFileSync(this.outputPath, beautifiedContent);
            console.log(`✅ 美化数据已保存: ${this.outputPath}`);

        } catch (error) {
            console.error('❌ 保存数据失败:', error);
            throw error;
        }
    }

    /**
     * 生成数据文件内容
     */
    generateDataFileContent(data) {
        // 这里简化处理，实际应该重新构建整个数据结构
        const questionsContent = data.questions.map(q => {
            const contentStr = typeof q.content === 'object' 
                ? JSON.stringify(q.content, null, 8)
                : `\`${q.content.replace(/`/g, '\\`')}\``;

            return `        {
            id: "${q.id}",
            category: "${q.category}",
            priority: "${q.priority}",
            title: ${JSON.stringify(q.title, null, 12)},
            content: ${contentStr},
            ${q.tags ? `tags: ${JSON.stringify(q.tags)},` : ''}
            ${q._enhanced ? '_enhanced: true,' : ''}
        }`;
        }).join(',\n\n');

        return `const faqData = {
    categories: ${JSON.stringify(data.categories, null, 4)},
    questions: [\n${questionsContent}\n    ]
};`;
    }

    /**
     * 创建示例页面
     */
    async createDemoPage() {
        const demoHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ内容美化示例 - GoMyHire</title>
    <link rel="stylesheet" href="src/styles/index.css">
    <link rel="stylesheet" href="src/styles/components/enhanced-content.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .demo-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .demo-content {
            max-width: 800px;
            margin: 0 auto;
        }
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        .section h3 {
            margin-top: 0;
            color: #007bff;
        }
        @media (max-width: 768px) {
            .before-after {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎨 FAQ内容美化示例</h1>
            <p>展示美化前后的对比效果</p>
        </div>

        <div class="demo-content">
            <h2>📊 美化效果展示</h2>
            
            <div class="before-after">
                <div class="section">
                    <h3>原始内容</h3>
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <p>订单状态更新时间要求：</p>
                        <p>1. 15分钟"已到达"规则：必须在预定接送时间前至少15分钟点击GMH中的"已到达"按钮</p>
                        <p>2. 违规后果：系统将自动取消订单</p>
                    </div>
                </div>

                <div class="section">
                    <h3>美化后内容</h3>
                    <div class="info-box">
                        <h3>📱 订单状态更新时间要求</h3>
                        <div class="procedure-box">
                            <h4>🕐 关键时间要求</h4>
                            <ol>
                                <li><strong>⏰ 15分钟"已到达"规则</strong>：必须在预定接送时间前至少15分钟点击GMH中的"已到达"按钮</li>
                            </ol>
                        </div>
                        <div class="warning-box">
                            <h4>⚠️ 违规后果</h4>
                            <ul>
                                <li>系统将自动取消订单</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <h2>🎯 可用样式类</h2>
            <div class="info-box">
                <h3>📋 info-box</h3>
                <p>用于一般信息展示，提供清晰的视觉层次</p>
            </div>

            <div class="procedure-box">
                <h3>📋 procedure-box</h3>
                <p>用于操作步骤和流程说明</p>
            </div>

            <div class="warning-box">
                <h3>⚠️ warning-box</h3>
                <p>用于警告和注意事项</p>
            </div>

            <div class="tip-box">
                <h3>💡 tip-box</h3>
                <p>用于提示和建议</p>
            </div>

            <div class="success-box">
                <h3>✅ success-box</h3>
                <p>用于成功消息和确认信息</p>
            </div>

            <div class="error-box">
                <h3>❌ error-box</h3>
                <p>用于错误和失败信息</p>
            </div>

            <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <h3>🚀 开始使用</h3>
                <p>运行以下命令开始批量美化：</p>
                <code style="background: #333; color: #fff; padding: 10px; border-radius: 5px;">node tools/run-beautification.js</code>
            </div>
        </div>
    </div>

    <script src="tools/beautify-faq-content.js"></script>
    <script>
        // 演示用：展示美化效果
        console.log('🎨 FAQ美化演示页面已加载');
    </script>
</body>
</html>`;

        fs.writeFileSync(
            path.join(__dirname, '..', 'docs', 'beautification-demo.html'), 
            demoHtml
        );
        console.log('✅ 示例页面已保存到 docs/ 目录');
    }
}

// 命令行运行
if (require.main === module) {
    const runner = new FAQBeautificationRunner();
    runner.runBeautification().catch(console.error);
}

module.exports = FAQBeautificationRunner;