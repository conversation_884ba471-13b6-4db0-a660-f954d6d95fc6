{
  "ai_assistant_interface": {
    "version": "3.0.0",
    "last_updated": "2025-09-01T00:00:00Z",
    "integration_guide": {
      "purpose": "GoMyHire AI助手接口文档 - 为公司内部AI助手和客服系统提供标准知识库访问",
      "target_users": [
        "AI_developers",
        "customer_service_teams",
        "operations_staff",
        "management_personnel"
      ],
      "access_methods": [
        "RESTful_API",
        "GraphQL",
        "WebSocket",
        "SDK_libraries"
      ]
    },
    "api_endpoints": {
      "knowledge_query": {
        "endpoint": "/api/v1/knowledge/query",
        "method": "POST",
        "description": "查询知识库获取相关信息",
        "parameters": {
          "query": {
            "type": "string",
            "required": true,
            "description": "用户查询的问题或请求",
            "example": "如何预订从机场到云顶高原的服务？"
          },
          "context": {
            "type": "object",
            "required": false,
            "description": "对话上下文信息",
            "properties": {
              "customer_id": {
                "type": "string",
                "description": "客户ID（如果可用）"
              },
              "conversation_history": {
                "type": "array",
                "description": "之前的对话记录"
              },
              "current_session": {
                "type": "object",
                "description": "当前会话信息"
              }
            }
          },
          "filters": {
            "type": "object",
            "required": false,
            "description": "搜索过滤器",
            "properties": {
              "knowledge_categories": {
                "type": "array",
                "items": {"type": "string"},
                "description": "知识类别过滤"
              },
              "date_range": {
                "type": "object",
                "properties": {
                  "start": {"type": "string"},
                  "end": {"type": "string"}
                }
              },
              "access_level": {
                "type": "string",
                "enum": ["public", "internal", "restricted", "critical"]
              }
            }
          },
          "preferences": {
            "type": "object",
            "required": false,
            "description": "响应偏好设置",
            "properties": {
              "language": {
                "type": "string",
                "enum": ["zh", "en", "ms", "ta"],
                "default": "zh"
              },
              "response_format": {
                "type": "string",
                "enum": ["detailed", "concise", "bullet_points", "step_by_step"],
                "default": "detailed"
              },
              "include_sources": {
                "type": "boolean",
                "default": true
              }
            }
          }
        },
        "response": {
          "success": {
            "type": "object",
            "properties": {
              "answer": {
                "type": "string",
                "description": "AI生成的回答"
              },
              "confidence": {
                "type": "number",
                "description": "回答的置信度 (0-1)"
              },
              "sources": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "id": {"type": "string"},
                    "title": {"type": "string"},
                    "relevance_score": {"type": "number"},
                    "excerpt": {"type": "string"}
                  }
                }
              },
              "suggested_actions": {
                "type": "array",
                "items": {"type": "string"},
                "description": "建议的后续操作"
              },
              "metadata": {
                "type": "object",
                "properties": {
                  "response_time": {"type": "number"},
                  "knowledge_coverage": {"type": "number"},
                  "processing_steps": {"type": "array"}
                }
              }
            }
          }
        }
      },
      "booking_assistance": {
        "endpoint": "/api/v1/booking/assist",
        "method": "POST",
        "description": "AI助手预订协助",
        "parameters": {
          "customer_intent": {
            "type": "string",
            "required": true,
            "enum": ["new_booking", "modification", "cancellation", "inquiry"],
            "description": "客户意图"
          },
          "trip_details": {
            "type": "object",
            "required": true,
            "properties": {
              "pickup_location": {"type": "string"},
              "destination": {"type": "string"},
              "pickup_datetime": {"type": "string"},
              "passengers": {"type": "integer"},
              "luggage": {"type": "string"},
              "vehicle_preference": {"type": "string"},
              "special_requirements": {"type": "array"}
            }
          },
          "customer_info": {
            "type": "object",
            "properties": {
              "name": {"type": "string"},
              "contact": {"type": "string"},
              "email": {"type": "string"},
              "loyalty_tier": {"type": "string"}
            }
          }
        },
        "response": {
          "booking_options": {
            "type": "array",
            "items": {
              "service_type": {"type": "string"},
              "vehicle_type": {"type": "string"},
              "price": {"type": "number"},
              "duration": {"type": "string"},
              "features": {"type": "array"}
            }
          },
          "recommendations": {
            "type": "array",
            "items": {"type": "string"}
          },
          "next_steps": {
            "type": "array",
            "items": {"type": "string"}
          }
        }
      },
      "customer_support": {
        "endpoint": "/api/v1/support/assist",
        "method": "POST",
        "description": "客户支持问题处理",
        "parameters": {
          "issue_type": {
            "type": "string",
            "required": true,
            "enum": ["booking_issue", "service_problem", "billing_inquiry", "complaint", "emergency"],
            "description": "问题类型"
          },
          "urgency_level": {
            "type": "string",
            "enum": ["low", "medium", "high", "critical"],
            "default": "medium"
          },
          "issue_description": {
            "type": "string",
            "required": true,
            "description": "问题描述"
          },
          "customer_context": {
            "type": "object",
            "properties": {
              "booking_reference": {"type": "string"},
              "customer_id": {"type": "string"},
              "previous_interactions": {"type": "array"}
            }
          }
        },
        "response": {
          "issue_analysis": {
            "type": "object",
            "properties": {
              "category": {"type": "string"},
              "severity": {"type": "string"},
              "resolution_approach": {"type": "string"}
            }
          },
          "recommended_actions": {
            "type": "array",
            "items": {"type": "string"}
          },
          "escalation_needed": {
            "type": "boolean",
            "description": "是否需要升级处理"
          },
          "estimated_resolution_time": {
            "type": "string"
          }
        }
      }
    },
    "real_time_coordination": {
      "driver_location": {
        "endpoint": "/api/v1/driver/location",
        "method": "GET",
        "description": "获取司机实时位置",
        "parameters": {
          "order_id": {
            "type": "string",
            "required": true,
            "description": "订单ID"
          },
          "customer_id": {
            "type": "string",
            "required": true,
            "description": "客户ID"
          }
        },
        "response": {
          "location": {
            "latitude": {"type": "number"},
            "longitude": {"type": "number"},
            "address": {"type": "string"},
            "estimated_arrival": {"type": "string"}
          },
          "driver_info": {
            "name": {"type": "string"},
            "phone": {"type": "string"},
            "vehicle": {"type": "object"},
            "license_plate": {"type": "string"}
          }
        }
      },
      "flight_status": {
        "endpoint": "/api/v1/flight/status",
        "method": "GET",
        "description": "获取航班状态信息",
        "parameters": {
          "flight_number": {
            "type": "string",
            "required": true,
            "description": "航班号"
          },
          "date": {
            "type": "string",
            "required": true,
            "description": "航班日期"
          }
        },
        "response": {
          "flight_info": {
            "status": {"type": "string"},
            "departure_time": {"type": "string"},
            "arrival_time": {"type": "string"},
            "delay_minutes": {"type": "number"},
            "gate": {"type": "string"}
          },
          "impact_assessment": {
            "pickup_adjustment_needed": {"type": "boolean"},
            "recommended_action": {"type": "string"},
            "customer_notification_required": {"type": "boolean"}
          }
        }
      },
      "order_modification": {
        "endpoint": "/api/v1/order/modify",
        "method": "POST",
        "description": "订单修改请求",
        "parameters": {
          "order_id": {
            "type": "string",
            "required": true,
            "description": "订单ID"
          },
          "modification_type": {
            "type": "string",
            "required": true,
            "enum": ["address_change", "time_change", "vehicle_change", "cancelation"],
            "description": "修改类型"
          },
          "modification_details": {
            "type": "object",
            "required": true,
            "description": "修改详情"
          }
        },
        "response": {
          "modification_status": {
            "success": {"type": "boolean"},
            "confirmation_number": {"type": "string"},
            "price_adjustment": {"type": "number"},
            "driver_notification_sent": {"type": "boolean"}
          }
        }
      }
    },
    "webhook_events": {
      "knowledge_updated": {
        "event": "knowledge.base.updated",
        "description": "知识库内容更新时触发",
        "data": {
          "update_type": {"type": "string"},
          "affected_collections": {"type": "array"},
          "timestamp": {"type": "string"}
        }
      },
      "feedback_received": {
        "event": "feedback.received",
        "description": "收到AI助手反馈时触发",
        "data": {
          "conversation_id": {"type": "string"},
          "feedback_type": {"type": "string"},
          "rating": {"type": "number"},
          "comments": {"type": "string"}
        }
      },
      "performance_alert": {
        "event": "performance.alert",
        "description": "性能指标异常时触发",
        "data": {
          "metric_name": {"type": "string"},
          "current_value": {"type": "number"},
          "threshold": {"type": "number"},
          "severity": {"type": "string"}
        }
      }
    },
    "sdk_examples": {
      "python": {
        "installation": "pip install gomyhire-ai-sdk",
        "usage_example": `
from gomyhire_ai import GoMyHireAI

# 初始化AI助手
ai = GoMyHireAI(api_key="your_api_key")

# 查询知识库
response = ai.knowledge_query(
    query="如何预订从机场到市区的服务？",
    context={"customer_id": "12345"},
    preferences={"language": "zh", "response_format": "detailed"}
)

print(response.answer)
print(f"置信度: {response.confidence}")
        `
      },
      "javascript": {
        "installation": "npm install @gomyhire/ai-sdk",
        "usage_example": `
const GoMyHireAI = require('@gomyhire/ai-sdk');

// 初始化AI助手
const ai = new GoMyHireAI({
    apiKey: 'your_api_key'
});

// 查询知识库
async function getBookingInfo() {
    const response = await ai.knowledgeQuery({
        query: '如何预订从机场到市区的服务？',
        context: { customerId: '12345' },
        preferences: { language: 'zh', responseFormat: 'detailed' }
    });
    
    console.log(response.answer);
    console.log(\`置信度: \${response.confidence}\`);
}
        `
      },
      "curl": {
        "example": `
curl -X POST https://api.gomyhire.com/v1/knowledge/query \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer your_api_key" \\
  -d '{
    "query": "如何预订从机场到市区的服务？",
    "context": {"customer_id": "12345"},
    "preferences": {"language": "zh", "response_format": "detailed"}
  }'
        `
      }
    },
    "integration_scenarios": {
      "customer_service_chatbot": {
        "description": "客服聊天机器人集成",
        "use_case": "自动回答客户常见问题，协助预订",
        "key_features": [
          "多语言支持",
          "上下文理解",
          "预订流程引导",
          "问题升级处理"
        ],
        "integration_points": [
          "网站聊天窗口",
          "移动应用",
          "社交媒体平台",
          "短信服务"
        ]
      },
      "internal_staff_assistant": {
        "description": "内部员工AI助手",
        "use_case": "为员工提供即时信息和流程指导",
        "key_features": [
          "政策查询",
          "操作流程指导",
          "问题诊断",
          "培训支持"
        ],
        "integration_points": [
          "内部通讯工具",
          "CRM系统",
          "培训平台",
          "知识管理平台"
        ]
      },
      "voice_assistant": {
        "description": "语音助手集成",
        "use_case": "电话客服和语音交互",
        "key_features": [
          "语音识别",
          "自然语言理解",
          "语音合成",
          "实时响应"
        ],
        "integration_points": [
          "电话系统",
          "智能音箱",
          "车载系统",
          "移动应用"
        ]
      }
    },
    "best_practices": {
      "query_optimization": {
        "clear_questions": "使用清晰、具体的查询",
        "provide_context": "提供相关上下文信息",
        "use_filters": "适当使用过滤器提高准确性",
        "iterate": "基于反馈优化查询"
      },
      "response_handling": {
        "confidence_threshold": "设置合适的置信度阈值",
        "fallback_strategy": "准备备用回答策略",
        "human_handoff": "明确何时转接人工",
        "feedback_loop": "建立反馈收集机制"
      },
      "performance_monitoring": {
        "key_metrics": [
          "响应时间",
          "准确性",
          "用户满意度",
          "解决率"
        ],
        "optimization": "基于数据持续改进",
        "testing": "定期测试和验证"
      }
    },
    "troubleshooting": {
      "common_issues": {
        "low_confidence_responses": {
          "causes": ["知识库覆盖不足", "查询不明确", "上下文缺失"],
          "solutions": ["改进查询", "增加上下文", "知识库更新"]
        },
        "slow_response_times": {
          "causes": ["系统负载高", "网络延迟", "查询复杂"],
          "solutions": ["优化查询", "增加资源", "使用缓存"]
        },
        "inaccurate_answers": {
          "causes": ["知识库过时", "理解错误", "上下文缺失"],
          "solutions": ["更新知识库", "改进算法", "增加上下文"]
        }
      },
      "support_channels": {
        "documentation": "详细API文档和指南",
        "community": "开发者社区论坛",
        "support_team": "专业技术支持团队",
        "monitoring": "实时系统监控"
      }
    }
  }
}