# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个包含 35,031 个订单聊天记录的数据库，每个订单对应一个 .txt 文件。文件名格式为 6 位数字编号（如 100001.txt、100002.txt 等）。

## 数据结构

### 文件格式
每个 .txt 文件包含：
- 订单编号（文件名）
- 时间戳
- 用户消息
- 系统回复

### 数据字段
- **Order Number**: 订单编号（与文件名对应）
- **OTA Reference**: OTA 参考号
- **Order Type**: 订单类型（如 Airport）
- **Start Date**: 服务开始时间
- **Flight Number**: 航班号
- **Pickup Address**: 接送地址
- **Destination Address**: 目的地地址
- **Car Type**: 车辆类型

### 示例数据格式
```
-----------------------------------------------
Time: 06/06/2025 16:34:12
User: Support: GoMyHire System
Message: Dear Aneeta Lhetaria Sibero, we are transportation service operator of Malaysia and Singapore. According your booking. OTA Reference: KYP625167. Order Number: 100001. Order Type: Airport Start Date: 2025-06-26 12:00:00. Flight Number: AK 432. Pickup Address: Kuala Lumpur International Airport. Destination Address: Genting Highlands Premium OutletsGenting Highlands Premium Outlets, Dataran Tinggi Genting, Pahang, Malaysia. Car Type: N/A.
```

## 工作说明

这是一个数据存储仓库，包含马来西亚和新加坡交通服务运营商的订单聊天记录。主要用于：
- 订单记录存储
- 客户服务对话存档
- 运营数据分析

## 常用命令

### 查看数据
```bash
# 查看特定订单
head -5 100001.txt

# 统计文件数量
find . -name "*.txt" | wc -l

# 查看文件大小
ls -la *.txt | head -10
```

### 数据分析
```bash
# 搜索特定订单类型
grep "Order Type: Airport" *.txt

# 提取订单编号
grep "Order Number:" *.txt | head -10

# 查看特定日期的订单
grep "2025-06-26" *.txt
```

## 注意事项

- 所有文件都是文本格式，包含结构化的订单信息
- 文件命名规范：6 位数字编号 + .txt 扩展名
- 数据涉及客户隐私，处理时需注意安全
- 主要服务区域为马来西亚和新加坡
- 数据格式相对固定，便于解析和分析