/*
 * 文件路径: src/search/gemini-assistant.js
 * 文件描述: 实现了 `GeminiSearchAssistant` 类，负责与Google Gemini AI API集成，为FAQ系统提供增强的搜索功能和智能对话响应。它处理API调用、速率限制、响应解析和安全验证。
 *
 * 深度追踪记录:
 *
 * 1.  **构造函数 (`constructor(config)`)**:
 *     - **输入**: 接收一个 `config` 对象（通常是 `window.CONFIG`），从中提取Gemini相关的配置（如 `apiKey`, `model`, `endpoint`, `temperature`, `maxTokens`, `maxRequestsPerMinute`, `timeoutMs`）。
 *     - **初始化**: 设置 `this.enabled` (是否启用AI功能)、`this.fallbackEnabled` (AI失败时是否回退到传统搜索)、`this.chatHistory` (存储对话历史)、`this.dataManager` (初始为null，等待注入)、`this.securityValidator` (依赖全局 `window.securityValidator`，用于输入和请求的安全验证) 和 `this.rateLimiter` (使用 `ApiRateLimiter` 类进行API调用速率控制)。
 *
 * 2.  **数据管理器注入 (`setDataManager(dataManager)`)**:
 *     - **职责**: 这是 `GeminiSearchAssistant` 获取FAQ数据访问能力的关键步骤。
 *     - **数据流**: `FAQApp` (在 `app.js` 中) 在初始化 `GeminiSearchAssistant` 实例后，会调用此方法将 `DataManager` 实例 (`this.dataManager`) 传递进来。
 *     - **影响**: 一旦设置，`GeminiSearchAssistant` 就可以通过 `this.dataManager` 访问所有FAQ问题、分类和搜索索引，从而实现基于FAQ知识库的AI响应。
 *
 * 3.  **可用性检查 (`isAvailable()`)**:
 *     - **逻辑**: 检查 `this.enabled` 标志是否为true，并且 `this.config.apiKey` 是否存在且不是默认的占位符（`'XXXXX'`）。
 *     - **控制流**: 许多AI相关的功能（如 `enhanceSearchQuery`, `chat`, `getSearchSuggestions`）在执行前都会调用此方法，以确保AI服务可用。
 *
 * 4.  **增强搜索查询 (`enhanceSearchQuery(query, language, options)`)**:
 *     - **职责**: 这是AI增强搜索的核心方法，用于将用户查询转换为更适合搜索的关键词、识别用户意图并提供搜索建议。
 *     - **控制流**: 
 *         - 首先调用 `isAvailable()` 检查AI服务是否可用。
 *         - 调用 `buildEnhancePrompt()` 构建发送给Gemini的提示词。
 *         - 使用 `Promise.race` 和 `getAdaptiveTimeout()` 实现智能超时和重试策略，提高API调用的鲁棒性。
 *         - 调用 `callGeminiAPI()` 发送请求到Gemini。
 *         - 如果API调用失败（例如超时），会根据 `options.attempt` 进行最多3次重试，每次重试之间有递增的延迟 (`delay()`)。
 *     - **数据流**: 
 *         - 将用户 `query` 和 `language` 封装到提示词中发送给Gemini。
 *         - 接收Gemini返回的JSON响应，从中提取 `keywords` (增强后的关键词)、`intent` (用户意图) 和 `suggestions` (搜索建议)。
 *         - 返回一个包含 `enhanced` 状态、原始查询、增强查询、关键词、意图和建议的对象。
 *
 * 5.  **智能对话 (`chat(message, language)`)**:
 *     - **职责**: 提供与用户的智能对话交互，优先从FAQ数据中查找答案，如果找不到则利用AI生成回答。
 *     - **控制流**: 
 *         - 调用 `addToChatHistory()` 记录用户消息。
 *         - 调用 `searchFAQData()` 尝试在本地FAQ数据中查找直接匹配或相关问题。
 *         - **条件分支**: 
 *             - 如果找到 `directMatch`，则调用 `formatFAQResponse()` 格式化FAQ答案并返回。
 *             - 如果找到 `relatedQuestions` 且AI可用，则调用 `generateFAQBasedResponse()` 利用AI生成基于FAQ知识库的回答。
 *             - 如果AI不可用或AI回答生成失败，则回退到 `formatSearchResults()` 显示搜索结果列表。
 *             - 如果没有找到任何相关FAQ，则调用 `generateGuidanceResponse()` 返回引导信息。
 *         - 调用 `addToChatHistory()` 记录助手的回复。
 *     - **数据流**: 
 *         - `message` (用户输入) 和 `language` 作为输入。
 *         - `faqResults` (包含 `directMatch` 和 `relatedQuestions`) 作为 `searchFAQData` 的输出。
 *         - `chatHistory` 用于维护对话上下文，通过 `getChatContext()` 提供给AI。
 *
 * 6.  **FAQ数据搜索 (`searchFAQData(query, language)`)**:
 *     - **职责**: 作为 `GeminiSearchAssistant` 与 `DataManager` 之间的桥梁，用于在本地FAQ数据中执行搜索。
 *     - **数据流**: 调用 `this.dataManager.searchQuestions(query, language)` 获取搜索结果。
 *     - **逻辑**: 分析 `searchResults`，尝试识别 `directMatch` (高相似度匹配) 和 `relatedQuestions` (其他相关问题)。
 *     - **输出**: 返回一个包含 `directMatch` 和 `relatedQuestions` 的对象。
 *
 * 7.  **调用Gemini API (`callGeminiAPI(prompt)`)**:
 *     - **职责**: 封装了向Gemini API发送HTTP请求的逻辑，包括速率限制、请求构建、安全验证和响应解析。
 *     - **控制流**: 
 *         - 调用 `this.rateLimiter.recordRequest()` 进行速率限制检查，如果请求过于频繁则抛出错误。
 *         - 调用 `this.securityValidator.validateSearchInput()` 对提示词进行安全验证，防止恶意注入。
 *         - 调用 `this.securityValidator.validateApiRequest()` 对API请求进行安全验证。
 *         - 使用 `fetch` API 发送POST请求到Gemini API端点。
 *         - 处理HTTP响应，如果响应不成功则抛出错误。
 *     - **数据流**: 
 *         - `prompt` (AI提示词) 作为输入。
 *         - `this.config` 提供API密钥、模型、端点等配置。
 *         - 接收Gemini返回的原始JSON数据，并尝试解析为JavaScript对象。
 *         - **错误处理**: 如果JSON解析失败，会尝试从响应文本中提取JSON部分。
 *
 * 8.  **生成基于FAQ知识库的AI回答 (`generateFAQBasedResponse(userMessage, relatedQuestions, language)`)**:
 *     - **职责**: 根据用户消息和找到的相关FAQ内容，构建一个详细的提示词，并调用Gemini生成自然语言的回答。
 *     - **数据流**: 
 *         - `userMessage` (用户原始问题) 和 `relatedQuestions` (从 `DataManager` 获得的FAQ内容) 作为输入。
 *         - `buildChatPrompt()` 负责将 `userMessage`、`chatHistory` (通过 `getChatContext()`) 和 `relatedQuestions` 整合到AI提示词中，指导Gemini生成高质量的回答。
 *         - 调用 `callGeminiAPI()` 获取Gemini的原始响应。
 *         - 调用 `parseChatResponse()` 解析Gemini的响应，并可能添加相关FAQ编号。
 *
 * 9.  **流式搜索增强 (`streamingEnhanceSearchQuery`, `callStreamingGeminiAPI`, `parseStreamingChunk`, `mergeResults`)**:
 *     - **职责**: 支持与Gemini API的流式交互，允许分阶段接收和处理AI生成的响应（例如，先返回意图，再返回关键词，最后返回建议）。
 *     - **控制流**: 
 *         - `streamingEnhanceSearchQuery` 负责协调流式请求，并提供 `onProgress` 和 `onComplete` 回调函数。
 *         - `callStreamingGeminiAPI` 使用 `fetch` API 的 `response.body.getReader()` 来读取流式数据。
 *         - `parseStreamingChunk` 负责解析从流中接收到的数据块，从中提取JSON对象。
 *         - `mergeResults` 用于将分阶段接收到的AI数据（如关键词、建议）合并到最终结果中。
 *
 * 10. **其他辅助方法**:
 *     - `addToChatHistory()` / `getChatContext()` / `clearChatHistory()`: 管理和提供对话历史上下文。
 *     - `getSearchSuggestions()`: 调用Gemini生成搜索建议。
 *     - `isDirectMatch()`: 简单的字符串匹配逻辑，用于判断是否为直接匹配的FAQ。
 *     - `formatFAQResponse()` / `formatSearchResults()` / `generateGuidanceResponse()`: 格式化不同类型的AI或搜索结果响应，使其更具可读性和用户友好性。
 *     - `buildSuggestionPrompt()`: 构建用于生成搜索建议的提示词。
 *     - `testAPIConnection()`: 用于测试与Gemini API的连接性。
 *     - `setEnabled()`: 动态启用或禁用Gemini助手。
 *     - `getAdaptiveTimeout()`: 实现API调用的自适应超时和重试延迟。
 *     - `getNetworkCondition()`: 获取网络连接状态，可用于优化API调用策略。
 *
 * 模块间数据与控制流:
 *   - **`app.js` -> `GeminiSearchAssistant`**: `app.js` 实例化 `GeminiSearchAssistant`，并调用 `setDataManager` 注入 `DataManager`。`app.js` 通过 `this.geminiAssistant` 调用其 `enhanceSearchQuery` 和 `chat` 等方法。
 *   - **`GeminiSearchAssistant` -> `DataManager`**: `GeminiSearchAssistant` 通过其内部的 `this.dataManager` 引用，调用 `DataManager` 的 `searchQuestions` 等方法来获取FAQ数据。
 *   - **`GeminiSearchAssistant` -> `window.CONFIG`**: `GeminiSearchAssistant` 读取 `window.CONFIG` 来获取API密钥、端点和模型等配置信息。
 *   - **`GeminiSearchAssistant` -> `ApiRateLimiter`**: `GeminiSearchAssistant` 实例化 `ApiRateLimiter` 并使用其方法来控制API请求频率。
 *   - **`GeminiSearchAssistant` -> `window.securityValidator`**: `GeminiSearchAssistant` 在调用 `fetch` API 之前，会调用 `window.securityValidator` 对请求进行安全验证。
 *   - **数据流**: 用户查询 (`query`, `message`) 从 `app.js` 传递到 `GeminiSearchAssistant`。`GeminiSearchAssistant` 内部处理后，可能从 `DataManager` 获取FAQ数据，并向Gemini API发送提示词。Gemini的响应数据被解析并格式化后，作为AI回复返回给 `app.js`。
 *
 * 这个更深度的追踪记录详细阐述了 `gemini-assistant.js` 如何作为AI层，通过与 `DataManager` 和 Gemini API 的复杂交互，为应用程序提供智能搜索和对话能力。
 */
// Gemini AI 搜索助手
class GeminiSearchAssistant {
    constructor(config) {
        this.config = config.gemini || {};
        this.enabled = config.search?.enableAI ?? false;
        this.fallbackEnabled = config.search?.fallbackToTraditional ?? true;
        this.chatHistory = []; // 存储对话历史
        this.dataManager = null; // FAQ数据管理器引用
        this.securityValidator = window.securityValidator;
        this.rateLimiter = new ApiRateLimiter(
            this.config.maxRequestsPerMinute || 60,
            60000
        );
        this.connectionTested = false;
    }

    // 设置数据管理器引用
    setDataManager(dataManager) {
        this.dataManager = dataManager;
    }

    // 获取助手状态
    getStatus() {
        return {
            available: this.isAvailable(),
            model: this.config.model || 'gemini-pro',
            enabled: this.enabled,
            dataManagerConnected: !!this.dataManager,
            chatHistoryLength: this.chatHistory.length
        };
    }
    
    // 检查是否可用
    isAvailable() {
        return this.enabled && this.config.apiKey && !this.config.apiKey.includes('XXXXX');
    }
    
    // 增强搜索查询 - 优化版本，支持非阻塞调用
    async enhanceSearchQuery(query, language = 'zh', options = {}) {
        if (!this.isAvailable()) {
            // console.debug('Gemini assistant not available');
            return { enhanced: false, query: query, language };
        }
        
        const startTime = Date.now();
        
        try {
            const prompt = this.buildEnhancePrompt(query, language);
            // console.debug('Gemini prompt:', prompt.substring(0, 100) + '...');

            // 智能超时控制 - 使用递进超时策略
            const timeout = options.timeout || this.getAdaptiveTimeout(options.attempt || 1);
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error(`Gemini API timeout after ${timeout}ms`)), timeout)
            );

            // console.debug('Using enhanced API with timeout');
            const response = await Promise.race([
                this.callGeminiAPI(prompt),
                timeoutPromise
            ]);

            const processingTime = Date.now() - startTime;

            if (response && response.keywords) {
                // console.debug(`Gemini enhancement successful in ${processingTime}ms:`, response);
                return {
                    enhanced: true,
                    originalQuery: query,
                    language,
                    enhancedQuery: response.keywords.join(' '),
                    keywords: response.keywords,
                    intent: response.intent,
                    suggestions: response.suggestions || [],
                    processingTime,
                    // 增加用于结果合并的元数据
                    confidence: response.confidence || 0.8,
                    categories: response.categories || [],
                    additionalResults: response.additionalResults || []
                };
            }
        } catch (error) {
            const processingTime = Date.now() - startTime;
            
            // 如果是超时错误且允许重试，尝试重试
            if (error.message.includes('timeout') && (options.attempt || 1) < 3) {
                const nextAttempt = (options.attempt || 1) + 1;
                // console.debug(`API超时，尝试第${nextAttempt}次重试...`);
                
                // 递增重试延迟
                await this.delay(200 * nextAttempt);
                return this.enhanceSearchQuery(query, language, { 
                    ...options, 
                    attempt: nextAttempt 
                });
            }
            
            // 静默处理错误，不在控制台显示警告，但记录性能数据
            // console.debug(`Gemini search enhancement failed after ${processingTime}ms:`, error.message);
        }
        
        return { 
            enhanced: false, 
            query: query, 
            language,
            processingTime: Date.now() - startTime
        };
    }

    // 为统一搜索引擎提供的别名方法
    async enhanceQuery(query, language = 'zh', options = {}) {
        return this.enhanceSearchQuery(query, language, options);
    }
    
    // 语言检测（简单启发式）：返回 { language: 'zh'|'en'|'ms', scores: { zh, en, ms }, isMixed }
    detectLanguage(text) {
        const result = { language: 'zh', scores: { zh: 0, en: 0, ms: 0 }, isMixed: false };
        if (!text || typeof text !== 'string') return result;

        const zhMatches = text.match(/[\u4e00-\u9fff]/g) || [];
        const latinMatches = text.match(/[A-Za-z]/g) || [];
        const digits = text.match(/[0-9]/g) || [];

        // Malay 常见词
        const msWords = ['yang','dan','atau','tidak','boleh','sila','untuk','dengan','kepada','dari','akan','sudah','belum','akaun','pemandu','pendaftaran','pengeluaran','bayaran','log masuk','kata laluan','aplikasi'];
        const lower = text.toLowerCase();
        let msHits = 0;
        for (const w of msWords) {
            const re = new RegExp(`(^|\b|[^A-Za-z])${w}($|\b|[^A-Za-z])`, 'g');
            if ((lower.match(re) || []).length > 0) msHits++;
        }

        // 英文常见停用词（少量）
        const enWords = ['the','is','are','and','or','not','please','how','what','when','why','login','withdraw','payment','driver','account','app'];
        let enHits = 0;
        for (const w of enWords) {
            const re = new RegExp(`(^|\b|[^A-Za-z])${w}($|\b|[^A-Za-z])`, 'g');
            if ((lower.match(re) || []).length > 0) enHits++;
        }

        const zhScore = zhMatches.length;
        // 拉丁字母基础分 + 词命中加权
        const latinBase = latinMatches.length + digits.length * 0.2;
        const msScore = latinBase * 0.5 + msHits * 5;
        const enScore = latinBase * 0.5 + enHits * 5;

        result.scores = { zh: zhScore, en: enScore, ms: msScore };

        // 主导语言
        const entries = Object.entries(result.scores).sort((a,b) => b[1]-a[1]);
        result.language = (entries[0] && entries[0][1] > 0) ? entries[0][0] : 'zh';
        // 是否混合
        result.isMixed = entries[1] && entries[1][1] > 0 && (entries[1][1] / (entries[0][1] || 1)) > 0.35;
        return result;
    }

    // 模糊/不可理解检测：返回 { ambiguous: boolean, reasons: string[] }
    detectAmbiguity(text) {
        const reasons = [];
        if (!text || typeof text !== 'string' || text.trim().length < 2) {
            reasons.push('内容过短');
        }
        const hasMeaningful = /[\u4e00-\u9fffA-Za-z0-9]/.test(text || '');
        if (!hasMeaningful) {
            reasons.push('缺少有效文字');
        }
        const onlyPunct = /^[\s\p{P}]+$/u.test(text || '');
        if (onlyPunct) {
            reasons.push('只有标点符号');
        }
        // 连续无意义字符
        if (/(.)\1{4,}/.test(text || '')) {
            reasons.push('包含重复无意义字符');
        }
        return { ambiguous: reasons.length > 0, reasons };
    }

    // 智能对话方法 - 与FAQ数据集成
    async chat(message, language = undefined) {
        // 自动语言选择
        const langDetected = this.detectLanguage(message);
        const lang = language || langDetected.language || 'zh';

        // 添加到对话历史
        this.addToChatHistory('user', message, lang);

        // 如果问题模糊/不可理解，直接返回澄清提示（使用检测到的主语言）
        const ambiguity = this.detectAmbiguity(message);
        if (ambiguity.ambiguous) {
            return this.generateClarificationPrompt(ambiguity.reasons, lang);
        }

        // 首先尝试从FAQ数据中查找直接匹配
        const faqResults = await this.searchFAQData(message, lang);

        let response = '';

        if (faqResults.directMatch) {
            // 找到直接匹配的FAQ，返回详细答案
            response = this.formatFAQResponse(faqResults.directMatch, lang, faqResults.relatedQuestions);
        } else if (faqResults.relatedQuestions.length > 0) {
            // 找到相关FAQ，使用AI生成基于FAQ知识库的回答

            if (this.isAvailable()) {
                try {
                    response = await this.generateFAQBasedResponse(message, faqResults.relatedQuestions, lang);
                } catch (error) {
                    console.warn('AI回答生成失败，使用FAQ搜索结果:', error);
                    response = this.formatSearchResults(faqResults.relatedQuestions, lang);
                }
            } else {
                // AI不可用时，返回搜索结果
                response = this.formatSearchResults(faqResults.relatedQuestions, lang);
            }
        } else {
            // 没有找到相关FAQ，返回引导信息
            response = this.generateGuidanceResponse(lang);
        }

        // 添加助手回复到对话历史
        this.addToChatHistory('assistant', response, lang);

        return response;
    }

    // 添加到对话历史
    addToChatHistory(role, content, language) {
        this.chatHistory.push({
            role,
            content,
            language,
            timestamp: new Date().toISOString()
        });

    // 保持对话历史在合理长度内（最多10轮对话=20条消息）
        if (this.chatHistory.length > 20) {
            this.chatHistory = this.chatHistory.slice(-20);
        }
    }

    // 获取对话上下文（最近5轮对话 = 10条消息）
    getChatContext(language) {
        return this.chatHistory
            .filter(item => item.language === language)
            .slice(-10) // 最近5轮对话
            .map(item => `${item.role}: ${item.content}`)
            .join('\n');
    }

    // 清除对话历史
    clearChatHistory() {
        this.chatHistory = [];
    }

    // 智能搜索建议
    async getSearchSuggestions(query, availableQuestions, language = 'zh') {
        if (!this.isAvailable()) {
            return [];
        }

        try {
            const prompt = this.buildSuggestionPrompt(query, availableQuestions, language);
            const response = await this.callGeminiAPI(prompt);

            if (response && response.suggestions) {
                return response.suggestions;
            }
        } catch (error) {
            console.warn('Gemini suggestions failed:', error);
        }

        return [];
    }
    
    // 搜索FAQ数据
    async searchFAQData(query, language) {
        if (!this.dataManager) {
            console.warn('DataManager未设置，无法搜索FAQ数据');
            return { directMatch: null, relatedQuestions: [] };
        }

        try {
            // 使用DataManager搜索相关问题
            const searchResults = await this.dataManager.searchQuestions(query, language);

            let directMatch = null;
            const relatedQuestions = [];

            // 分析搜索结果，寻找直接匹配
            for (const result of (Array.isArray(searchResults) ? searchResults : []).slice(0, 10)) { // 只处理前10个结果
                // 兼容返回结果可能为 { question, score } 的包装结构
                const question = result?.question || result;
                const score = result?.score || result?.similarity || 0;

                // 安全获取标题（多语言回退）
                const title = (question?.title?.[language] || question?.title?.zh || question?.title?.en || '');

                // 如果相似度很高，认为是直接匹配
                if (score > 0.8 || this.isDirectMatch(query, title)) {
                    directMatch = question;
                    break;
                } else if (question) {
                    relatedQuestions.push(question);
                }
            }

            return {
                directMatch,
                relatedQuestions: relatedQuestions.slice(0, 5) // 最多5个相关问题
            };

        } catch (error) {
            console.error('FAQ数据搜索失败:', error);
            return { directMatch: null, relatedQuestions: [] };
        }
    }

    // 判断是否为直接匹配
    isDirectMatch(query, title) {
        if (!query || !title) return false;
        const queryLower = String(query).toLowerCase().trim();
        const titleLower = String(title).toLowerCase().trim();

        // 简单的直接匹配逻辑
        return queryLower === titleLower ||
               titleLower.includes(queryLower) ||
               queryLower.includes(titleLower);
    }

    // 构建增强搜索的提示词
    buildEnhancePrompt(query, language) {
        const langMap = {
            'zh': '中文',
            'en': '英文',
            'ms': '马来文'
        };

    return `你是一个专业的司机FAQ搜索助手。用户输入了搜索查询："${query}"，语言是${langMap[language]}。

请注意：所有回复请使用 ${langMap[language]} 回答。

请分析这个查询并提供：
1. 相关的关键词（包括同义词、相关术语）
2. 用户的搜索意图
3. 可能的搜索建议

请以JSON格式回复，确保JSON格式完整且有效：
{
    "keywords": ["关键词1", "关键词2", "关键词3"],
    "intent": "用户搜索意图描述",
    "suggestions": ["建议1", "建议2", "建议3"]
}

重要提示：
- 确保JSON格式完全正确，不要截断
- keywords数组至少包含3个相关关键词
- intent字段要详细描述用户意图
- suggestions数组至少包含2个搜索建议
- 专注于司机相关的术语，如：注册、APP使用、订单、支付、评价、车辆、安全等主题。

只返回JSON，不要添加其他说明文字。`;
    }
    
    // 格式化FAQ直接匹配响应
    formatFAQResponse(faqQuestion, language, relatedQuestions = []) {
        const categories = this.dataManager ? this.dataManager.getCategories() : {};
        const category = categories[faqQuestion.category];
        const categoryName = category ? category.name[language] : '未知分类';
        const categoryIcon = category ? category.icon : '📋';

        let response = `✅ **${faqQuestion.title[language]}**\n\n`;
        response += `📋 **问题编号**: ${faqQuestion.id}\n`;
        response += `${categoryIcon} **分类**: ${categoryName}\n\n`;
        response += `📝 **详细解答**:\n${faqQuestion.content[language]}\n\n`;

        // 添加相关问题推荐
        if (relatedQuestions.length > 0) {
            response += `🔗 **相关问题推荐**:\n`;
            relatedQuestions.slice(0, 3).forEach((q, index) => {
                response += `${index + 1}. ${q.title[language]} (${q.id})\n`;
            });
        }

        return response;
    }

    // 格式化搜索结果响应
    formatSearchResults(questions, language) {
        const safeQuestions = Array.isArray(questions) ? questions : [];
        if (safeQuestions.length === 0) {
            return this.generateGuidanceResponse(language);
        }

        let response = `🔍 我找到了以下相关信息:\n\n`;

        safeQuestions.slice(0, 3).forEach((question, index) => {
            const categories = this.dataManager ? this.dataManager.getCategories() : {};
            const category = categories?.[question?.category];
            const categoryIcon = category ? category.icon : '📋';

            const title = (question?.title?.[language] || question?.title?.zh || question?.title?.en || '');
            const content = (question?.content?.[language] || question?.content?.zh || question?.content?.en || '');
            const categoryName = (category?.name?.[language] || category?.name?.zh || category?.name?.en || '未知分类');

            response += `${index + 1}. **${title}** (${question?.id || ''})\n`;
            response += `   ${categoryIcon} ${categoryName}\n`;
            response += `   ${content.substring(0, 100)}...\n\n`;
        });

        response += `💡 点击问题编号可查看完整答案，或继续提问获取更多帮助。`;
        
        console.debug('[SearchResults] 格式化搜索结果完成，返回对象格式');
        return {
            type: 'search_results',
            content: response,
            language: language,
            related: safeQuestions.slice(0, 3)
        };
    }

    // 生成引导响应
    generateGuidanceResponse(language) {
        const responses = {
            'zh': `😊 抱歉，我没有找到直接相关的FAQ答案。

🔍 **建议您可以尝试**:
• 使用更具体的关键词，如"登录问题"、"提现流程"等
• 浏览不同分类查找相关问题
• 联系客服获取人工帮助

📋 **常见问题分类**:
📱 技术问题 | 💰 财务问题 | 🛎️ 服务流程
🚗 注册入门 | 💬 沟通管理 | 🚨 紧急处理`,

            'en': `😊 Sorry, I couldn't find directly related FAQ answers.

🔍 **You can try**:
• Use more specific keywords like "login issues", "withdrawal process"
• Browse different categories for related questions
• Contact customer service for manual assistance

📋 **Common Question Categories**:
📱 Technical | 💰 Financial | 🛎️ Service Process
🚗 Registration | 💬 Communication | 🚨 Emergency`,

            'ms': `😊 Maaf, saya tidak dapat mencari jawapan FAQ yang berkaitan secara langsung.

🔍 **Anda boleh cuba**:
• Gunakan kata kunci yang lebih spesifik seperti "masalah log masuk", "proses pengeluaran"
• Layari kategori berbeza untuk soalan berkaitan
• Hubungi khidmat pelanggan untuk bantuan manual

📋 **Kategori Soalan Biasa**:
📱 Teknikal | 💰 Kewangan | 🛎️ Proses Perkhidmatan
🚗 Pendaftaran | 💬 Komunikasi | 🚨 Kecemasan`
        };

        const responseText = responses[language] || responses['zh'];
        console.debug('[Guidance] 生成引导响应，返回对象格式');
        
        return {
            type: 'guidance',
            content: responseText,
            language: language,
            related: []
        };
    }

    // 构建建议提示词
    buildSuggestionPrompt(query, availableQuestions, language) {
    const langMap = { zh: '中文', en: '英文', ms: '马来文' };
        const questionSample = availableQuestions.slice(0, 20).map(q =>
            `- ${q.id}: ${q.title[language]}`
        ).join('\n');

    return `用户搜索："${query}"

请注意：请使用 ${langMap[language]} 回复。

以下是部分可用的FAQ问题：
${questionSample}

请基于用户查询和可用问题，推荐最相关的3-5个问题ID，以JSON数组格式回复：
["Q001", "Q002", "Q003"]

只返回问题ID，确保ID在提供的列表中存在。`;
    }
    
    // 调用Gemini API - 已集成安全验证
    async callGeminiAPI(prompt) {
        // 速率限制检查
        if (!this.rateLimiter.recordRequest()) {
            const waitTime = this.rateLimiter.getTimeUntilNextRequest();
            throw new Error(`API请求过于频繁，请等待${Math.ceil(waitTime/1000)}秒后重试`);
        }

        // 构建请求URL
        const url = `${this.config.endpoint}${this.config.model}:generateContent?key=${this.config.apiKey}`;
        
        // 请求体安全验证
        let cleanPrompt = prompt;
        if (this.securityValidator) {
            try {
                cleanPrompt = this.securityValidator.validateSearchInput(prompt, {
                    maxLength: 2000,
                    minLength: 1,
                    stripHtml: true
                });
            } catch (error) {
                throw new Error(`输入验证失败: ${error.message}`);
            }
        }
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: cleanPrompt
                }]
            }],
            generationConfig: {
                temperature: this.config.temperature,
                maxOutputTokens: this.config.maxTokens,
                candidateCount: 1
            }
        };
        
        // 请求选项安全配置
        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache'
            },
            body: JSON.stringify(requestBody)
            // 注意：移除了无效的timeout配置，使用Promise.race实现超时
        };
        
        // API请求安全验证
        if (this.securityValidator) {
            try {
                this.securityValidator.validateApiRequest(url, requestOptions);
            } catch (error) {
                throw new Error(`API请求安全验证失败: ${error.message}`);
            }
        }
        
        // console.debug('🔒 安全验证通过，调用Gemini API');

        const response = await fetch(url, requestOptions);

        if (!response.ok) {
            // console.debug('Gemini API error response:', response.status, response.statusText);
            throw new Error(`Gemini API error: ${response.status}`);
        }

        const data = await response.json();
        // console.debug('Gemini API response:', data);

        if (data.candidates && data.candidates[0]) {
            let text = '';

            // Handle different response structures
            if (data.candidates[0].content) {
                if (data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
                    text = data.candidates[0].content.parts[0].text;
                } else if (data.candidates[0].content.text) {
                    text = data.candidates[0].content.text;
                }
            } else if (data.candidates[0].text) {
                text = data.candidates[0].text;
            }

            if (text) {
                // console.debug('Gemini response text:', text);
                try {
                    const result = JSON.parse(text);
                    // console.debug('Parsed JSON result:', result);
                    return result;
                } catch (e) {
                    // console.debug('Failed to parse as JSON, trying to extract JSON part');
                    // 如果不是JSON，尝试提取JSON部分
                    // 首先尝试提取markdown代码块中的JSON
                    const codeBlockMatch = text.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
                    if (codeBlockMatch) {
                        try {
                            const result = JSON.parse(codeBlockMatch[1]);
                            // console.debug('Extracted JSON from code block:', result);
                            return result;
                        } catch (e2) {
                            // console.debug('Failed to parse JSON from code block, trying to fix incomplete JSON');
                            // 尝试修复不完整的JSON
                            const fixedJson = this.fixIncompleteJSON(codeBlockMatch[1]);
                            if (fixedJson) {
                                try {
                                    const result = JSON.parse(fixedJson);
                                    // console.debug('Fixed and parsed incomplete JSON:', result);
                                    return result;
                                } catch (e3) {
                                    // console.debug('Failed to parse fixed JSON');
                                }
                            }
                        }
                    }
                    
                    // 如果代码块提取失败，尝试直接提取JSON对象
                    const jsonMatch = text.match(/\{[\s\S]*?\}/);
                    if (jsonMatch) {
                        try {
                            const result = JSON.parse(jsonMatch[0]);
                            // console.debug('Extracted JSON result:', result);
                            return result;
                        } catch (e2) {
                            // console.debug('Failed to parse extracted JSON, trying to fix incomplete JSON');
                            // 尝试修复不完整的JSON
                            const fixedJson = this.fixIncompleteJSON(jsonMatch[0]);
                            if (fixedJson) {
                                try {
                                    const result = JSON.parse(fixedJson);
                                    // console.debug('Fixed and parsed incomplete JSON:', result);
                                    return result;
                                } catch (e3) {
                                    // console.debug('Failed to parse fixed JSON');
                                }
                            }
                        }
                    }
                    
                    throw new Error('Invalid JSON response from Gemini');
                }
            }
        }
        
        throw new Error('No valid response from Gemini');
    }

    // 修复不完整的JSON响应
    fixIncompleteJSON(jsonText) {
        if (!jsonText || typeof jsonText !== 'string') return null;

        try {
            // 移除末尾的逗号
            let cleaned = jsonText.trim();
            
            // 如果以逗号结尾，移除它
            if (cleaned.endsWith(',')) {
                cleaned = cleaned.slice(0, -1);
            }
            
            // 检查是否有未闭合的数组或对象
            const openBraces = (cleaned.match(/\{/g) || []).length;
            const closeBraces = (cleaned.match(/\}/g) || []).length;
            const openBrackets = (cleaned.match(/\[/g) || []).length;
            const closeBrackets = (cleaned.match(/\]/g) || []).length;
            
            // 补全缺失的闭合括号
            while (openBraces > closeBraces) {
                cleaned += '}';
                closeBraces++;
            }
            
            while (openBrackets > closeBrackets) {
                cleaned += ']';
                closeBrackets++;
            }
            
            // 如果JSON以引号开头但没有闭合，补全引号
            if (cleaned.startsWith('"') && !cleaned.endsWith('"') && !cleaned.endsWith('",')) {
                cleaned += '"';
            }
            
            // 验证修复后的JSON是否有效
            JSON.parse(cleaned);
            return cleaned;
            
        } catch (e) {
            console.debug('无法修复不完整的JSON:', e.message);
            return null;
        }
    }

    // 调用Gemini API用于聊天 - 返回原始文本
    async callGeminiAPIForChat(prompt) {
        // 速率限制检查
        if (!this.rateLimiter.recordRequest()) {
            const waitTime = this.rateLimiter.getTimeUntilNextRequest();
            throw new Error(`API请求过于频繁，请等待${Math.ceil(waitTime/1000)}秒后重试`);
        }

        // 构建请求URL
        const url = `${this.config.endpoint}${this.config.model}:generateContent?key=${this.config.apiKey}`;
        
        // 请求体安全验证
        let cleanPrompt = prompt;
        if (this.securityValidator) {
            try {
                cleanPrompt = this.securityValidator.validateSearchInput(prompt, {
                    maxLength: 2000,
                    minLength: 1,
                    stripHtml: true
                });
            } catch (error) {
                throw new Error(`输入验证失败: ${error.message}`);
            }
        }
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: cleanPrompt
                }]
            }],
            generationConfig: {
                temperature: this.config.temperature,
                maxOutputTokens: this.config.maxTokens,
                candidateCount: 1
            }
        };
        
        // 请求选项安全配置
        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache'
            },
            body: JSON.stringify(requestBody)
            // 注意：移除了无效的timeout配置，使用Promise.race实现超时
        };
        
        // API请求安全验证
        if (this.securityValidator) {
            try {
                this.securityValidator.validateApiRequest(url, requestOptions);
            } catch (error) {
                throw new Error(`API请求安全验证失败: ${error.message}`);
            }
        }
        
        // console.debug('🔒 安全验证通过，调用Gemini API (聊天模式)');

        const response = await fetch(url, requestOptions);

        if (!response.ok) {
            // console.debug('Gemini API error response:', response.status, response.statusText);
            throw new Error(`Gemini API error: ${response.status}`);
        }

        const data = await response.json();
        // console.debug('Gemini API response (聊天):', data);

        if (data.candidates && data.candidates[0]) {
            let text = '';

            // Handle different response structures
            if (data.candidates[0].content) {
                if (data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
                    text = data.candidates[0].content.parts[0].text;
                } else if (data.candidates[0].content.text) {
                    text = data.candidates[0].content.text;
                }
            } else if (data.candidates[0].text) {
                text = data.candidates[0].text;
            }

            if (text) {
                // console.debug('Gemini response text (聊天):', text);
                return text; // 返回原始文本，不尝试解析JSON
            }
        }
        
        throw new Error('No valid response from Gemini');
    }
    


    // 生成基于FAQ知识库的AI回答
    async generateFAQBasedResponse(userMessage, relatedQuestions, language, options = {}) {
        console.debug('🤖 [Gemini] 开始生成FAQ回答:', {
            userMessage,
            relatedQuestionsCount: relatedQuestions?.length || 0,
            relatedQuestionIds: relatedQuestions?.map(q => q.id) || [],
            language,
            attempt: options.attempt || 1
        });

        const prompt = this.buildChatPrompt(userMessage, relatedQuestions, language);
        console.debug('🤖 [Gemini] 提示词构建完成:', {
            promptLength: prompt.length,
            promptPreview: prompt.substring(0, 200) + '...',
            fullPrompt: prompt // 完整提示词
        });

        const attempt = options.attempt || 1;

        try {
            const timeout = this.getAdaptiveTimeout(attempt);
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error(`Gemini Chat API timeout after ${timeout}ms`)), timeout)
            );

            const startTime = Date.now();
            console.debug(`[Chat] Calling Gemini API. Attempt: ${attempt}, Timeout: ${timeout}ms`);

            const response = await Promise.race([
                this.callGeminiAPIForChat(prompt),
                timeoutPromise
            ]);

            const endTime = Date.now();
            console.debug(`[Chat] API call completed in ${endTime - startTime}ms (attempt ${attempt})`);

            return this.parseChatResponse(response, relatedQuestions, language);

        } catch (error) {
            console.warn(`[Chat] AI response generation failed on attempt ${attempt}:`, error.message);
            console.debug(`[Chat] Error details:`, error);

            if (error.message.includes('timeout') && attempt < 3) {
                const nextAttempt = attempt + 1;
                console.debug(`[Chat] API timeout detected, retrying... (Attempt ${nextAttempt})`);
                await this.delay(200 * nextAttempt);
                return this.generateFAQBasedResponse(userMessage, relatedQuestions, language, { ...options, attempt: nextAttempt });
            }

            // Fallback to search results if all retries fail
            console.warn('[Chat] All retries failed or error is not a timeout. Falling back to search results.');
            console.debug('[Chat] Fallback reason - Error type:', typeof error, 'Message includes timeout:', error.message.includes('timeout'), 'Attempt:', attempt);
            return this.formatSearchResults(relatedQuestions, language);
        }
    }

    // 构建对话提示词
    buildChatPrompt(userMessage, relatedQuestions, language) {
        console.debug('📝 [Gemini] 开始构建提示词:', {
            userMessage,
            relatedQuestionsCount: relatedQuestions?.length || 0,
            language
        });

        const langMap = {
            'zh': '中文',
            'en': 'English',
            'ms': 'Bahasa Malaysia'
        };

        // 保障relatedQuestions结构健壮性，避免读取undefined属性
        const safeQuestions = Array.isArray(relatedQuestions) ? relatedQuestions : [];
        console.debug('📝 [Gemini] 处理搜索结果:', {
            safeQuestionsCount: safeQuestions.length,
            questionsData: safeQuestions.map(q => ({
                id: q?.id || q?.question?.id,
                title: q?.title || q?.question?.title,
                hasContent: !!(q?.content || q?.question?.content),
                category: q?.category || q?.question?.category,
                rawStructure: Object.keys(q || {})
            }))
        });

        const faqContext = safeQuestions.map(q => {
            // 适配两种数据结构：直接的问题对象或包含question属性的搜索结果
            const question = q?.question || q;
            const title = (question && question.title && (question.title[language] || question.title.zh || question.title.en)) || '';
            const content = (question && question.content && (question.content[language] || question.content.zh || question.content.en)) || '';
            const id = (question && question.id) || '';
            return `问题: ${title}\n答案: ${content}\n编号: ${id}\n`;
        }).join('\n---\n');

        console.debug('📝 [Gemini] FAQ上下文构建完成:', {
            faqContextLength: faqContext.length,
            faqContextPreview: faqContext.substring(0, 300) + '...'
        });

        // 获取对话上下文
        const chatContext = this.getChatContext(language);
        const contextSection = chatContext ? `\n对话历史:\n${chatContext}\n` : '';

        const langName = langMap[language] || '中文';
        const finalPrompt = `你是GoMyHire司机FAQ智能助手。${contextSection}

当前用户问题："${userMessage}"

以下是相关的FAQ知识库内容：
${faqContext}

请注意：请使用 ${langName} 回答用户问题。要求：
1. 优先使用FAQ知识库中的准确信息
2. 考虑对话上下文，提供连贯的回答
3. 回答要专业、友好、有帮助
4. 如果涉及多个FAQ，可以综合回答
5. 在回答末尾提及相关的FAQ编号
6. 保持回答简洁明了，不超过200字

请直接回答，不要使用JSON格式。`;

        console.debug('📝 [Gemini] 最终提示词构建完成:', {
            totalLength: finalPrompt.length,
            hasContext: !!chatContext,
            hasFaqContent: faqContext.length > 0
        });

        return finalPrompt;
    }

    // 解析对话响应
    parseChatResponse(response, relatedQuestions, language) {
        console.debug('[Chat] 开始解析API响应:', response);
        let content = '';

        if (typeof response === 'string') {
            content = response;
            console.debug('[Chat] 响应为字符串:', content.substring(0, 100) + '...');
        } else if (response && response.candidates && response.candidates[0]) {
            const candidate = response.candidates[0];
            if (candidate.content && candidate.content.parts && candidate.content.parts[0] && candidate.content.parts[0].text) {
                content = candidate.content.parts[0].text;
                console.debug('[Chat] 从candidate.content.parts[0].text获取内容:', content.substring(0, 100) + '...');
            } else if (candidate.content && candidate.content.text) {
                content = candidate.content.text;
                console.debug('[Chat] 从candidate.content.text获取内容:', content.substring(0, 100) + '...');
            } else if (candidate.text) {
                content = candidate.text;
                console.debug('[Chat] 从candidate.text获取内容:', content.substring(0, 100) + '...');
            }
        } else if (response && response.enhanced === false) {
            // API返回的未增强结果，回退到搜索结果
            console.debug('[Chat] 响应标记为未增强，回退到搜索结果');
            return this.formatSearchResults(relatedQuestions, language);
        }

        if (!content) {
            // 若无法解析内容，回退到搜索结果
            console.warn('[Chat] 无法解析响应内容，回退到搜索结果. 响应结构:', typeof response, Object.keys(response || {}));
            return this.formatSearchResults(relatedQuestions, language);
        }

        console.debug('[Chat] 成功解析响应，内容长度:', content.length);
        
        // 处理相关问题数据结构 - 提取问题对象
        const processedRelated = Array.isArray(relatedQuestions) ? 
            relatedQuestions.slice(0, 3).map(item => {
                // 如果是搜索结果格式{question: {...}, score: ...}，提取question对象
                if (item && item.question) {
                    return item.question;
                }
                // 如果已经是问题对象，直接返回
                return item;
            }).filter(q => q && q.id) : []; // 确保有有效的ID
            
        console.debug('[Chat] 处理后的相关问题:', {
            originalCount: relatedQuestions?.length || 0,
            processedCount: processedRelated.length,
            sampleIds: processedRelated.slice(0, 2).map(q => q.id)
        });
        
        return {
            type: 'ai',
            content,
            language,
            related: processedRelated
        };
    }
    
    // 解析Gemini响应
    parseGeminiResponse(responseText, originalPrompt) {
        try {
            // 首先尝试直接解析为JSON
            const result = JSON.parse(responseText);
            if (result && result.keywords) {
                return {
                    enhanced: true,
                    originalQuery: this.extractQueryFromPrompt(originalPrompt),
                    enhancedQuery: result.keywords.join(' '),
                    keywords: result.keywords,
                    intent: result.intent,
                    suggestions: result.suggestions || []
                };
            }
        } catch (e) {
            // 如果不是纯JSON，尝试提取JSON部分
            try {
                const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/) || 
                                 responseText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const result = JSON.parse(jsonMatch[1] || jsonMatch[0]);
                    if (result && result.keywords) {
                        return {
                            enhanced: true,
                            originalQuery: this.extractQueryFromPrompt(originalPrompt),
                            enhancedQuery: result.keywords.join(' '),
                            keywords: result.keywords,
                            intent: result.intent,
                            suggestions: result.suggestions || []
                        };
                    }
                }
            } catch (e2) {
                console.warn('Failed to parse JSON from response:', responseText.substring(0, 100));
            }
        }
        
        return { enhanced: false, query: this.extractQueryFromPrompt(originalPrompt) };
    }
    
    // 从提示词中提取原始查询
    extractQueryFromPrompt(prompt) {
        const match = prompt.match(/："(.+?)"/);
        return match ? match[1] : '';
    }
    
    // 流式搜索增强 - 新增核心方法
    async streamingEnhanceSearchQuery(query, language, onProgress, onComplete) {
        if (!this.isAvailable()) {
            console.debug('Gemini assistant not available for streaming');
            onComplete({ enhanced: false, query: query });
            return;
        }
        
        try {
            const prompt = this.buildStreamingPrompt(query, language);
            console.debug('Starting streaming search enhancement for:', query);
            
            await this.callStreamingGeminiAPI(prompt, {
                onProgress: (data) => {
                    try {
                        const parsed = this.parseStreamingChunk(data);
                        if (parsed) {
                            onProgress({
                                stage: parsed.stage || 'processing',
                                keywords: parsed.keywords || [],
                                suggestions: parsed.suggestions || [],
                                confidence: parsed.confidence || 0.5
                            });
                        }
                    } catch (error) {
                        console.debug('Streaming progress parse error:', error);
                    }
                },
                onComplete: (finalResult) => {
                    onComplete(finalResult || { enhanced: false, query: query });
                }
            });
            
        } catch (error) {
            console.debug('Streaming search enhancement failed:', error.message);
            onComplete({ enhanced: false, query: query });
        }
    }
    
    // 构建流式搜索提示词
    buildStreamingPrompt(query, language) {
        const langMap = {
            'zh': '中文',
            'en': 'English', 
            'ms': 'Bahasa Malaysia'
        };
        
    return `你是GoMyHire司机FAQ智能搜索助手。用户搜索查询："${query}"，语言：${langMap[language]}。

请注意：请使用 ${langMap[language]} 回复。

请分阶段分析并提供流式响应：

阶段1 - 意图理解：
分析用户搜索意图，识别问题类型（技术/财务/服务/注册/沟通/紧急）

阶段2 - 关键词扩展：  
生成相关关键词和同义词，包括：
- 核心关键词
- 相关术语
- 常见变体

阶段3 - 搜索建议：
基于FAQ知识，提供3-5个精准的搜索建议

请以JSON格式逐阶段回复：
{"stage": "intent", "intent": "问题类型", "confidence": 0.8}
{"stage": "keywords", "keywords": ["关键词1", "关键词2"], "confidence": 0.9}
{"stage": "suggestions", "suggestions": ["建议1", "建议2"], "confidence": 0.85}

专注司机相关场景：APP使用、订单处理、支付提现、客户服务、车辆管理、安全规范等。`;
    }
    
    // 流式API调用
    async callStreamingGeminiAPI(prompt, options = {}) {
        const url = `${this.config.endpoint}${this.config.model}:streamGenerateContent?key=${this.config.apiKey}`;
        const requestBody = {
            contents: [{ parts: [{ text: prompt }] }],
            generationConfig: {
                temperature: this.config.temperature || 0.1,
                maxOutputTokens: this.config.maxTokens || 1000,
                candidateCount: 1
            }
        };
        
        console.debug('Calling streaming Gemini API:', url.substring(0, 80) + '...');
        
        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
        });
        
        if (!response.ok) {
            throw new Error(`Streaming API error: ${response.status} ${response.statusText}`);
        }
        
        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let finalResult = { enhanced: false, keywords: [], suggestions: [] };
        
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || ''; // 保留不完整的行
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const chunk = line.slice(6).trim();
                        if (chunk && chunk !== '[DONE]') {
                            try {
                                const data = JSON.parse(chunk);
                                if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                                    const text = data.candidates[0].content.parts[0].text;
                                    
                                    // 处理流式数据块
                                    if (options.onProgress) {
                                        options.onProgress(text);
                                    }
                                    
                                    // 累积最终结果
                                    const parsed = this.parseStreamingChunk(text);
                                    if (parsed) {
                                        finalResult = this.mergeResults(finalResult, parsed);
                                    }
                                }
                            } catch (parseError) {
                                console.debug('Parse streaming chunk error:', parseError);
                            }
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
        
        if (options.onComplete) {
            options.onComplete(finalResult);
        }
        
        return finalResult;
    }
    
    // 解析流式数据块
    parseStreamingChunk(chunk) {
        try {
            // 尝试解析JSON
            const data = JSON.parse(chunk);
            return data;
        } catch (e) {
            // 如果不是JSON，尝试提取JSON部分
            // 首先尝试提取markdown代码块中的JSON
            const codeBlockMatch = chunk.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
            if (codeBlockMatch) {
                try {
                    const parsed = JSON.parse(codeBlockMatch[1]);
                    if (parsed.stage || parsed.keywords || parsed.suggestions) {
                        return parsed;
                    }
                } catch (e2) {
                    console.debug('Failed to parse JSON from streaming code block');
                }
            }
            
            // 如果代码块提取失败，尝试直接提取JSON对象
            const jsonMatches = chunk.match(/\{[\s\S]*?\}/g);
            if (jsonMatches) {
                for (const match of jsonMatches) {
                    try {
                        const parsed = JSON.parse(match);
                        if (parsed.stage || parsed.keywords || parsed.suggestions) {
                            return parsed;
                        }
                    } catch (innerError) {
                        continue;
                    }
                }
            }
        }
        return null;
    }
    
    // 合并结果
    mergeResults(existing, newData) {
        const merged = { ...existing };
        
        if (newData.intent) merged.intent = newData.intent;
        if (newData.keywords && newData.keywords.length > 0) {
            merged.keywords = [...new Set([...merged.keywords, ...newData.keywords])];
            merged.enhanced = true;
        }
        if (newData.suggestions && newData.suggestions.length > 0) {
            merged.suggestions = [...new Set([...merged.suggestions, ...newData.suggestions])];
        }
        if (newData.confidence) merged.confidence = newData.confidence;
        
        return merged;
    }

    // 设置启用状态
    setEnabled(enabled) {
        this.enabled = enabled;
    }
    

    
    // 设置启用状态
    setEnabled(enabled) {
        this.enabled = enabled;
    }

    // 获取自适应超时时间 - 递进策略
    getAdaptiveTimeout(attempt = 1) {
        const baseTimeout = 8000;  // 基础超时8秒（适合AI API）
        const maxTimeout = 15000;  // 最大超时15秒（与CONFIG保持一致）
        
        // 递进超时: 8s -> 12s -> 15s
        const timeouts = [8000, 12000, 15000];
        const timeout = attempt <= timeouts.length ? timeouts[attempt - 1] : maxTimeout;
        
        console.debug(`[Timeout] 自适应超时策略: 第${attempt}次尝试, 超时${timeout}ms`);
        return timeout;
    }
    
    // 延迟函数用于重试
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 网络状态检测 - 用于动态调整超时
    getNetworkCondition() {
        if (navigator.connection) {
            const connection = navigator.connection;
            const effectiveType = connection.effectiveType;
            
            // 基于网络类型调整超时
            const timeoutMultipliers = {
                'slow-2g': 3.0,
                '2g': 2.5,
                '3g': 1.5,
                '4g': 1.0
            };
            
            return {
                effectiveType,
                multiplier: timeoutMultipliers[effectiveType] || 1.0
            };
        }
        
        return { effectiveType: 'unknown', multiplier: 1.0 };
    }
    

}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GeminiSearchAssistant;
} else {
    window.GeminiSearchAssistant = GeminiSearchAssistant;
}
