# GoMyHire FAQ 系统 - 项目文档

**报告日期:** 2025年9月1日

## 1. 项目概述

GoMyHire FAQ System 是一个现代化的、响应式的司机常见问题解答系统，支持中文、英文、马来文三语切换。其核心特点是纯前端实现，除与 Google Gemini API 交互外，不依赖任何后端服务器。

## 2. 文档导航

*   **[技术指南 (Technical_Guide.md)](Technical_Guide.md)**
    *   API密钥设置、AI配置、布局问题排查、系统优化等。

*   **[部署指南 (Deployment_Guide.md)](Deployment_Guide.md)**
    *   Netlify部署流程与部署前检查清单。

*   **[内容与功能指南 (Content_And_Feature_Guide.md)](Content_And_Feature_Guide.md)**
    *   内容美化、FAQ集成、图片资源管理等。

*   **[开发日志 (Development_Log.md)](Development_Log.md)**
    *   包含所有历史开发、修复、重构和优化报告的存档。

## 3. 架构状态摘要

*   **CSS架构**: 已成功重构为模块化架构，包含设计令牌、主题化、工具类和组件样式。
*   **JavaScript架构**: 采用模块化结构，分离了核心逻辑、数据管理、AI集成和UI组件。
*   **数据管理**: FAQ数据、分类、标签集中在 `src/core/data.js`。
*   **环境变量**: 通过 `index.html` 的 `meta` 标签和 `localStorage` 管理。

## 4. 核心技术

*   **纯前端**: 无服务器依赖的静态应用。
*   **AI集成**: Google Gemini API 用于搜索增强和智能对话。
*   **多语言**: 支持中文、英文、马来文。
*   **响应式设计**: 移动优先，适配多种设备。

---
*本文档整合了 `docs/README.md` 和 `docs/currentstatus.md` 的核心内容。*
