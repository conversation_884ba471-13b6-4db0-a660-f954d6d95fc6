/**
 * GoMyHire Driver FAQ System - 简化版本
 * 目标：从68k行代码简化到5k行以下
 * 原则：简单胜过复杂，可维护性优先
 */

class SimpleFAQApp {
    constructor() {
        console.log('🚀 初始化简化版FAQ应用...');
        
        // 核心组件初始化
        this.i18n = new I18nManager();
        this.dataManager = new DataManager();
        this.currentLanguage = 'zh';
        
        // 简单的内存缓存
        this.searchCache = new Map();
        this.lastQuery = '';
        this.currentResults = [];
        
        // Gemini助手（可选）
        this.initializeGeminiAssistant();
        
        // 初始化UI
        this.initializeUI();
        
        console.log('✅ 简化版FAQ应用初始化完成');
    }

    // 简化的Gemini初始化
    initializeGeminiAssistant() {
        try {
            if (window.CONFIG?.gemini?.apiKey && window.CONFIG.gemini.apiKey !== 'your-gemini-api-key-here') {
                this.geminiAssistant = new GeminiSearchAssistant(window.CONFIG);
                this.geminiAssistant.setDataManager(this.dataManager);
                console.log('✅ Gemini助手已启用');
            } else {
                console.log('ℹ️ Gemini助手未配置，使用基础搜索');
                this.geminiAssistant = null;
            }
        } catch (error) {
            console.warn('⚠️ Gemini初始化失败，使用基础搜索:', error.message);
            this.geminiAssistant = null;
        }
    }

    // 简化的UI初始化
    initializeUI() {
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');
        
        if (!searchInput || !searchResults) {
            console.error('❌ 必需的UI元素未找到');
            return;
        }

        // 搜索输入事件
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length >= 2) {
                this.performSearch(query);
            } else if (query.length === 0) {
                this.clearResults();
            }
        });

        // 回车搜索
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const query = e.target.value.trim();
                if (query) {
                    this.performSearch(query);
                }
            }
        });

        // 语言切换
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = btn.getAttribute('data-lang');
                this.switchLanguage(lang);
            });
        });

        console.log('✅ UI事件监听器已设置');
    }

    // 核心搜索功能 - 极简实现
    async performSearch(query) {
        console.log('🔍 搜索:', query);
        
        // 检查缓存
        if (this.searchCache.has(query)) {
            console.log('📦 使用缓存结果');
            this.displayResults(this.searchCache.get(query), query);
            return;
        }

        // 显示加载状态
        this.showSearchLoading();

        try {
            // 基础文本搜索
            let results = this.basicSearch(query);
            
            // 如果有Gemini助手且结果较少，尝试AI增强
            if (this.geminiAssistant && results.length < 3) {
                try {
                    const aiResults = await this.geminiAssistant.searchFAQData(query, this.currentLanguage);
                    if (aiResults.relatedQuestions?.length > 0) {
                        // 合并AI结果
                        results = this.mergeResults(results, aiResults.relatedQuestions);
                        console.log('🤖 AI增强搜索结果');
                    }
                } catch (error) {
                    console.warn('⚠️ AI搜索失败，使用基础结果:', error.message);
                }
            }

            // 缓存结果（最多50个查询）
            if (this.searchCache.size >= 50) {
                const firstKey = this.searchCache.keys().next().value;
                this.searchCache.delete(firstKey);
            }
            this.searchCache.set(query, results);

            this.displayResults(results, query);
            this.lastQuery = query;
            this.currentResults = results;

        } catch (error) {
            console.error('❌ 搜索失败:', error);
            this.showSearchError('搜索时发生错误，请稍后重试');
        }
    }

    // 基础文本搜索 - 简单有效
    basicSearch(query) {
        const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);
        const allQuestions = this.dataManager.getAllQuestions();
        const results = [];

        allQuestions.forEach(question => {
            const title = (question.title[this.currentLanguage] || question.title.zh || '').toLowerCase();
            const content = (question.content[this.currentLanguage] || question.content.zh || '').toLowerCase();
            const tags = question.tags || [];
            
            let score = 0;

            // 计算匹配分数
            searchTerms.forEach(term => {
                if (title.includes(term)) score += 10;
                if (content.includes(term)) score += 3;
                tags.forEach(tag => {
                    if (tag.toLowerCase().includes(term)) score += 5;
                });
            });

            if (score > 0) {
                results.push({
                    ...question,
                    score,
                    matchType: 'basic'
                });
            }
        });

        // 按分数排序，返回前10个
        return results
            .sort((a, b) => b.score - a.score)
            .slice(0, 10);
    }

    // 合并搜索结果
    mergeResults(basicResults, aiResults) {
        const resultMap = new Map();
        
        // 添加基础结果
        basicResults.forEach(result => {
            resultMap.set(result.id, result);
        });
        
        // 添加AI结果（如果不重复）
        aiResults.forEach(result => {
            if (!resultMap.has(result.id)) {
                resultMap.set(result.id, {
                    ...result,
                    score: result.score || 1,
                    matchType: 'ai'
                });
            }
        });

        return Array.from(resultMap.values())
            .sort((a, b) => b.score - a.score)
            .slice(0, 10);
    }

    // 显示搜索结果 - 简化版本
    displayResults(results, query) {
        const container = document.getElementById('searchResults');
        if (!container) return;

        if (results.length === 0) {
            container.innerHTML = `
                <div class="no-results">
                    <h3>未找到相关问题</h3>
                    <p>尝试使用不同的关键词搜索</p>
                </div>
            `;
            return;
        }

        const html = results.map(result => this.createResultHTML(result, query)).join('');
        container.innerHTML = `
            <div class="search-summary">
                找到 ${results.length} 个相关问题
            </div>
            <div class="results-list">
                ${html}
            </div>
        `;

        // 添加点击事件
        container.querySelectorAll('.result-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.showQuestionDetail(results[index]);
            });
        });
    }

    // 创建结果HTML - 简化版本
    createResultHTML(result, query) {
        const title = result.title[this.currentLanguage] || result.title.zh;
        const content = result.content[this.currentLanguage] || result.content.zh;
        const excerpt = this.createExcerpt(content, 150);
        const score = Math.round((result.score || 0) * 10); // 简化分数显示

        return `
            <div class="result-item" data-id="${result.id}">
                <div class="result-header">
                    <h3 class="result-title">${this.highlightQuery(title, query)}</h3>
                    <span class="result-score">${score}/10</span>
                </div>
                <div class="result-content">
                    ${this.highlightQuery(excerpt, query)}
                </div>
                <div class="result-footer">
                    <span class="result-id">#${result.id}</span>
                    <span class="result-category">${this.getCategoryName(result.category)}</span>
                </div>
            </div>
        `;
    }

    // 高亮搜索词
    highlightQuery(text, query) {
        if (!query || !text) return text;
        
        const terms = query.split(/\s+/).filter(term => term.length > 0);
        let highlightedText = text;
        
        terms.forEach(term => {
            const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    // 创建内容摘要
    createExcerpt(content, maxLength) {
        const text = content.replace(/<[^>]*>/g, '').trim();
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    // 获取分类名称
    getCategoryName(categoryId) {
        const categories = this.dataManager.getCategories();
        const category = categories[categoryId];
        return category ? category.name[this.currentLanguage] || category.name.zh : '未分类';
    }

    // 正则表达式转义
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // 显示问题详情
    showQuestionDetail(question) {
        const modal = document.getElementById('questionModal');
        const title = document.getElementById('modalTitle');
        const content = document.getElementById('modalContent');
        
        if (modal && title && content) {
            title.textContent = question.title[this.currentLanguage] || question.title.zh;
            content.innerHTML = question.content[this.currentLanguage] || question.content.zh;
            modal.style.display = 'flex';
        }
    }

    // 语言切换
    switchLanguage(lang) {
        if (this.currentLanguage === lang) return;
        
        this.currentLanguage = lang;
        this.i18n.setLanguage(lang);
        
        // 清空缓存，因为语言变了
        this.searchCache.clear();
        
        // 如果有当前搜索，重新执行
        if (this.lastQuery) {
            this.performSearch(this.lastQuery);
        }
        
        // 更新UI语言
        this.updateUILanguage();
        
        console.log('🌐 语言切换至:', lang);
    }

    // 更新UI语言
    updateUILanguage() {
        // 更新语言按钮状态
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.toggle('active', btn.getAttribute('data-lang') === this.currentLanguage);
        });

        // 更新占位符和按钮文本
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.placeholder = this.i18n.t('searchPlaceholder') || '搜索FAQ...';
        }
    }

    // 显示搜索加载状态
    showSearchLoading() {
        const container = document.getElementById('searchResults');
        if (container) {
            container.innerHTML = `
                <div class="search-loading">
                    <div class="loading-spinner"></div>
                    <span>搜索中...</span>
                </div>
            `;
        }
    }

    // 显示搜索错误
    showSearchError(message) {
        const container = document.getElementById('searchResults');
        if (container) {
            container.innerHTML = `
                <div class="search-error">
                    <span>⚠️ ${message}</span>
                </div>
            `;
        }
    }

    // 清空搜索结果
    clearResults() {
        const container = document.getElementById('searchResults');
        if (container) {
            container.innerHTML = '';
        }
        this.lastQuery = '';
        this.currentResults = [];
    }

    // 获取系统状态 - 简化版本
    getSystemStatus() {
        return {
            cacheSize: this.searchCache.size,
            lastQuery: this.lastQuery,
            resultCount: this.currentResults.length,
            geminiEnabled: !!this.geminiAssistant,
            currentLanguage: this.currentLanguage,
            totalQuestions: this.dataManager.getAllQuestions().length
        };
    }
}

// 应用初始化
let app;

function initializeApp() {
    try {
        // 等待必需的依赖加载
        if (typeof I18nManager === 'undefined' || typeof DataManager === 'undefined') {
            console.log('⏳ 等待依赖加载...');
            setTimeout(initializeApp, 100);
            return;
        }

        app = new SimpleFAQApp();
        window.app = app; // 全局访问
        console.log('🎉 简化版FAQ应用启动成功');
        
        // 调试信息
        console.log('📊 系统状态:', app.getSystemStatus());
        
    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        document.body.innerHTML = `
            <div style="text-align: center; padding: 50px; color: #d32f2f;">
                <h2>应用启动失败</h2>
                <p>${error.message}</p>
                <button onclick="location.reload()">重新加载</button>
            </div>
        `;
    }
}

document.addEventListener('DOMContentLoaded', initializeApp);