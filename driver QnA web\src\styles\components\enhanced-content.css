/*
 * 文件路径: src/styles/components/enhanced-content.css
 * 文件描述: 统一的FAQ内容展示和列表样式管理组件
 * 功能: 
 *   - 为data.js中的问题内容提供精美的视觉展示效果
 *   - 统一管理FAQ内容区域的所有列表样式
 *   - 解决列表样式冲突问题，提供一致的用户体验
 * 依赖: 
 *   - 需要配合cards.css使用
 *   - 依赖tokens系统中的spacing、typography、colors等变量
 *   - 由index.css通过@import引入
 * 技术栈: CSS3, CSS Grid, Flexbox, CSS Counter, CSS逻辑属性
 * 相关文件: 
 *   - src/core/data.js (HTML内容定义)
 *   - src/core/app.js (渲染到.faq-body容器)
 *   - src/styles/index.css (主样式入口)
 * 核心功能:
 *   - 主题化增强内容盒子 (.info-box, .procedure-box等)
 *   - 统一的FAQ内容列表样式管理
 *   - 自动计数器重置和样式隔离
 *   - 响应式列表布局和现代CSS特性支持
 * 
 * 更新说明: 
 * - v2.0: 合并了来自index.css的列表样式，统一管理FAQ内容展示
 * - 解决了增强内容样式与FAQ卡片样式的冲突问题
 * - 为.faq-content容器内的增强内容提供了专门的样式优化
 * - 使用更微妙的背景色和边框，与外层玻璃拟态效果协调
 * - 调整了内边距和悬停效果，避免视觉冲突
 * - 添加了完整的counter-reset支持和现代CSS特性
 */

/* ===== 增强内容展示样式 ===== */

/* 主题化信息盒子 - 添加计数器重置 */
.info-box,
.procedure-box,
.warning-box,
.tip-box,
.success-box,
.error-box {
  padding: var(--space-lg);
  margin: var(--space-md) 0;
  border-radius: var(--radius-xl);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  counter-reset: step-counter; /* 重置有序列表计数器 */
}

/* ===== 统一的FAQ内容列表样式管理 ===== */

/* FAQ内容区域的通用列表样式 - 整合自index.css */
.faq-body ul:not(.info-box ul):not(.procedure-box ul):not(.warning-box ul):not(.tip-box ul):not(.success-box ul):not(.error-box ul),
.faq-body ol:not(.info-box ol):not(.procedure-box ol):not(.warning-box ol):not(.tip-box ol):not(.success-box ol):not(.error-box ol) {
  margin: var(--space-md) 0;
  padding-inline-start: var(--space-xl); /* 使用CSS逻辑属性 */
}

.faq-body li:not(.info-box li):not(.procedure-box li):not(.warning-box li):not(.tip-box li):not(.success-box li):not(.error-box li) {
  margin-bottom: var(--space-xs);
  line-height: var(--leading-relaxed);
}

/* 在FAQ详情页面中的增强内容盒子 - 优化样式，避免与外层卡片冲突 */
.faq-content .info-box,
.faq-content .procedure-box,
.faq-content .warning-box,
.faq-content .tip-box,
.faq-content .success-box,
.faq-content .error-box {
  /* 减少内边距，避免与外层卡片的内边距重叠 */
  padding: var(--space-md) var(--space-lg);
  /* 减少外边距，在FAQ内容中更紧凑 */
  margin: var(--space-sm) 0;
  /* 使用更微妙的背景，不与外层玻璃拟态效果冲突 */
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  /* 去掉悬停变换效果，避免在滚动内容中过于突兀 */
  transform: none;
  counter-reset: step-counter; /* 确保FAQ内容中的计数器也能正确重置 */
}

.faq-content .info-box:hover,
.faq-content .procedure-box:hover,
.faq-content .warning-box:hover,
.faq-content .tip-box:hover,
.faq-content .success-box:hover,
.faq-content .error-box:hover {
  /* 在FAQ内容中使用更微妙的悬停效果 */
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.05);
}

.info-box::before,
.procedure-box::before,
.warning-box::before,
.tip-box::before,
.success-box::before,
.error-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, transparent, currentColor, transparent);
  opacity: 0.6;
}

/* 信息盒子主题色 */
.info-box {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1), 
    rgba(147, 197, 253, 0.05)
  );
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: var(--primary-700);
}

/* FAQ内容中的信息盒子 - 使用更微妙的蓝色主题 */
.faq-content .info-box {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.04), 
    rgba(147, 197, 253, 0.02)
  );
  border: 1px solid rgba(59, 130, 246, 0.15);
  color: var(--primary-600);
}

.procedure-box {
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.1), 
    rgba(110, 231, 183, 0.05)
  );
  border: 1px solid rgba(16, 185, 129, 0.2);
  color: var(--success-700);
}

/* FAQ内容中的步骤盒子 - 使用更微妙的绿色主题 */
.faq-content .procedure-box {
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.04), 
    rgba(110, 231, 183, 0.02)
  );
  border: 1px solid rgba(16, 185, 129, 0.15);
  color: var(--success-600);
}

.warning-box {
  background: linear-gradient(135deg, 
    rgba(245, 158, 11, 0.1), 
    rgba(252, 211, 77, 0.05)
  );
  border: 1px solid rgba(245, 158, 11, 0.2);
  color: var(--warning-700);
}

/* FAQ内容中的警告盒子 - 使用更微妙的黄色主题 */
.faq-content .warning-box {
  background: linear-gradient(135deg, 
    rgba(245, 158, 11, 0.04), 
    rgba(252, 211, 77, 0.02)
  );
  border: 1px solid rgba(245, 158, 11, 0.15);
  color: var(--warning-600);
}

.tip-box {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.1), 
    rgba(196, 181, 253, 0.05)
  );
  border: 1px solid rgba(139, 92, 246, 0.2);
  color: var(--accent-700);
}

/* FAQ内容中的提示盒子 - 使用更微妙的紫色主题 */
.faq-content .tip-box {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.04), 
    rgba(196, 181, 253, 0.02)
  );
  border: 1px solid rgba(139, 92, 246, 0.15);
  color: var(--accent-600);
}

.success-box {
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.1), 
    rgba(134, 239, 172, 0.05)
  );
  border: 1px solid rgba(34, 197, 94, 0.2);
  color: var(--success-600);
}

/* FAQ内容中的成功盒子 - 使用更微妙的绿色主题 */
.faq-content .success-box {
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.04), 
    rgba(134, 239, 172, 0.02)
  );
  border: 1px solid rgba(34, 197, 94, 0.15);
  color: var(--success-500);
}

.error-box {
  background: linear-gradient(135deg, 
    rgba(239, 68, 68, 0.1), 
    rgba(252, 165, 165, 0.05)
  );
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: var(--error-600);
}

/* FAQ内容中的错误盒子 - 使用更微妙的红色主题 */
.faq-content .error-box {
  background: linear-gradient(135deg, 
    rgba(239, 68, 68, 0.04), 
    rgba(252, 165, 165, 0.02)
  );
  border: 1px solid rgba(239, 68, 68, 0.15);
  color: var(--error-500);
}

/* 悬停效果 */
.info-box:hover,
.procedure-box:hover,
.warning-box:hover,
.tip-box:hover,
.success-box:hover,
.error-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* FAQ内容中的悬停效果已在上面单独定义，更加微妙 */

/* 标题样式 */
.info-box h3,
.procedure-box h3,
.warning-box h3,
.tip-box h3,
.success-box h3,
.error-box h3 {
  font-size: var(--font-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.info-box h4,
.procedure-box h4,
.warning-box h4,
.tip-box h4,
.success-box h4,
.error-box h4 {
  font-size: var(--font-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-sm);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

/* ===== 增强内容盒子的专用列表样式 ===== */

/* 基础列表项样式 - 使用现代CSS布局 */
.info-box li,
.procedure-box li,
.warning-box li,
.tip-box li,
.success-box li,
.error-box li {
  margin-bottom: var(--space-xs);
  line-height: var(--leading-relaxed);
  display: flex;
  align-items: flex-start; /* 使用Flexbox改善对齐 */
}

/* 有序列表容器样式 - 确保计数器正确初始化 */
.info-box ol,
.procedure-box ol,
.warning-box ol,
.tip-box ol,
.success-box ol,
.error-box ol {
  counter-reset: step-counter;
  margin: var(--space-md) 0;
  padding-inline-start: 0; /* 移除默认缩进，使用自定义布局 */
}

/* 有序列表样式 - 使用现代CSS特性优化 */
.info-box ol li,
.procedure-box ol li,
.warning-box ol li,
.tip-box ol li,
.success-box ol li,
.error-box ol li {
  counter-increment: step-counter;
  position: relative;
  /* 为20px圆形计数器预留充足空间，避免与首字重叠 */
  padding-inline-start: calc(20px + var(--space-md));
  list-style: none;
  min-height: 24px; /* 确保与计数器圆形对齐 */
}

.info-box ol li::before,
.procedure-box ol li::before,
.warning-box ol li::before,
.tip-box ol li::before,
.success-box ol li::before,
.error-box ol li::before {
  content: counter(step-counter);
  position: absolute;
  inset-inline-start: 0; /* 使用CSS逻辑属性 */
  top: 0;
  background: currentColor;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xs);
  font-weight: var(--font-weight-bold);
  z-index: 1;
  flex-shrink: 0; /* 防止数字圆圈被压缩 */
}

/* 无序列表容器样式 */
.info-box ul,
.procedure-box ul,
.warning-box ul,
.tip-box ul,
.success-box ul,
.error-box ul {
  margin: var(--space-md) 0;
  padding-inline-start: 0; /* 移除默认缩进 */
}

/* 无序列表样式 - 现代化布局 */
.info-box ul li,
.procedure-box ul li,
.warning-box ul li,
.tip-box ul li,
.success-box ul li,
.error-box ul li {
  position: relative;
  /* 为0.5em几何圆点预留空间 + 文字安全间距 */
  padding-inline-start: clamp(1.25rem, 6vw, 1.75rem);
  list-style: none;
  line-height: var(--leading-relaxed);
}

.info-box ul li::before,
.procedure-box ul li::before,
.warning-box ul li::before,
.tip-box ul li::before,
.success-box ul li::before,
.error-box ul li::before {
  /* 使用几何圆点而非字体字符，避免不同平台字体导致大小/对齐不一致 */
  content: '';
  position: absolute;
  inset-inline-start: 0; /* 与padding配合，从内容起始处绘制 */
  top: 0.6em; /* 以首行中线为参考进行垂直居中 */
  transform: translateY(-50%);
  width: 0.5em;
  height: 0.5em;
  border-radius: 50%;
  background: currentColor;
  z-index: 1;
  pointer-events: none;
}

/* 强调文本 */
.info-box strong,
.procedure-box strong,
.warning-box strong,
.tip-box strong,
.success-box strong,
.error-box strong {
  font-weight: var(--font-weight-bold);
  color: currentColor;
}

/* 代码样式 */
.info-box code,
.procedure-box code,
.warning-box code,
.tip-box code,
.success-box code,
.error-box code {
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

/* 表格样式 */
.info-box table,
.procedure-box table,
.warning-box table,
.tip-box table,
.success-box table,
.error-box table {
  width: 100%;
  border-collapse: collapse;
  margin: var(--space-md) 0;
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* FAQ内容中的表格样式 - 使用更微妙的背景 */
.faq-content .info-box table,
.faq-content .procedure-box table,
.faq-content .warning-box table,
.faq-content .tip-box table,
.faq-content .success-box table,
.faq-content .error-box table {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-box th,
.procedure-box th,
.warning-box th,
.tip-box th,
.success-box th,
.error-box th,
.info-box td,
.procedure-box td,
.warning-box td,
.tip-box td,
.success-box td,
.error-box td {
  padding: var(--space-sm) var(--space-md);
  text-align: left;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.info-box th,
.procedure-box th,
.warning-box th,
.tip-box th,
.success-box th,
.error-box th {
  background: rgba(0, 0, 0, 0.05);
  font-weight: var(--font-weight-semibold);
}

/* FAQ内容中的表格单元格 - 使用更微妙的边框和背景 */
.faq-content .info-box th,
.faq-content .procedure-box th,
.faq-content .warning-box th,
.faq-content .tip-box th,
.faq-content .success-box th,
.faq-content .error-box th,
.faq-content .info-box td,
.faq-content .procedure-box td,
.faq-content .warning-box td,
.faq-content .tip-box td,
.faq-content .success-box td,
.faq-content .error-box td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.faq-content .info-box th,
.faq-content .procedure-box th,
.faq-content .warning-box th,
.faq-content .tip-box th,
.faq-content .success-box th,
.faq-content .error-box th {
  background: rgba(255, 255, 255, 0.08);
}

/* 响应式设计 - 使用现代CSS特性 */
@media (max-width: 768px) {
  .info-box,
  .procedure-box,
  .warning-box,
  .tip-box,
  .success-box,
  .error-box {
    padding: clamp(var(--space-sm), 4vw, var(--space-md)); /* 响应式内边距 */
    margin: var(--space-sm) 0;
  }
  
  .info-box h3,
  .procedure-box h3,
  .warning-box h3,
  .tip-box h3,
  .success-box h3,
  .error-box h3 {
    font-size: var(--font-lg);
  }
  
  .info-box h4,
  .procedure-box h4,
  .warning-box h4,
  .tip-box h4,
  .success-box h4,
  .error-box h4 {
    font-size: var(--font-base);
  }

  /* 移动端列表项优化 */
  .info-box ol li,
  .procedure-box ol li,
  .warning-box ol li,
  .tip-box ol li,
  .success-box ol li,
  .error-box ol li,
  .info-box ul li,
  .procedure-box ul li,
  .warning-box ul li,
  .tip-box ul li,
  .success-box ul li,
  .error-box ul li {
    padding-inline-start: clamp(var(--space-md), 6vw, var(--space-lg));
  }
}

@media (max-width: 480px) {
  .info-box,
  .procedure-box,
  .warning-box,
  .tip-box,
  .success-box,
  .error-box {
    padding: var(--space-sm);
  }

  /* 小屏幕下的计数器优化 */
  .info-box ol li::before,
  .procedure-box ol li::before,
  .warning-box ol li::before,
  .tip-box ol li::before,
  .success-box ol li::before,
  .error-box ol li::before {
    width: 18px;
    height: 18px;
    font-size: calc(var(--font-xs) * 0.9);
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-box,
.procedure-box,
.warning-box,
.tip-box,
.success-box,
.error-box {
  animation: fadeInUp 0.6s ease-out;
}

/* 图标增强 */
.info-box h3::before {
  content: '📋';
}

.procedure-box h3::before,
.procedure-box h4::before {
  content: '📋';
}

.warning-box h3::before,
.warning-box h4::before {
  content: '⚠️';
}

.tip-box h3::before,
.tip-box h4::before {
  content: '💡';
}

.success-box h3::before,
.success-box h4::before {
  content: '✅';
}

.error-box h3::before,
.error-box h4::before {
  content: '❌';
}

/* 无障碍支持和现代浏览器优化 */
@media (prefers-reduced-motion: reduce) {
  .info-box,
  .procedure-box,
  .warning-box,
  .tip-box,
  .success-box,
  .error-box {
    animation: none;
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .info-box,
  .procedure-box,
  .warning-box,
  .tip-box,
  .success-box,
  .error-box {
    border-width: 2px;
  }
}

/* 浏览器兼容性支持 - CSS逻辑属性回退 */
@supports not (padding-inline-start: 0) {
  .faq-body ul:not(.info-box ul):not(.procedure-box ul):not(.warning-box ul):not(.tip-box ul):not(.success-box ul):not(.error-box ul),
  .faq-body ol:not(.info-box ol):not(.procedure-box ol):not(.warning-box ol):not(.tip-box ol):not(.success-box ol):not(.error-box ol) {
    padding-left: var(--space-xl); /* 回退到传统属性 */
  }

  .info-box ol,
  .procedure-box ol,
  .warning-box ol,
  .tip-box ol,
  .success-box ol,
  .error-box ol,
  .info-box ul,
  .procedure-box ul,
  .warning-box ul,
  .tip-box ul,
  .success-box ul,
  .error-box ul {
    padding-left: 0;
  }

  .info-box ol li,
  .procedure-box ol li,
  .warning-box ol li,
  .tip-box ol li,
  .success-box ol li,
  .error-box ol li,
  .info-box ul li,
  .procedure-box ul li,
  .warning-box ul li,
  .tip-box ul li,
  .success-box ul li,
  .error-box ul li {
    padding-left: var(--space-lg);
  }

  .info-box ol li::before,
  .procedure-box ol li::before,
  .warning-box ol li::before,
  .tip-box ol li::before,
  .success-box ol li::before,
  .error-box ol li::before {
    left: 0;
  }

  .info-box ul li::before,
  .procedure-box ul li::before,
  .warning-box ul li::before,
  .tip-box ul li::before,
  .success-box ul li::before,
  .error-box ul li::before {
    left: var(--space-sm);
  }
}