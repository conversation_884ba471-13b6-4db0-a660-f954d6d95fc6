<!DOCTYPE html>
<html>
<head>
    <title>JavaScript Test</title>
</head>
<body>
    <h1>JavaScript Test Page</h1>
    <button onclick="testFunction()">Test JavaScript</button>
    <div id="result"></div>
    
    <script>
        console.log('Test script loaded successfully!');
        
        function testFunction() {
            console.log('Button clicked!');
            document.getElementById('result').innerHTML = 'JavaScript is working!';
            alert('JavaScript is working!');
        }
        
        // Test if our main script variables are accessible
        setTimeout(function() {
            console.log('Testing main script variables...');
            if (typeof translations !== 'undefined') {
                console.log('translations object found:', Object.keys(translations));
            } else {
                console.log('translations object not found');
            }
            
            if (typeof currentLanguage !== 'undefined') {
                console.log('currentLanguage found:', currentLanguage);
            } else {
                console.log('currentLanguage not found');
            }
        }, 1000);
    </script>
</body>
</html>