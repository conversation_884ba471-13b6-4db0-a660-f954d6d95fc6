<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title data-key="site-title">GoMyHire 平台规则</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo">
             <img src="gmh%20logo.png" 
                 alt="GoMyHire Logo" 
                 id="company-logo" 
                 data-alt-key="logo-alt"
                 width="90" height="90" 
                     decoding="async"
                     onload="console.log('[Logo] loaded', this.currentSrc, this.naturalWidth+'x'+this.naturalHeight)"
                     onerror="this.style.border='2px dashed red'; console.error('[Logo] load failed', this.currentSrc || this.src); this.setAttribute('data-alt-key','logo-alt-error'); this.alt='Logo 加载失败';">
                <h1 id="site-title" data-key="site-title">GoMyHire 平台规则</h1>
            </div>
            <div class="language-switcher">
                <button class="lang-btn active" id="zh-btn" type="button" data-key="lang-zh">中文</button>
                <button class="lang-btn" id="en-btn" type="button" data-key="lang-en">English</button>
                <button class="lang-btn" id="ms-btn" type="button" data-key="lang-ms">Bahasa</button>
                <button class="lang-btn" id="exam-entrance-btn" type="button" data-key="exam-entry-label" data-title-key="exam-entry-title">📝 考试入口</button>
                <button id="menu-toggle" type="button" aria-expanded="false" aria-controls="toc-list" class="lang-btn menu-btn" data-title-key="menu-title" data-key="menu-toggle">☰</button>
            <button id="dark-mode-toggle" type="button" class="lang-btn" aria-pressed="false" data-title-key="dark-mode-title" data-key="dark-mode-toggle">🌙</button>
                <button id="collapse-toggle" type="button" class="lang-btn desktop-only" data-state="expanded" data-key="toggle-collapse" data-title-key="collapse-toggle-title">−</button>
            </div>
        </div>
    </header>

    <nav class="table-of-contents" aria-expanded="false">
        <h2 id="toc-title" role="button" tabindex="0" aria-expanded="false" aria-controls="toc-list" data-key="toc-title">📋 目录</h2>
        <ul id="toc-list">
            <li><a href="#section1" id="toc1" data-key="toc1">沟通方式重要变更</a></li>
            <li><a href="#section2" id="toc2" data-key="toc2">工作流程指南</a></li>
            <li><a href="#section3" id="toc3" data-key="toc3">自动取消机制</a></li>
            <li><a href="#section4" id="toc4" data-key="toc4">超时费用政策</a></li>
            <li><a href="#section5" id="toc5" data-key="toc5">订单取消规则与积分系统</a></li>
            <li><a href="#section6" id="toc6" data-key="toc6">乘客失联处理流程</a></li>
            <li><a href="#section7" id="toc7" data-key="toc7">快速行动清单</a></li>
            <li><a href="#section8" id="toc8" data-key="toc8">应急联系与支持</a></li>
            <li><a href="#section9" id="toc9" data-key="toc9">凭证要求与标准</a></li>
            <li><a href="#section10" id="toc10" data-key="toc10">司机行为守则</a></li>
            <li><a href="#section11" id="toc11" data-key="toc11">奖惩机制</a></li>
            <li><a href="#section12" id="toc12" data-key="toc12">辅助工具使用指南</a></li>
            <li><a href="#section13" id="toc13" data-key="toc13">费用结算</a></li>
            <li><a href="#section14" id="toc14" data-key="toc14">GoMyHire提款项</a></li>
        </ul>
    </nav>

    <main class="content">
        <section id="section1" class="content-section">
            <h2 id="h1" data-key="h1">💬 工作期间沟通方式</h2>
            
            <h3 id="h1-1" data-key="h1-1">1.1 与GoMyHire团队沟通</h3>
            <ul id="comm-team">
                <li id="comm1" data-key="comm1">唯一渠道：平台内文字讯息功能</li>
                <li id="comm2" data-key="comm2">禁止电话：不再接受或拨打任何电话</li>
                <li id="comm3" data-key="comm3">响应时间：收到消息必须及时回复</li>
            </ul>

            <h3 id="h1-2" data-key="h1-2">1.2 与乘客沟通</h3>
            <ul id="comm-passenger">
                <li id="comm4" data-key="comm4">聊天限制：只有点击"Arrived"后才能使用订单聊天功能</li>
                <li id="comm5" data-key="comm5">沟通方式：仅限平台内文字讯息</li>
                <li id="comm6" data-key="comm6">禁止行为：不得主动添加乘客好友或私下联系</li>
                <li><img src="talk to cusotmer.png" alt="General Chat" id="general-chat-img"></li>
            </ul>

            <h3 id="h1-3" data-key="h1-3">1.3 重要沟通规则</h3>
            <div class="communication-rules-alert">
                <h4 id="comm-rules-title" data-key="comm-rules-title">🚨 紧急沟通规范</h4>
                <ul id="comm-rules-list">
                    <li id="comm-rule1" data-key="comm-rule1">
                        <span class="highlight-critical" data-key="extra-service-report">⚠️ 额外服务报备</span>：
                        任何涉及<span class="highlight-important" data-key="extra-service-items">额外服务、加价、变动</span>，
                        都必须<span class="highlight-must" data-key="must-report-immediately">立即报备</span>到手机app的 
                        <span class="highlight-channel" data-key="general-chat-support">General Chat / Contact Support</span>
                    </li>
                    <li id="comm-rule2" data-key="comm-rule2">
                        <span class="highlight-critical" data-key="emergency-contact-handling">📞 紧急联系处理</span>：
                        必须<span class="highlight-must" data-key="follow-support-instructions">遵守Support给的指示</span>，
                        并且注意手机app内的消息通知
                    </li>
                    <li id="comm-rule3" data-key="comm-rule3">
                        <span class="highlight-emergency" data-key="emergency-situation">🚨 紧急情况</span>：
                        如果Support联系<span class="highlight-emergency" data-key="three-calls">拨打三次通话</span>，
                        表示事态<span class="highlight-emergency" data-key="very-urgent">十分紧急</span>必须
                        <span class="highlight-must" data-key="immediate-response">立即接听/文字回复</span>
                    </li>
                    <li id="comm-rule4" data-key="comm-rule4">
                        <span class="highlight-must" data-key="message-monitoring">📱 消息监控</span>：
                        始终保持app消息通知开启，定期检查未读消息
                    </li>
                </ul>
            </div>
        </section>

        <section id="section2" class="content-section">
            <h2 id="h2" data-key="h2">📱 工作流程指南</h2>
            
            <h3 id="h2-1" data-key="h2-1">2.1 接单前准备</h3>
            <ul id="prep-list">
                <li id="prep1" data-key="prep1">检查订单详情：必须点击"View More"查看乘客信息、航班号、接送地址（集合地址与定位地址）</li>
                <li id="prep2" data-key="prep2">确认航班状态：使用FlightStats或飞常准APP确认航班信息</li>
                <li id="prep3" data-key="prep3"><span class="highlight-important" data-key="important-reminder">⚠️ 重要提醒</span>：跟着订单时间计算，如果查到航班时间跟订单时间差异过大（<span class="highlight-time" data-key="time-difference-limit">提前/延误超过一小时</span>），请<span class="highlight-must" data-key="report-to-support">立即上报客服备案并等待后续处理</span></li>
                <li id="prep4" data-key="prep4">如有疑问：通过"Contact Operator / General Chat"联系客服确认</li>
                <li><img src="contact pic.png" alt="General Chat" id="general-chat-img"></li>
            </ul>

            <h3 id="h2-2" data-key="h2-2">2.2 服务阶段操作</h3>
            <div class="workflow-box">
                <h4 id="workflow-title" data-key="workflow-title">服务阶段流程</h4>
                <ol id="workflow-steps">
                    <li id="step1" data-key="step1"><strong data-key="one-hour-before">订单前1小时</strong>：必须点击 "On The Way"，否则自动取消+封号（仅司机未接回时）</li>
                    <li id="step2" data-key="step2"><strong data-key="fifteen-minutes-before">订单前15分钟</strong>：必须点击 "Arrived"，否则自动取消+封号（仅司机未接回时）</li>
                    <li id="step3" data-key="step3"><strong data-key="confirm-passenger">确认乘客上车</strong>：核对详细行程 (Pick Up)</li>
                    <li id="step3-detail">
                        <div class="address-confirmation-alert">
                            <h5 id="address-alert-title" data-key="address-alert-title">⚠️ 重要：地址确认流程</h5>
                            <ul id="address-alert-list">
                                <li id="addr1" data-key="addr1"><span class="highlight-must" data-key="triple-confirm-address">三确认地址</span>：上车后必须跟客人在三确认酒店/目的地地址</li>
                                <li id="addr2" data-key="addr2"><span class="highlight-must" data-key="check-voucher">查看凭证</span>：必须让客人展示其酒店凭证，而不是单纯的听客人说</li>
                                <li id="addr3" data-key="addr3"><span class="highlight-important" data-key="chain-hotel-risk">连锁酒店风险</span>：客人可能不知道有酒店连锁命名高度相似的情况</li>
                                <li id="addr4" data-key="addr4"><span class="highlight-must" data-key="verify-correct">核对无误</span>：确认地址、房间号等信息完全匹配后再出发</li>
                            </ul>
                        </div>
                    </li>
                    <li id="step4" data-key="step4"><strong data-key="deliver-to-destination">送达指定地点</strong>：不得擅自更改 (Drop Off)</li>
                    <li id="step5" data-key="step5"><strong data-key="collect-payment-amount">按显示金额收费</strong> (Collect Payment)</li>
                </ol>
            </div>
        </section>

        <section id="section3" class="content-section">
            <h2 id="h3" data-key="h3">⏰ 自动取消机制</h2>
            
            <h3 id="h3-1" data-key="h3-1">3.1 生效时间</h3>
            <ul id="auto-cancel-time">
                <li id="auto1" data-key="auto1">开始日期：2025年3月10日中午12:00</li>
                <li id="auto2" data-key="auto2">适用范围：所有订单</li>
            </ul>

            <h3 id="h3-2" data-key="h3-2">3.2 触发条件</h3>
            <div class="auto-cancel-conditions compact-block">
        <h4 id="auto-title" data-key="auto-title-compact"></h4>
                <div class="info-table scrollable" data-aria-key="aria-auto-cancel-table">
                    <div class="it-head it-row">
            <div data-key="auto-col-time"></div>
            <div data-key="auto-col-action"></div>
            <div data-key="auto-col-result"></div>
                    </div>
                    <div class="it-row">
            <div><strong data-key="auto-t1-label"></strong></div>
            <div data-key="auto-t1-action-click"></div>
            <div><span class="badge ok" data-key="auto-result-continue"></span></div>
                    </div>
                    <div class="it-row">
            <div data-key="auto-t1-label"></div>
            <div data-key="auto-t1-action-miss"></div>
            <div><span class="badge danger" data-key="auto-result-fail-fine"></span></div>
                    </div>
                    <div class="it-row">
            <div><strong data-key="auto-t15-label"></strong></div>
            <div data-key="auto-t15-action-click"></div>
            <div><span class="badge ok" data-key="auto-result-continue"></span></div>
                    </div>
                    <div class="it-row">
            <div data-key="auto-t15-label"></div>
            <div data-key="auto-t15-action-miss"></div>
            <div><span class="badge danger" data-key="auto-result-fail-fine"></span></div>
                    </div>
                </div>
                <details class="raw-toggle"><summary data-key="raw-toggle-expand-details"></summary>
                    <div class="raw-content">
                        <div class="condition-group">
                            <h5 id="condition1-title" data-key="condition1-title">订单开始前1小时：</h5>
                            <ul id="condition1">
                                <li id="cond1a" data-key="cond1a">✅ 已点击"On The Way"：流程继续</li>
                                <li id="cond1b" data-key="cond1b">❌ 未点击"On The Way"：订单自动取消 + 封号 + 罚款 100%（根据次数上升）</li>
                            </ul>
                        </div>
                        <div class="condition-group">
                            <h5 id="condition2-title" data-key="condition2-title">订单开始前15分钟：</h5>
                            <ul id="condition2">
                                <li id="cond2a" data-key="cond2a">✅ 已点击"Arrived"：流程继续</li>
                                <li id="cond2b" data-key="cond2b">❌ 未点击"Arrived"：订单自动取消 + 封号 + 罚款 100%（根据次数上升）</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
            <h3 id="h3-3" data-key="h3-3">3.3 WhatsApp警告机制</h3>
            <ul id="whatsapp-warning">
                <li id="warning1" data-key="warning1">警告次数：自动取消前发送2次警告</li>
                <li id="warning2" data-key="warning2">扣分标准：每次警告扣0.1分</li>
                <li id="warning3" data-key="warning3">账号要求：后台注册手机号必须是主力WhatsApp账号</li>
            </ul>
        </section>

        <section id="section4" class="content-section">
            <h2 id="h4" data-key="h4">💰 超时费用政策</h2>
            
            <h3 id="h4-1" data-key="h4-1">4.1 免费等待时间</h3>
            <div class="waiting-time">
                <ul id="waiting-rules">
                    <li id="wait1" data-key="wait1"><strong>接机服务（吉隆坡，槟城，新山）</strong>：<span class="highlight-time">免费等待90分钟</span></li>
                    <li id="wait2" data-key="wait2"><strong>接机服务（新加坡/沙巴，斗湖）</strong>：<span class="highlight-time">免费等待60分钟</span></li>
                    <li id="wait3" data-key="wait3"><strong>送机服务</strong>：<span class="highlight-time">免费等待30分钟</span></li>
                    <li id="wait4" data-key="wait4"><strong>超时收费</strong>：<span class="highlight-charge">超时后每30分钟收费（因车型而异）</span></li>
                </ul>
                <div class="timeout-judgment-note">
                    <p data-key="timeout-judgment-note"><strong>⚠️ 超时判断方式：</strong></p>
                    <ul>
                        <li data-key="timeout-judgment1"><span class="highlight-important">系统自动计算</span>：从到达指定地点开始计时</li>
                        <li data-key="timeout-judgment2"><span class="highlight-important">免费等待时间结束</span>后即开始收费</li>
                        <li data-key="timeout-judgment3"><span class="highlight-important">每30分钟</span>为一个计费周期</li>
                    </ul>
                </div>
            </div>

            <h3 id="h4-2" data-key="h4-2">4.2 超时处理流程</h3>
            <div class="timeout-process">
                <h4 id="timeout-title" data-key="timeout-title">超时处理流程</h4>
                <ol id="timeout-steps">
                    <li id="timeout1" data-key="timeout1"><span class="highlight-step" data-key="free-wait-end">免费等待时间结束时</span>：联系客服，询问是否继续等待（点击app顶部的 contact support）</li>
                    <li id="timeout2" data-key="timeout2">等待客服确认（<span class="highlight-time" data-key="wait-support-confirm">10-15分钟</span>）</li>
                    <li id="timeout3" data-key="timeout3">根据客服回复情况处理：
                        <ul>
                            <li id="timeout3a" data-key="timeout3a">客服有回复：按客服指示操作</li>
                            <li id="timeout3b" data-key="timeout3b"><span class="highlight-important" data-key="no-reply-15min">15分钟内无回复</span>：视为乘客No-Show</li>
                        </ul>
                    </li>
                    <li id="timeout4" data-key="timeout4"><span class="highlight-must" data-key="must-photo-evidence">无论如何，必须拍照留证后方可离开</span></li>
                </ol>
            </div>
        </section>

        <section id="section5" class="content-section">
            <h2 id="h5" data-key="h5">⚠ 订单取消规则与积分系统</h2>
            
            <h3 id="h5-1" data-key="h5-1">5.1 取消时间扣分标准</h3>
            <div class="points-system">
                <h4 id="cancel-title" data-key="cancel-title">取消订单决策流程</h4>
                <ul id="cancel-rules">
                    <li id="cancel1" data-key="cancel1"><strong>超过6小时</strong>：扣1积分</li>
                    <li id="cancel2" data-key="cancel2"><strong>1-6小时内</strong>：扣2积分</li>
                    <li id="cancel3" data-key="cancel3"><strong>少于1小时</strong>：扣4积分 + 封号处理</li>
                </ul>
            </div>

            <h3 id="h5-2" data-key="h5-2">5.2 特殊情况处理</h3>
            <ul id="special-cases">
                <li id="special1" data-key="special1">航班延误：必须第一时间联系客服</li>
                <li id="special2" data-key="special2">航班提前：<span class="highlight-important" data-key="flight-early-1hour">如果航班提前超过1小时到达</span>，必须立即联系客服确认新的接机时间</li>
                <li id="special3" data-key="special3">凭证要求：必须提供航空公司的官方延误/提前证明截图</li>
                <li id="special4" data-key="special4">免扣分条件：飞机延误超过1小时、医生证明、汽车维修收据 可寻找客服核实凭证有效后可免扣分</li>
                <li id="special5" data-key="special5">禁止自行取消：未联系客服并获得许可前，自行取消订单将照常扣分</li>
            </ul>
        </section>

        <section id="section6" class="content-section">
            <h2 id="h6" data-key="h6">👥 乘客失联处理流程</h2>
            
            <h3 id="h6-1" data-key="h6-1">6.1 等待时间计算</h3>
            <ul id="wait-calculation">
                <li id="calc1" data-key="calc1">送机服务：30分钟免费等待</li>
                <li id="calc2" data-key="calc2">接机服务（吉隆坡，槟城，新山）：90分钟免费等待</li>
                <li id="calc3" data-key="calc3">接机服务（新加坡/沙巴，斗湖）：60分钟免费等待</li>
            </ul>

            <h3 id="h6-2" data-key="h6-2">6.2 处理步骤</h3>
            <div class="passenger-lost-process">
                <h4 id="lost-title" data-key="lost-title">乘客失联处理流程</h4>
                <ol id="lost-steps">
                    <li id="lost1" data-key="lost1"><strong>免费等待时间结束前15分钟</strong>：联系客服，报告情况并查询</li>
                    <li id="lost2" data-key="lost2"><strong>免费等待时间结束</strong>（接机90分钟/新加坡沙巴60分钟/送机30分钟）：再次联系客服，确认是否可离开</li>
                    <li id="lost3" data-key="lost3"><strong>等待客服确认</strong>（10-15分钟）：
                        <ul>
                            <li id="lost3a" data-key="lost3a">✅ 客服有回复：按客服指示操作</li>
                            <li id="lost3b" data-key="lost3b">❌ 15分钟内无回复：视为乘客No-Show</li>
                        </ul>
                    </li>
                    <li id="lost4" data-key="lost4"><strong>最后步骤</strong>：拍照留证后方可离开</li>
                </ol>
            </div>
        </section>

        <section id="section7" class="content-section">
            <h2 id="h7" data-key="h7">⚡ 快速行动清单</h2>
            
            <h3 id="h7-1" data-key="h7-1">7.1 每日必做</h3>
            <ul id="daily-checklist" class="checklist">
                <li id="daily1" data-key="daily1">检查今日订单并确认航班状态</li>
                <li id="daily2" data-key="daily2">确保WhatsApp账号正常接收消息</li>
                <li id="daily3" data-key="daily3"><span class="highlight-must" data-key="check-app-notifications">检查app消息通知</span>：确保General Chat消息能正常接收</li>
                <li id="daily4" data-key="daily4">提前1小时点击"On the Way"</li>
                <li id="daily5" data-key="daily5">提前15分钟点击"Arrived"</li>
                <li id="daily6" data-key="daily6">保持车辆整洁、着装规范</li>
            </ul>

            <h3 id="h7-2" data-key="h7-2">7.2 紧急情况处理</h3>
            <ul id="emergency-checklist" class="checklist">
                <li id="emergency1" data-key="emergency1">航班延误→立即联系客服+提供凭证</li>
                <li id="emergency2" data-key="emergency2">乘客失联→按流程拍照留证+联系客服</li>
                <li id="emergency3" data-key="emergency3">需要取消→先联系客服再操作</li>
            </ul>

            <h3 id="h7-3" data-key="h7-3">7.3 服务前检查表</h3>
            <ul id="pre-service-checklist" class="checklist">
                <li id="pre1" data-key="pre1">确认订单时间和乘客信息</li>
                <li id="pre2" data-key="pre2">检查车辆整洁度和着装规范</li>
                <li id="pre3" data-key="pre3">验证WhatsApp和GPS功能正常</li>
                <li id="pre4" data-key="pre4">提前准备好GPS Map Camera应用</li>
            </ul>

            <h3 id="h7-4" data-key="h7-4">7.4 服务后确认</h3>
            <ul id="post-service-checklist" class="checklist">
                <li id="post1" data-key="post1">确认乘客已安全送达</li>
                <li id="post2" data-key="post2">确认送达地址与订单完全一致</li>
                <li id="post3" data-key="post3">验证客人已确认到达正确地点</li>
                <li id="post4" data-key="post4">按系统显示金额收费</li>
                <li id="post5" data-key="post5">检查订单状态是否完成</li>
                <li id="post6" data-key="post6">如有问题立即联系客服</li>
            </ul>
        </section>

        <section id="section8" class="content-section">
            <h2 id="h8" data-key="h8">📞 应急联系与支持</h2>
            
            <h3 id="h8-1" data-key="h8-1">8.1 严重事件处理流程</h3>
            <div class="emergency-process">
                <p id="emergency-intro" data-key="emergency-intro">在遇到交通事故、乘客/司机严重冲突、重大服务投诉或任何涉及安全的违规行为时，请遵循以下步骤：</p>
                <ol id="emergency-steps">
                    <li id="em1" data-key="em1">确保安全：保持冷静，优先确保人身安全</li>
                    <li id="em2" data-key="em2">立即汇报：第一时间通过平台内文字讯息联系客服</li>
                    <li id="em3" data-key="em3">收集信息：在安全的情况下收集相关信息</li>
                    <li id="em4" data-key="em4">保持沟通：切勿擅自向外界发布任何信息</li>
                </ol>
            </div>

            <h3 id="h8-2" data-key="h8-2">8.2 联系方式</h3>
            <div class="contact-info">
                <ul id="contact-list">
                    <li id="contact1" data-key="contact1">📱 WhatsApp: +60162234711</li>
                    <li id="contact2" data-key="contact2">📧 邮箱: <EMAIL></li>
                    <li id="contact3" data-key="contact3">💬 App客服: App内入口</li>
                </ul>
                <div class="emergency-contact-note">
                    <h5 id="emergency-contact-title" data-key="emergency-contact-title">� 紧急联系提醒</h5>
                    <ul id="emergency-contact-list">
                        <li id="emerg1" data-key="emerg1"><span class="highlight-emergency" data-key="priority-use">优先使用</span>：App内General Chat为主要联系渠道</li>
                        <li id="emerg2" data-key="emerg2"><span class="highlight-must" data-key="emergency-situation">紧急情况</span>：Support拨打三次电话表示紧急，必须立即回复</li>
                        <li id="emerg3" data-key="emerg3"><span class="highlight-must" data-key="message-monitoring">消息监控</span>：保持app通知开启，定期检查消息</li>
                    </ul>
                </div>
            </div>

            <h3 id="h8-3" data-key="h8-3">8.3 申诉流程</h3>
            <div class="appeal-flow">
                <ol id="appeal-flow-steps">
                    <li id="af1" data-key="af1">收到处罚通知</li>
                    <li id="af2" data-key="af2">3天内提交申诉及证据</li>
                    <li id="af3" data-key="af3">团队复议 (7天内)</li>
                    <li id="af4" data-key="af4">通过消息通知结果</li>
                </ol>
            </div>
        </section>

        <section id="section9" class="content-section">
            <h2 id="h9" data-key="h9">📸 凭证要求与标准</h2>
            
            <h3 id="h9-1" data-key="h9-1">9.1 乘客失联凭证</h3>
            <div class="evidence-requirements compact-block">
                <h4 id="evidence-title" data-key="evidence-title-compact"></h4>
                <div class="info-table" data-aria-key="aria-evidence-composition">
                    <div class="it-head it-row">
                        <div data-key="evidence-col-type"></div>
                        <div data-key="evidence-col-include"></div>
                    </div>
                    <div class="it-row"><div data-key="evidence-row-gps"></div><div data-key="evidence-row-gps-detail"></div></div>
                    <div class="it-row"><div data-key="evidence-row-map"></div><div data-key="evidence-row-map-detail"></div></div>
                    <div class="it-row"><div data-key="evidence-row-call"></div><div data-key="evidence-row-call-detail"></div></div>
                </div>
                <details class="raw-toggle"><summary data-key="raw-toggle-expand-details"></summary>
                    <div class="evidence-list raw-content">
                        <div class="evidence-item">
                            <h5 id="ev1-title" data-key="ev1-title">📸 GPS照片：</h5>
                            <ul id="ev1">
                                <li id="ev1a" data-key="ev1a">GPS坐标</li>
                                <li id="ev1b" data-key="ev1b">车辆与车牌</li>
                            </ul>
                        </div>
                        <div class="evidence-item">
                            <h5 id="ev2-title" data-key="ev2-title">🗺 地图截图：</h5>
                            <ul id="ev2">
                                <li id="ev2a" data-key="ev2a">实时位置</li>
                                <li id="ev2b" data-key="ev2b">Google Map</li>
                            </ul>
                        </div>
                        <div class="evidence-item">
                            <h5 id="ev3-title" data-key="ev3-title">📞 通话记录：</h5>
                            <ul id="ev3">
                                <li id="ev3a" data-key="ev3a">3次以上</li>
                                <li id="ev3b" data-key="ev3b">截图</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>

            <h3 id="h9-2" data-key="h9-2">9.2 凭证完成要求</h3>
            <div class="evidence-completion">
                <h4 id="completion-title" data-key="completion-title">凭证完成要求</h4>
                <ul id="completion-req">
                    <li id="comp1" data-key="comp1"><strong>📍 必须在场完成</strong>：未完成不得离开</li>
                    <li id="comp2" data-key="comp2"><strong>⚖ 投诉后果</strong>：惩罚 + 封号处理</li>
                </ul>
            </div>
        </section>

        <section id="section10" class="content-section">
            <h2 id="h10" data-key="h10">� 司机行为守则</h2>
            
            <h3 id="h10-1" data-key="h10-1">10.1 禁止行为</h3>
            <ul id="prohibited-behavior">
                <li id="prohib1" data-key="prohib1">收费规定：不得主动向客人收取任何费用</li>
                <li id="prohib2" data-key="prohib2">用餐规范：不得未经许可与客人同桌用餐</li>
                <li id="prohib3" data-key="prohib3">联系方式：不得主动添加客人好友或私下联系</li>
                <li id="prohib4" data-key="prohib4">车辆要求：服务车辆必须与注册信息完全一致</li>
                <li id="prohib5" data-key="prohib5">订单处理：严禁私自转派订单、恶意抢单、威胁客人</li>
            </ul>

            <h3 id="h10-2" data-key="h10-2">10.2 服务标准</h3>
            <div class="service-standards compact-block">
                <h4 id="standards-title" data-key="standards-title-compact"></h4>
                <div class="badge-groups" data-aria-key="aria-standards-groups">
                    <div class="badge-block"><span class="badge label" data-key="standards-cat-vehicle"></span><span class="badge pill" data-key="standards-vehicle-clean"></span><span class="badge pill" data-key="standards-vehicle-trunk"></span></div>
                    <div class="badge-block"><span class="badge label" data-key="standards-cat-attire"></span><span class="badge pill" data-key="standards-attire-shirt"></span><span class="badge pill" data-key="standards-attire-pants"></span><span class="badge pill" data-key="standards-attire-shoes"></span></div>
                    <div class="badge-block"><span class="badge label" data-key="standards-cat-behavior"></span><span class="badge pill" data-key="standards-behavior-fare"></span></div>
                </div>
                <details class="raw-toggle"><summary data-key="raw-toggle-expand-details"></summary>
                    <div class="standards-grid raw-content">
                        <div class="standard-item">
                            <h5 id="std1-title" data-key="std1-title">🚗 车辆：</h5>
                            <ul id="std1">
                                <li id="std1a" data-key="std1a">内外洁净</li>
                                <li id="std1b" data-key="std1b">后备箱空置</li>
                            </ul>
                        </div>
                        <div class="standard-item">
                            <h5 id="std2-title" data-key="std2-title">👕 着装：</h5>
                            <ul id="std2">
                                <li id="std2a" data-key="std2a">有领T恤</li>
                                <li id="std2b" data-key="std2b">长裤、包鞋</li>
                            </ul>
                        </div>
                        <div class="standard-item">
                            <h5 id="std3-title" data-key="std3-title">🤫 行为：</h5>
                            <ul id="std3">
                                <li id="std3a" data-key="std3a">不得展示车费</li>
                                <li id="std3b" data-key="std3b"><span class="highlight-must" data-key="address-confirmation">地址确认</span>：上车后必须三确认目的地地址</li>
                                <li id="std3c" data-key="std3c"><span class="highlight-must" data-key="check-voucher">查看凭证</span>：要求客人出示酒店凭证确认地址</li>
                                <li id="std3d" data-key="std3d"><span class="highlight-important" data-key="chain-hotel-warning">注意连锁酒店</span>：警惕相似命名的连锁酒店</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <section id="section11" class="content-section">
            <h2 id="h11" data-key="h11">🏆 奖惩机制</h2>
            
            <div class="reward-punishment compact-block">
                <h3 id="h11-overview" data-key="h11-overview">奖惩机制概览（速览）</h3>
                <div class="info-table" data-aria-key="aria-reward-overview">
                    <div class="it-head it-row"><div data-key="overview-col-project"></div><div data-key="overview-col-desc"></div><div data-key="overview-col-impact"></div></div>
                    <div class="it-row"><div data-key="overview-row-base"></div><div data-key="overview-row-base-desc"></div><div data-key="overview-row-base-impact"></div></div>
                    <div class="it-row"><div data-key="overview-row-good"></div><div data-key="overview-row-good-desc"></div><div data-key="overview-row-good-impact"></div></div>
                    <div class="it-row"><div data-key="overview-row-penalty"></div><div data-key="overview-row-penalty-desc"></div><div data-key="overview-row-penalty-impact"></div></div>
                    <div class="it-row"><div data-key="overview-row-progressive"></div><div data-key="overview-row-progressive-desc"></div><div data-key="overview-row-progressive-impact"></div></div>
                </div>
                <details class="raw-toggle"><summary data-key="raw-toggle-expand-details"></summary>
                    <div class="mechanism-grid raw-content">
                        <div class="reward-section">
                            <h4 id="reward-title" data-key="reward-title">⭐ 奖励机制</h4>
                            <ul id="rewards">
                                <li id="reward1" data-key="reward1">🔄 每周日 00.00 重置 10分</li>
                                <li id="reward2" data-key="reward2">👍 好评+奖金</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>

            <h3 id="h11-1" data-key="h11-1">11.1 严重违规行为罚款机制</h3>
            <div class="violation-penalties compact-block">
                <h4 id="late-penalty-title" data-key="late-penalty-title-compact"></h4>
                <div class="info-table responsive" data-aria-key="aria-late-penalty-table">
                    <div class="it-head it-row">
                        <div data-key="late-col-times"></div><div data-key="late-col-fine"></div><div data-key="late-col-ban"></div><div data-key="late-col-extra"></div>
                    </div>
                    <div class="it-row"><div data-key="late-first"></div><div>30%</div><div data-key="late-ban1"></div><div data-key="late-extra1"></div></div>
                    <div class="it-row"><div data-key="late-second"></div><div>50%</div><div data-key="late-ban2"></div><div data-key="late-extra2"></div></div>
                    <div class="it-row"><div data-key="late-third"></div><div>100%</div><div data-key="late-ban3"></div><div data-key="late-extra3"></div></div>
                </div>
                <details class="raw-toggle"><summary data-key="raw-toggle-expand-details"></summary>
                    <div class="penalty-levels raw-content">
                        <div class="penalty-level">
                            <h5 id="first-late-title" data-key="first-late-title">第一次迟到</h5>
                            <ul id="first-late">
                                <li id="fl1" data-key="fl1">💰 罚款：订单金额的30% +封号七天</li>
                                <li id="fl2" data-key="fl2">⚠️ 警告处理</li>
                            </ul>
                        </div>
                        <div class="penalty-level">
                            <h5 id="second-late-title" data-key="second-late-title">第二次迟到</h5>
                            <ul id="second-late">
                                <li id="sl1" data-key="sl1">💰 罚款：订单金额的50%</li>
                                <li id="sl2" data-key="sl2">🔒 封号：15天</li>
                                <li id="sl3" data-key="sl3">📝 需进行重新培训</li>
                            </ul>
                        </div>
                        <div class="penalty-level">
                            <h5 id="third-late-title" data-key="third-late-title">第三次迟到</h5>
                            <ul id="third-late">
                                <li id="tl1" data-key="tl1">💰 罚款：订单金额的100%</li>
                                <li id="tl2" data-key="tl2">🔒 封号：30天</li>
                                <li id="tl3" data-key="tl3">👥 需进行重新培训</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>

            <div class="serious-violations">
                <h4 id="serious-violations-title" data-key="serious-violations-title">严重违规行为定义</h4>
                <ul id="serious-violations-list">
                    <li id="serious1" data-key="serious1">到场无车：司机点击"Arrived"但实际未到达指定地点</li>
                    <li id="serious2" data-key="serious2">迟到导致没用车：因司机迟到造成乘客无法使用服务</li>
                    <li id="serious3" data-key="serious3">未到时间自行离开：在免费等待时间内擅自离开</li>
                    <li id="serious4" data-key="serious4">客人严重投诉：服务态度恶劣、车辆不符、拒载等行为</li>
                </ul>
            </div>

            <div class="penalty-standards">
                <h4 id="penalty-standards-title" data-key="penalty-standards-title">罚款标准（递进式处罚）—紧凑表</h4>
                <div class="info-table responsive" data-aria-key="aria-progressive-penalty-table">
                    <div class="it-head it-row"><div data-key="prog-col-times"></div><div data-key="prog-col-fine"></div><div data-key="prog-col-ban"></div><div data-key="prog-col-extra"></div></div>
                    <div class="it-row"><div data-key="prog-first"></div><div data-key="prog-fine1"></div><div data-key="prog-ban1"></div><div data-key="prog-extra1"></div></div>
                    <div class="it-row"><div data-key="prog-second"></div><div data-key="prog-fine2"></div><div data-key="prog-ban2"></div><div data-key="prog-extra2"></div></div>
                    <div class="it-row"><div data-key="prog-third"></div><div data-key="prog-fine3"></div><div data-key="prog-ban3"></div><div data-key="prog-extra3"></div></div>
                </div>
                <details class="raw-toggle"><summary data-key="raw-toggle-expand-details"></summary>
                    <div class="penalty-progression raw-content">
                        <div class="penalty-step">
                            <h5 id="first-violation-title" data-key="first-violation-title">第一次违规</h5>
                            <ul id="first-violation">
                                <li id="fv1" data-key="fv1">💰 经济处罚：退一赔二</li>
                                <li id="fv2" data-key="fv2">🔒 封号处罚：封号7天</li>
                            </ul>
                        </div>
                        <div class="penalty-step">
                            <h5 id="second-violation-title" data-key="second-violation-title">第二次违规</h5>
                            <ul id="second-violation">
                                <li id="sv1" data-key="sv1">💰 经济处罚：退一赔二</li>
                                <li id="sv2" data-key="sv2">🔒 封号处罚：封号15天</li>
                            </ul>
                        </div>
                        <div class="penalty-step">
                            <h5 id="third-violation-title" data-key="third-violation-title">第三次违规</h5>
                            <ul id="third-violation">
                                <li id="tv1" data-key="tv1">💰 经济处罚：退一赔三</li>
                                <li id="tv2" data-key="tv2">🔒 封号处罚：封号30天</li>
                                <li id="tv3" data-key="tv3">📋 额外要求：需要面试解封</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>

            <h3 id="h11-2" data-key="h11-2">11.2 一般违规行为</h3>
            <ul id="general-violations">
                <li id="gv1" data-key="gv1">🚗 无车到场：封号+赔偿，逐次加重</li>
                    <li id="gv2" data-key="gv2">📉 积分过低：封号3天，取消后续订单</li>
            </ul>

            <div class="appeal-process">
                <h4 id="appeal-title" data-key="appeal-title">申诉与复议</h4>
                <ul id="appeal-list">
                    <li id="appeal1" data-key="appeal1">所有罚款处罚均有3天申诉期</li>
                    <li id="appeal2" data-key="appeal2">提供充分证据可减轻或撤销处罚</li>
                    <li id="appeal3" data-key="appeal3">最终解释权归GoMyHire所有</li>
                </ul>
            </div>
        </section>

        <section id="section12" class="content-section">
            <h2 id="h12" data-key="h12">📱 辅助工具使用指南</h2>
            
            <h3 id="h12-1" data-key="h12-1">12.1 航班查询工具</h3>
            <div class="flight-tools compact-block">
                <h4 id="flightstats-title" data-key="flightstats-title">航班工具速览</h4>
                <div class="info-table" data-aria-key="aria-flight-tools-compare">
                    <div class="it-head it-row">
                        <div data-key="flight-table-tool">工具</div><div data-key="flight-table-input">输入字段 / 步骤</div><div data-key="flight-table-purpose">用途</div>
                    </div>
                    <div class="it-row"><div data-key="flight-tool-flightstats">FlightStats</div><div data-key="flight-input-flightstats">Airline · FlightNumber · FlightDate</div><div data-key="flight-purpose-flightstats">官方航班状态确认</div></div>
                    <div class="it-row"><div data-key="flight-tool-flightapp">飞常准</div><div data-key="flight-input-flightapp">查航班 → 输入航班号 → 选日期</div><div data-key="flight-purpose-flightapp">快速实时动态</div></div>
                    <div class="it-row"><div data-key="flight-tool-google">Google</div><div data-key="flight-input-google">搜索"flight [航班号] [日期]"</div><div data-key="flight-purpose-google">通用查询工具</div></div>
                </div>
                <details class="raw-toggle"><summary data-key="raw-toggle-expand-details"></summary>
                    <div class="raw-content">
                        <div class="tool-section">
                            <h4 id="flightstats-title-raw" data-key="flightstats-title">✈ FlightStats</h4>
                            <ul id="flightstats">
                                <li id="fs1" data-key="fs1">Airline: 航班字母</li>
                                <li id="fs2" data-key="fs2">FlightNumber: 航班数字</li>
                                <li id="fs3" data-key="fs3">FlightDate: 订单日期</li>
                            </ul>
                        </div>
                        <div class="tool-section">
                            <h4 id="flightapp-title" data-key="flightapp-title">✈ 飞常准</h4>
                            <ul id="flightapp">
                                <li id="fa1" data-key="fa1">打开查航班</li>
                                <li id="fa2" data-key="fa2">输入航班号</li>
                                <li id="fa3" data-key="fa3">选择订单日期</li>
                            </ul>
                        </div>
                        <div class="tool-section">
                            <h4 id="google-title" data-key="google-title">🔍 Google</h4>
                            <ul id="google">
                                <li id="g1" data-key="g1">在Google搜索框输入</li>
                                <li id="g2" data-key="g2">"flight [航班号] [日期]"</li>
                                <li id="g3" data-key="g3">例如：flight MH370 2025-09-14</li>
                            </ul>
                        </div>
                    </div>
                </details>
                
                <div class="flight-time-alert">
                    <h5 id="flight-alert-title" data-key="flight-alert-title">⚠️ 重要提醒：航班时间差异处理</h5>
                    <ul id="flight-alert-list">
                        <li id="alert1" data-key="alert1"><span class="highlight-important">实时监控</span>：使用工具查询时，请注意实际航班时间与订单时间的差异</li>
                        <li id="alert2" data-key="alert2"><span class="highlight-time">时间阈值</span>：如果差异超过<span class="highlight-time">1小时</span>（提前或延误），必须立即上报</li>
                        <li id="alert3" data-key="alert3"><span class="highlight-must">立即行动</span>：发现异常立即联系客服，不要自行判断或处理</li>
                        <li id="alert4" data-key="alert4"><span class="highlight-must">保留记录</span>：截图保存查询结果，作为后续处理的凭证</li>
                    </ul>
                </div>
            </div>

            <h3 id="h12-2" data-key="h12-2">12.2 GPS Map Camera使用指南</h3>
            <div class="gps-camera-guide">
                <h4 id="gps-guide-title" data-key="gps-guide-title">GPS Map Camera使用指南</h4>
                <ol id="gps-steps">
                    <li id="gps1" data-key="gps1">📱 打开应用程序</li>
                    <li id="gps2" data-key="gps2">📸 拍摄当前位置</li>
                    <li id="gps3" data-key="gps3">✅ 确保照片包含必要信息</li>
                    <li id="gps4" data-key="gps4">💾 保存并上传照片</li>
                </ol>
                
                <div class="gps-requirements">
                    <h5 id="gps-permissions-title" data-key="gps-permissions-title">权限设置</h5>
                    <p id="gps-permissions" data-key="gps-permissions">必须开启GPS和相机权限</p>
                    
                    <h5 id="gps-scenarios-title" data-key="gps-scenarios-title">使用场景</h5>
                    <ul id="gps-scenarios">
                        <li id="scenario1" data-key="scenario1">到达凭证</li>
                        <li id="scenario2" data-key="scenario2">乘客失联凭证</li>
                    </ul>
                    
                    <h5 id="photo-requirements-title" data-key="photo-requirements-title">拍照要求</h5>
                    <ul id="photo-requirements">
                        <li id="photo1" data-key="photo1">GPS坐标</li>
                        <li id="photo2" data-key="photo2">车辆</li>
                        <li id="photo3" data-key="photo3">车牌</li>
                        <li id="photo4" data-key="photo4">地点</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="section13" class="content-section">
            <h2 id="h13" data-key="h13">💰 费用结算</h2>
            
            <ul id="fee-settlement">
                <li id="fee1" data-key="fee1">服务费用包含过路费/停车费，请勿向乘客额外收取任何费用。</li>
                <li id="fee2" data-key="fee2">任何关于车费的疑问立即跟客服报备，严禁与客人直接沟通收费</li>
                <li id="fee3" data-key="fee3">收入将在完成订单后的12小时结算至您的电子钱包，请留意。</li>
            </ul>
        </section>

        <section id="section14" class="content-section">
            <h2 id="h14" data-key="h14">🏦 GoMyHire提款项</h2>
            
            <h3 id="h14-1" data-key="h14-1">14.1 完成的工作</h3>
            <p id="withdrawal1" data-key="withdrawal1">点击进入可以查询完毕订单</p>

            <h3 id="h14-2" data-key="h14-2">14.2 取钱</h3>
            <p id="withdrawal2" data-key="withdrawal2">点击进入填写数额完毕后点击取钱发送请求</p>

            <h3 id="h14-3" data-key="h14-3">14.3 查看提款记录</h3>
            <p id="withdrawal3" data-key="withdrawal3">点击进入可查询款项进展 进行中 & 汇款成功 款项会在三天工作日前到账 周六、周日以及公共假期不能计算在里，如有遇到款项问题可询问Gomyhire Driver +6011155882117</p>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <p id="footer-note" data-key="footer-note">重要提醒：本指南内容会定期更新，请务必定期查看最新版本。如有疑问，请通过WhatsApp: +60162234711 联系客服。</p>
            <p id="footer-company" data-key="footer-company">GoMyHire管理团队</p>
            <p id="footer-date" data-key="footer-date">最后更新：2025年9月14日</p>
        </div>
    </footer>

    <!-- Back to top button -->
    <button id="back-to-top" data-aria-key="aria-back-to-top" title="TOP" data-key="back-to-top">↑</button>

    <!-- Reading progress bar -->
    <div id="reading-progress" aria-hidden="true"></div>

    <!-- Command palette / quick search -->
    <div id="command-palette" role="dialog" aria-modal="true" aria-labelledby="cp-title" hidden>
        <div class="cp-panel">
            <div class="cp-header">
                <h2 id="cp-title" data-key="cp-title">快速跳转 (Ctrl+K / Esc)</h2>
                <button type="button" id="cp-close" data-aria-key="aria-close" title="关闭" data-key="cp-close">✕</button>
            </div>
            <input type="text" id="cp-input" placeholder="输入关键词筛选章节..." autocomplete="off" data-key="cp-input-placeholder" />
            <ul id="cp-results" data-aria-key="aria-chapter-list"></ul>
            <div class="cp-hint" data-key="cp-hint">↑↓ 选择 • Enter 跳转 • Esc 关闭</div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>