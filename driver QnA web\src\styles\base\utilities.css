/*
 * 文件路径: src/styles/base/utilities.css
 * 文件描述: 提供了一套全面的实用工具类，用于快速UI开发。这些类旨在直接在HTML中应用单用途样式（例如，显示属性、间距、文本对齐、颜色、边框圆角、阴影、z-index、过渡），从而推广一种“实用优先”（utility-first）的CSS方法。它利用CSS自定义属性（设计令牌）来确保值的一致性。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于在以下文件中定义的CSS变量（设计令牌）：
 *     - `src/styles/tokens/spacing.css` (用于 `m-*`, `p-*` 类和移动端内边距)。
 *     - `src/styles/tokens/typography.css` (用于 `text-*` 字体大小和字重)。
 *     - `src/styles/tokens/colors.css` (用于 `text-*` 和 `bg-*` 颜色)。
 *     - `src/styles/tokens/radius.css` (用于 `rounded-*` 类)。
 *     - `src/styles/tokens/shadows.css` (用于 `shadow-*` 类)。
 *     - `src/styles/tokens/transitions.css` (用于 `transition-*` 类)。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - **实用优先开发**: 允许开发者通过直接在HTML中组合样式来快速构建UI，减少为每个组件编写自定义CSS的需求。
 *   - **一致性**: 确保所有应用的样式都遵循预定义的设计令牌（颜色、间距、排版等），从而保持视觉一致性。
 *   - **可维护性**: 对设计令牌的更改会自动传播到所有实用工具类，简化全局样式更新。
 *   - **可读性**: 类名通常是自描述的，使HTML在布局和样式方面更具可读性和可理解性。
 * 关键部分（工具类类别）:
 *   - **显示/可见性**: `hidden`, `block`, `inline`, `inline-block`, `flex`, `inline-flex`, `grid`。
 *   - **Flexbox**: `flex-row`, `flex-col`, `flex-wrap`, `flex-nowrap`, `items-*` (align-items), `justify-*` (justify-content), `flex-*` (flex 简写)。
 *   - **定位**: `relative`, `absolute`, `fixed`, `sticky`。
 *   - **间距**: `m-*` (外边距), `p-*` (内边距)，使用 `var(--space-*)` 令牌。
 *   - **文本**: `text-left`, `text-center`, `text-right` (对齐)；`text-xs` 到 `text-3xl` (字体大小，使用 `var(--font-*)`)；`font-normal` 到 `font-bold` (字重)。
 *   - **颜色**: `text-*` (文本颜色，使用 `var(--text-*)`, `var(--primary-600)` 等)；`bg-*` (背景颜色，使用 `var(--background-*)`, `var(--surface-*)`, `var(--primary-600)` 等)。
 *   - **边框圆角**: `rounded-*`，使用 `var(--radius-*)` 令牌。
 *   - **阴影**: `shadow-*`，使用 `var(--shadow-*)` 令牌。
 *   - **Z-index**: `z-*`，用于堆叠上下文。
 *   - **过渡**: `transition-*`，使用 `var(--transition-*)` 令牌。
 *   - **移动端专用工具类 (`@media (max-width: 767px)`)**: 提供以 `mobile:` 为前缀的响应式工具类（例如，`mobile:hidden`, `mobile:text-sm`），允许样式仅在移动屏幕上应用。
 * 使用约定:
 *   - 这些类直接应用于HTML元素（例如，`<div class="flex flex-col items-center p-md">`）。
 */
/* 基础样式 - 工具类 */

/* 显示/隐藏工具类 */
.hidden { display: none !important; }
.block { display: block !important; }
.inline { display: inline !important; }
.inline-block { display: inline-block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }

/* Flexbox工具类 */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

/* 位置工具类 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 间距工具类 */
.m-0 { margin: 0; }
.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }

.p-0 { padding: 0; }
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

/* 文本工具类 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-xs { font-size: var(--font-xs); }
.text-sm { font-size: var(--font-sm); }
.text-base { font-size: var(--font-base); }
.text-lg { font-size: var(--font-lg); }
.text-xl { font-size: var(--font-xl); }
.text-2xl { font-size: var(--font-2xl); }
.text-3xl { font-size: var(--font-3xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 颜色工具类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-inverse { color: var(--text-inverse); }
.text-brand { color: var(--primary-600); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

/* 背景工具类 */
.bg-primary { background-color: var(--background-color); }
.bg-secondary { background-color: var(--background-secondary); }
.bg-surface { background-color: var(--surface-color); }
.bg-brand { background-color: var(--primary-600); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }

/* 圆角工具类 */
.rounded-none { border-radius: 0; }
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 阴影工具类 */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-none { box-shadow: none; }

/* Z-index工具类 */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* 过渡工具类 */
.transition-none { transition: none; }
.transition-all { transition: var(--transition-all); }
.transition-colors { transition: var(--transition-colors); }
.transition-opacity { transition: var(--transition-opacity); }
.transition-transform { transition: var(--transition-transform); }

/* 移动端专用工具类 */
@media (max-width: 767px) {
  .mobile\:hidden { display: none !important; }
  .mobile\:block { display: block !important; }
  .mobile\:flex { display: flex !important; }
  .mobile\:text-sm { font-size: var(--font-sm); }
  .mobile\:text-base { font-size: var(--font-base); }
  .mobile\:p-sm { padding: var(--space-sm); }
  .mobile\:p-md { padding: var(--space-md); }
}