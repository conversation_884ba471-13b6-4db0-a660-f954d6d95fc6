/*
 * 文件路径: src/styles/tokens/spacing.css
 * 文件描述: 定义了应用程序的间距系统，通过CSS自定义属性（变量）提供了一致的布局尺寸。它遵循“T恤尺寸”的命名规范，并包含了移动端UI元素的特定尺寸以及安全区域插边。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 * 功能:
 *   - 集中管理所有间距定义，确保应用程序布局的视觉一致性。
 *   - 通过提供可伸缩和语义化的方式来管理元素之间的距离，简化响应式设计。
 *   - 定义了针对移动优先设计的特定尺寸，包括头部、底部导航和触摸目标的大小。
 *   - 利用CSS的 `env()` 函数来处理设备安全区域插边，这对于带有刘海屏或圆角的现代移动设备至关重要，可防止内容被硬件特性遮挡。
 * 关键部分:
 *   - `--space-xs` 到 `--space-6xl`: 定义了从特小到特大的“T恤尺寸”间距刻度，用于通用间距。
 *   - `--header-height`, `--bottom-nav-height`, `--fab-size`, `--emergency-size`, `--touch-target`: 移动UI组件的特定尺寸，确保交互元素尺寸的一致性。
 *   - `--safe-top`, `--safe-bottom`, `--safe-left`, `--safe-right`: 使用 `env(safe-area-inset-*)` 来适应设备特定的安全区域，防止内容被刘海或圆角遮挡。
 *   - `--container-padding`, `--section-padding`: 定义了内容容器和区块的标准内边距值。
 * 使用约定:
 *   - 其他CSS文件应通过 `var()` 函数引用这些间距变量（例如：`margin-top: var(--space-md);`），而不是直接使用硬编码的像素或rem值。
 */
/* 设计令牌 - 间距系统 */
:root {
  /* T-shirt尺寸间距系统 */
  --space-xs: 4px;    /* 0.25rem */
  --space-sm: 8px;    /* 0.5rem */
  --space-md: 12px;   /* 0.75rem */
  --space-lg: 16px;   /* 1rem */
  --space-xl: 20px;   /* 1.25rem */
  --space-2xl: 24px;  /* 1.5rem */
  --space-3xl: 32px;  /* 2rem */
  --space-4xl: 40px;  /* 2.5rem */
  --space-5xl: 48px;  /* 3rem */
  --space-6xl: 64px;  /* 4rem */

  /* 手机端专用尺寸 */
  --header-height: 100px;
  --bottom-nav-height: 60px;
  --fab-size: 56px;
  --emergency-size: 48px;
  --touch-target: 48px;
  
  /* 安全区域 */
  --safe-top: env(safe-area-inset-top);
  --safe-bottom: env(safe-area-inset-bottom);
  --safe-left: env(safe-area-inset-left);
  --safe-right: env(safe-area-inset-right);

  /* 容器边距 */
  --container-padding: var(--space-lg);
  --section-padding: var(--space-2xl);
}