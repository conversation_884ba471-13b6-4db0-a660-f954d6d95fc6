/*
 * 文件路径: src/styles/themes/light.css
 * 文件描述: 定义了应用程序“亮色”主题的特定颜色值和样式。它通过覆盖或设置语义化颜色变量（在 `src/styles/tokens/colors.css` 和 `src/styles/themes/variables.css` 中定义）来创建一种明亮的视觉美感。
 * 依赖关系:
 *   - 被 `src/styles/index.css` 文件通过 `@import` 规则导入。
 *   - 强烈依赖于 `src/styles/tokens/colors.css` 中定义的颜色变量（例如：`var(--neutral-900)`, `var(--neutral-0)`）以及 `src/styles/themes/variables.css` 中定义的变量（例如：`var(--success-light)`）。
 * 初始化时机:
 *   - 在 `src/styles/index.css` 被浏览器解析时加载并应用。
 *   - 通常通过在 `<html>` 或 `<body>` 元素上设置 `data-theme="light"` 属性或添加 `light-theme` 类来激活。
 * 功能:
 *   - 为应用程序提供一个独特的亮色视觉主题。
 *   - 将抽象的语义颜色变量（如 `--text-primary`, `--background-color`）映射到适合浅色背景的具体颜色值。
 *   - 定义亮色主题下状态指示器、输入字段和卡片的特定背景颜色和边框样式。
 * 关键部分:
 *   - `:root, [data-theme="light"]`: 这个选择器确保这些变量默认全局应用，并且可以通过在 `<html>` 或 `<body>` 元素上设置 `data-theme="light"` 来显式激活。
 *   - **语义颜色覆盖**: 将 `--text-*`, `--background-*`, `--surface-*`, `--border-*`, `--shadow-color`, `--overlay-color` 设置为适合亮色主题的值，通常使用较浅的中性色作为背景，较深的中性色作为文本。
 *   - **玻璃拟态效果**: 重新定义玻璃拟态的 `rgba` 值，通常背景和边框的透明度较低，以适应较亮的视觉效果。
 *   - **状态颜色背景**: 定义成功、警告、危险和信息状态的浅色背景。
 *   - **组件特定样式**: 设置输入字段和卡片的背景和边框颜色，确保它们与亮色主题良好融合。
 * 使用约定:
 *   - 此文件通常作为主题切换机制的一部分（例如，`app.js` 切换 `<body>` 或 `<html>` 上的类或 `data-theme` 属性）进行条件加载或应用。
 */
/* 主题系统 - 亮色主题 */

/* 默认亮色主题 */
:root,
[data-theme="light"] {
  /* 语义颜色覆盖 */
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-tertiary: var(--neutral-400);
  --text-inverse: var(--neutral-0);
  
  --background-color: var(--neutral-0);
  --background-secondary: var(--neutral-50);
  --surface-color: var(--neutral-0);
  --surface-elevated: var(--neutral-0);
  
  --border-color: var(--neutral-200);
  --border-light: var(--neutral-100);
  
  --shadow-color: rgba(0, 0, 0, 0.1);
  --overlay-color: rgba(0, 0, 0, 0.5);

  /* 玻璃拟态效果 - 亮色 */
  --glass-background: rgba(255, 255, 255, 0.1);
  --glass-background-intense: rgba(255, 255, 255, 0.15);
  --glass-background-subtle: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-border-intense: rgba(255, 255, 255, 0.3);
  --glass-border-subtle: rgba(255, 255, 255, 0.1);

  /* 状态颜色背景 - 亮色 */
  --success-background: var(--success-light);
  --warning-background: var(--warning-light);
  --danger-background: var(--danger-light);
  --info-background: var(--info-light);

  /* 输入框样式 - 亮色 */
  --input-background: var(--surface-color);
  --input-border: var(--border-color);
  --input-focus-border: var(--primary-500);
  --input-placeholder: var(--text-tertiary);

  /* 卡片样式 - 亮色 */
  --card-background: var(--surface-color);
  --card-border: var(--border-light);
  --card-hover-background: var(--background-secondary);
}